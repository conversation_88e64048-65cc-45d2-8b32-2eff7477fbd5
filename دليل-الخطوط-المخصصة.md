# دليل الخطوط المخصصة 🎨

## 📁 الملفات المُنشأة:

### 1. **ملف الخطوط الرئيسي:**
- `public/fonts/fonts.css` - ملف تعريف الخطوط المخصصة
- `public/fonts/README.md` - دليل استخدام مجلد الخطوط

### 2. **ملفات الإعداد:**
- `src/config/customFonts.js` - إعدادات الخطوط لـ Tailwind
- `tailwind.config.js` - تم تحديثه لإضافة الخطوط المخصصة
- `src/app/layout.tsx` - تم ربط ملف الخطوط

### 3. **ملفات العرض:**
- `src/components/FontExample.tsx` - مكون عرض الخطوط
- `src/app/fonts-demo/page.tsx` - صفحة عرض الخطوط

---

## 🚀 كيفية إضافة خط جديد:

### الخطوة 1: تحضير ملفات الخط
```
1. احصل على ملفات الخط بالتنسيقات التالية:
   - .woff2 (الأفضل - حجم صغير)
   - .woff (احتياطي)
   - .ttf (احتياطي إضافي)

2. ضع الملفات في مجلد: public/fonts/
```

### الخطوة 2: تعريف الخط في fonts.css
افتح ملف `public/fonts/fonts.css` وأضف:

```css
@font-face {
  font-family: 'اسم الخط';
  src: url('./اسم-الملف.woff2') format('woff2'),
       url('./اسم-الملف.woff') format('woff'),
       url('./اسم-الملف.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

/* للخط العريض */
@font-face {
  font-family: 'اسم الخط';
  src: url('./اسم-الملف-Bold.woff2') format('woff2');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}
```

### الخطوة 3: إضافة الخط في Tailwind
افتح ملف `tailwind.config.js` وأضف في قسم `fontFamily`:

```javascript
fontFamily: {
  // ... الخطوط الموجودة
  'اسم-الخط-الجديد': ['اسم الخط', 'خط احتياطي', 'sans-serif'],
}
```

### الخطوة 4: استخدام الخط
```jsx
<h1 className="font-اسم-الخط-الجديد">النص بالخط الجديد</h1>
```

---

## 📋 الخطوط المُعدة مسبقاً:

### الخطوط العربية:
- `font-amiri` - خط أميري التقليدي
- `font-cairo` - خط القاهرة الحديث
- `font-tajawal` - خط تجوال العصري
- `font-almarai` - خط المرعي البسيط
- `font-custom-arabic` - خط عربي مخصص

### الخطوط الإنجليزية:
- `font-inter` - خط Inter الحديث
- `font-roboto` - خط Roboto الكلاسيكي
- `font-poppins` - خط Poppins الأنيق
- `font-custom-english` - خط إنجليزي مخصص

### خطوط العناوين:
- `font-main-heading` - للعناوين الرئيسية
- `font-sub-heading` - للعناوين الفرعية
- `font-custom-heading` - عناوين مخصصة

### خطوط النصوص:
- `font-main-text` - للنصوص الرئيسية
- `font-small-text` - للنصوص الصغيرة
- `font-custom-body` - نصوص مخصصة

### خطوط خاصة:
- `font-logo` - للشعار
- `font-numbers` - للأرقام
- `font-code` - للأكواد

---

## 🎯 أمثلة عملية:

### مثال 1: إضافة خط "دبي" العربي
```css
/* في public/fonts/fonts.css */
@font-face {
  font-family: 'Dubai';
  src: url('./Dubai-Regular.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}
```

```javascript
// في tailwind.config.js
fontFamily: {
  'dubai': ['Dubai', 'Cairo', 'sans-serif'],
}
```

```jsx
// في المكون
<h1 className="font-dubai text-2xl">مرحباً بكم</h1>
```

### مثال 2: إضافة خط "Montserrat" الإنجليزي
```css
/* في public/fonts/fonts.css */
@font-face {
  font-family: 'Montserrat';
  src: url('./Montserrat-Regular.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}
```

```javascript
// في tailwind.config.js
fontFamily: {
  'montserrat': ['Montserrat', 'Inter', 'sans-serif'],
}
```

```jsx
// في المكون
<h1 className="font-montserrat text-2xl">Welcome</h1>
```

---

## 🔧 نصائح مهمة:

### 1. تحسين الأداء:
- استخدم WOFF2 دائماً (أصغر حجماً)
- أضف `font-display: swap` لتحسين التحميل
- ضغط ملفات الخطوط قبل الرفع

### 2. التوافق:
- اختبر الخطوط على متصفحات مختلفة
- ضع خطوط احتياطية دائماً
- تأكد من دعم الخط للغة العربية

### 3. التنظيم:
- استخدم أسماء واضحة ومنطقية
- نظم الخطوط حسب الاستخدام
- احتفظ بنسخة احتياطية

---

## 🧪 اختبار الخطوط:

### عرض جميع الخطوط:
زر الرابط: `http://localhost:3000/fonts-demo`

### اختبار خط معين:
```jsx
<div className="font-اسم-الخط">
  <h1>عنوان تجريبي</h1>
  <p>نص تجريبي للاختبار</p>
  <div>123,456 ريال</div>
</div>
```

---

## 🎨 خطوط عربية مُوصى بها:

### للعناوين:
- **أميري (Amiri)** - تقليدي وأنيق
- **القاهرة (Cairo)** - حديث وواضح
- **تجوال (Tajawal)** - عصري ومميز

### للنصوص:
- **المرعي (Almarai)** - بسيط وواضح
- **نوتو (Noto Sans Arabic)** - متوازن
- **IBM Plex Arabic** - احترافي

### للشعارات:
- **كوفام (Kufam)** - مميز
- **لاليزار (Lalezar)** - جذاب
- **ريم كوفي (Reem Kufi)** - عصري

---

## 📞 للمساعدة:

### مشاكل شائعة:
1. **الخط لا يظهر:** تحقق من مسار الملف
2. **الخط يظهر متأخراً:** أضف `font-display: swap`
3. **الخط لا يدعم العربية:** استخدم خط عربي مناسب

### مصادر الخطوط:
- Google Fonts (مجاني)
- Adobe Fonts (مدفوع)
- Font Squirrel (مجاني)
- MyFonts (مدفوع)

---

## ✅ الخلاصة:

تم إنشاء نظام شامل لإدارة الخطوط المخصصة يتيح لك:

1. ✅ **إضافة خطوط جديدة** بسهولة
2. ✅ **استخدام الخطوط** في Tailwind
3. ✅ **تنظيم الخطوط** حسب الاستخدام
4. ✅ **اختبار الخطوط** قبل الاستخدام
5. ✅ **تحسين الأداء** والتوافق

الآن يمكنك إضافة أي خط تريده واستخدامه في موقعك بسهولة! 🎉
