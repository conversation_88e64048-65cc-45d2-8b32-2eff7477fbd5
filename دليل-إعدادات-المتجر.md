# دليل إعدادات المتجر

## 🎯 كيفية الوصول لإعدادات المتجر

### الخطوات:
1. **ادخل لوحة التحكم**: `http://localhost:3000/admin`
2. **أدخل بيانات الدخول**:
   - اسم المستخدم: `admin`
   - كلمة المرور: `admin77111`
3. **اختر تبويب "الإعدادات"**
4. **ابدأ في تحرير إعدادات متجرك**

---

## 📋 أقسام الإعدادات

### 1. 🏪 المعلومات العامة
- **اسم المتجر**: الاسم الذي سيظهر في رأس الموقع
- **وصف المتجر**: الوصف الذي سيظهر في الصفحة الرئيسية
- **شعار المتجر**: رابط صورة الشعار (اختياري)

### 2. 📞 معلومات الاتصال
- **رقم الهاتف**: رقم هاتف المتجر
- **البريد الإلكتروني**: البريد الإلكتروني للتواصل
- **العنوان**: العنوان الفعلي للمتجر
- **وسائل التواصل الاجتماعي**:
  - فيسبوك
  - إنستغرام
  - واتساب
  - تويتر

### 3. 💳 طرق الدفع
- **التحويل البنكي**:
  - تفعيل/إلغاء التحويل البنكي
  - اسم البنك
  - رقم الحساب
  - اسم صاحب الحساب
- **الدفع عند الاستلام**:
  - تفعيل/إلغاء الدفع عند الاستلام

### 4. 🚚 إعدادات الشحن
- **تكلفة الشحن**: التكلفة الأساسية للشحن
- **الحد الأدنى للشحن المجاني**: المبلغ المطلوب للحصول على شحن مجاني
- **مناطق التوصيل**: قائمة بالمدن والمناطق المتاحة للتوصيل

---

## ✅ كيفية تحرير الإعدادات

### خطوات التحرير:
1. **اختر القسم** من القائمة الجانبية
2. **عدّل البيانات** في النماذج
3. **اضغط "حفظ التغييرات"**
4. **ستظهر رسالة تأكيد** عند نجاح الحفظ

### نصائح مهمة:
- ✅ **الحقول المطلوبة**: مميزة بعلامة (*)
- ✅ **الحفظ التلقائي**: التغييرات تُحفظ تلقائياً في المتصفح
- ✅ **التأثير الفوري**: التغييرات تظهر في الموقع فوراً
- ✅ **التحقق من البيانات**: النظام يتحقق من صحة البيانات قبل الحفظ

---

## 🔧 إعدادات متقدمة

### تخصيص مناطق التوصيل:
1. اذهب إلى قسم "إعدادات الشحن"
2. في قسم "مناطق التوصيل":
   - **لإضافة منطقة**: اضغط "+ إضافة منطقة جديدة"
   - **لحذف منطقة**: اضغط "حذف" بجانب المنطقة
   - **لتعديل منطقة**: عدّل النص مباشرة

### إعداد التحويل البنكي:
1. اذهب إلى قسم "طرق الدفع"
2. فعّل "التحويل البنكي"
3. أدخل:
   - اسم البنك الخاص بك
   - رقم حسابك البنكي
   - اسمك كما يظهر في البنك

### ربط وسائل التواصل:
1. اذهب إلى قسم "معلومات الاتصال"
2. في قسم "وسائل التواصل الاجتماعي":
   - أدخل الروابط الكاملة لصفحاتك
   - أدخل رقم الواتساب مع رمز الدولة

---

## 📊 معاينة الإعدادات

### أين تظهر التغييرات:
- **اسم المتجر**: في رأس الموقع وعنوان المتصفح
- **معلومات الاتصال**: في صفحة "اتصل بنا" والتذييل
- **طرق الدفع**: في صفحة إتمام الطلب
- **إعدادات الشحن**: في حساب تكلفة الشحن

### التحقق من التغييرات:
1. احفظ الإعدادات
2. اذهب إلى الموقع الرئيسي
3. تحقق من ظهور التغييرات
4. اختبر عملية الشراء للتأكد من عمل الإعدادات

---

## ⚠️ تنبيهات مهمة

### الأمان:
- 🔒 **كلمة المرور**: غيّر كلمة المرور الافتراضية في الإنتاج
- 🔒 **معلومات البنك**: تأكد من صحة معلومات الحساب البنكي
- 🔒 **النسخ الاحتياطي**: احفظ نسخة من إعداداتك

### الأداء:
- ⚡ **الصور**: استخدم صور مضغوطة للشعار
- ⚡ **الروابط**: تأكد من صحة روابط وسائل التواصل
- ⚡ **البيانات**: لا تترك حقول فارغة في الحقول المطلوبة

### الاختبار:
- 🧪 **اختبر الطلبات**: تأكد من عمل عملية الشراء
- 🧪 **اختبر التواصل**: تأكد من عمل روابط التواصل
- 🧪 **اختبر الشحن**: تأكد من صحة حساب تكلفة الشحن

---

## 🆘 حل المشاكل الشائعة

### المشكلة: لا تظهر التغييرات
**الحل**:
1. تأكد من الضغط على "حفظ التغييرات"
2. أعد تحميل الصفحة (F5)
3. امسح ذاكرة التخزين المؤقت للمتصفح

### المشكلة: رسالة خطأ عند الحفظ
**الحل**:
1. تأكد من ملء جميع الحقول المطلوبة (*)
2. تأكد من صحة تنسيق البريد الإلكتروني
3. تأكد من صحة تنسيق أرقام الهواتف

### المشكلة: لا تعمل روابط وسائل التواصل
**الحل**:
1. تأكد من إدخال الرابط الكامل (مع http:// أو https://)
2. تأكد من صحة الرابط بفتحه في متصفح منفصل
3. تأكد من عدم وجود مسافات إضافية

---

## 📞 الدعم

إذا واجهت أي مشكلة:
1. راجع هذا الدليل أولاً
2. تأكد من اتباع الخطوات بالترتيب
3. تحقق من رسائل الخطأ في المتصفح (F12)
4. جرب إعادة تشغيل الخادم: `npm run dev`

---

**ملاحظة**: جميع الإعدادات تُحفظ محلياً في المتصفح. في الإنتاج، يُنصح بربط النظام بقاعدة بيانات حقيقية.
