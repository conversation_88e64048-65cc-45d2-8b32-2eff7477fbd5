# دليل الخطوط في تبويب التصميم والمظهر 🎨

## 🎯 التحديث الجديد:

تم **دمج إدارة الخطوط** في تبويب **"التصميم والمظهر"** بدلاً من تبويب منفصل، مما يوفر تجربة أكثر تنظيماً وسهولة.

---

## 🚀 كيفية الوصول للخطوط:

### الخطوات:
1. **ادخل لوحة التحكم**: `http://localhost:3000/admin`
2. **سجل دخول**:
   - اسم المستخدم: `admin`
   - كلمة المرور: `admin77111`
3. **اختر تبويب "التصميم والمظهر"**
4. **اختر قسم "الخطوط والنصوص"**
5. **ابدأ في تخصيص خطوط موقعك**

---

## 📋 الأقسام المتاحة:

### 1. 🔤 التحكم المفصل في الخطوط
#### **أنواع النصوص:**
- **العناوين الرئيسية (Headings)**: للعناوين الكبيرة (h1)
- **العناوين الفرعية (Subheadings)**: للعناوين المتوسطة (h2)
- **الفقرات (Paragraphs)**: للنصوص العادية والمحتوى
- **الملاحظات (Notes)**: للنصوص الصغيرة والتفاصيل

#### **إعدادات كل نوع:**
- **نوع الخط**: اختيار من قائمة شاملة تشمل الخطوط المخصصة
- **حجم الخط**: تحديد الحجم بوحدة rem أو px
- **وزن الخط**: من Light إلى Bold
- **ارتفاع السطر**: للتحكم في المسافة بين الأسطر
- **تباعد الأحرف**: للتحكم في المسافة بين الأحرف

#### **معاينة فورية:**
- عرض مباشر لكل تغيير
- نص تجريبي لكل نوع من النصوص
- تطبيق فوري للإعدادات

### 2. 📁 الخطوط المخصصة من المجلد
#### **عرض الخطوط المتاحة:**
- **قراءة تلقائية** من مجلد `public/fonts/`
- **معلومات تفصيلية** عن كل خط:
  - اسم الملف
  - حجم الملف (بالكيلوبايت)
  - نوع الملف (WOFF2, WOFF, TTF, إلخ)
- **معاينة فورية** لكل خط
- **تصنيف تلقائي** (مخصص)

#### **إدارة الخطوط:**
- **إعادة تحميل**: زر لتحديث قائمة الخطوط
- **تعليمات واضحة** لإضافة خطوط جديدة
- **دمج تلقائي** في قائمة اختيار الخطوط

---

## 🎨 قائمة الخطوط الشاملة:

### الخطوط العربية:
- **Cairo** - خط عربي حديث وواضح
- **Tajawal** - خط أنيق وعصري
- **Amiri** - خط كلاسيكي تقليدي
- **Almarai** - خط بسيط وواضح
- **IBM Plex Arabic** - خط احترافي
- **Noto Sans Arabic** - خط Google متوازن
- **Markazi Text** - مناسب للعناوين
- **Reem Kufi** - خط عصري ومميز
- **Scheherazade** - خط تقليدي أنيق
- **Lateef** - خط نسخ كلاسيكي

### الخطوط الإنجليزية:
- **Inter** - حديث ونظيف
- **Roboto** - Google الشهير
- **Open Sans** - سهل القراءة
- **Lato** - أنيق ومرن
- **Montserrat** - مناسب للعناوين
- **Poppins** - عصري وجذاب
- **Source Sans Pro** - خط Adobe
- **Nunito** - ودود ومريح

### خطوط النظام:
- **خط النظام الافتراضي**
- **خط Apple النظام**
- **خط Windows النظام**

### الخطوط المخصصة:
- **جميع الخطوط** الموجودة في `public/fonts/`
- **تحديث تلقائي** عند إضافة خطوط جديدة
- **معاينة فورية** لكل خط مخصص

---

## 🛠️ كيفية إضافة خط جديد:

### الطريقة السهلة:
1. **ضع ملف الخط** في مجلد `public/fonts/`
2. **أضف تعريف الخط** في `public/fonts/fonts.css`:
```css
@font-face {
  font-family: 'اسم الخط';
  src: url('./اسم-الملف.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}
```
3. **اذهب لتبويب "التصميم والمظهر"**
4. **اضغط "إعادة تحميل"** في قسم الخطوط المخصصة
5. **اختر الخط الجديد** من قائمة "نوع الخط"

### مثال عملي:
```
1. ضع ملف Dubai-Regular.woff2 في public/fonts/
2. أضف في fonts.css:
   @font-face {
     font-family: 'Dubai';
     src: url('./Dubai-Regular.woff2') format('woff2');
     font-weight: 400;
     font-style: normal;
     font-display: swap;
   }
3. اضغط "إعادة تحميل" في لوحة التحكم
4. اختر "Dubai - خط مخصص" من القائمة
```

---

## 🎯 الميزات الجديدة:

### 1. **دمج ذكي:**
- الخطوط المخصصة تظهر في نفس قائمة الخطوط العادية
- تصنيف واضح لكل نوع من الخطوط
- معاينة فورية لجميع الخطوط

### 2. **إدارة سهلة:**
- زر إعادة تحميل للخطوط الجديدة
- معلومات تفصيلية عن كل خط
- تعليمات واضحة للاستخدام

### 3. **تجربة محسنة:**
- واجهة موحدة في مكان واحد
- تنظيم أفضل للإعدادات
- سهولة في التنقل والاستخدام

---

## 📝 أمثلة للاستخدام:

### تخصيص متجر عربي:
```
العناوين الرئيسية: Cairo (قوي وواضح)
العناوين الفرعية: Tajawal (أنيق)
الفقرات: Almarai (سهل القراءة)
الملاحظات: Noto Sans Arabic (صغير وواضح)
```

### تخصيص متجر عصري:
```
العناوين الرئيسية: Montserrat (إنجليزي للعناوين)
العناوين الفرعية: Cairo (عربي حديث)
الفقرات: Inter (نظيف ومقروء)
الملاحظات: Open Sans (صغير وواضح)
```

### استخدام خط مخصص:
```
1. أضف خط "دبي" المخصص
2. استخدمه للعناوين الرئيسية
3. احتفظ بـ Cairo للنصوص العادية
4. استخدم Roboto للأرقام والأسعار
```

---

## ⚠️ نصائح مهمة:

### 1. **اختيار الخطوط:**
- استخدم خطوط عربية للنصوص العربية
- تأكد من وضوح الخط على جميع الأحجام
- اختبر الخط على أجهزة مختلفة
- تجنب استخدام أكثر من 3 خطوط مختلفة

### 2. **الأداء:**
- فضل تنسيق WOFF2 (أصغر حجماً)
- تجنب الخطوط الكبيرة الحجم
- استخدم `font-display: swap` دائماً
- احذف الخطوط غير المستخدمة

### 3. **التوافق:**
- اختبر على متصفحات مختلفة
- تأكد من دعم الخط للأحرف العربية
- استخدم خطوط احتياطية
- تحقق من ترخيص الخط

---

## 🔍 استكشاف الأخطاء:

### المشكلة: الخط الجديد لا يظهر
**الحلول:**
1. تأكد من وضع الملف في `public/fonts/`
2. تحقق من تعريف الخط في `fonts.css`
3. اضغط "إعادة تحميل" في لوحة التحكم
4. امسح cache المتصفح

### المشكلة: الخط لا يعمل في الموقع
**الحلول:**
1. تحقق من اختيار الخط في الإعدادات
2. احفظ الإعدادات بعد التغيير
3. أعد تحميل الصفحة الرئيسية
4. تحقق من console للأخطاء

---

## ✅ الخلاصة:

تم دمج إدارة الخطوط في تبويب **"التصميم والمظهر"** مما يوفر:

1. ✅ **واجهة موحدة** لجميع إعدادات التصميم
2. ✅ **استعراض الخطوط المخصصة** من المجلد
3. ✅ **دمج ذكي** في قوائم الاختيار
4. ✅ **معاينة فورية** لجميع التغييرات
5. ✅ **إدارة سهلة** للخطوط الجديدة
6. ✅ **تنظيم أفضل** للإعدادات

الآن يمكنك إدارة جميع خطوط موقعك من مكان واحد بسهولة وفعالية! 🎨✨
