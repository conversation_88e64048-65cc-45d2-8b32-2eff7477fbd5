<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تخصيص الخريطة - مكتبة أنوار دارس</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 1200px;
            margin: 0 auto;
        }
        
        h1 {
            color: #333;
            margin-bottom: 20px;
            font-size: 2.5rem;
            text-align: center;
        }
        
        .section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.2);
        }
        
        .button.success {
            background: linear-gradient(45deg, #00b894, #00cec9);
        }
        
        .button.danger {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
        }
        
        .input-group {
            margin: 15px 0;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        
        .input-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }
        
        .map-preview {
            width: 100%;
            height: 400px;
            border: 2px solid #ddd;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .coordinates-display {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
        }
        
        .status {
            margin-top: 15px;
            padding: 12px;
            border-radius: 8px;
            font-weight: bold;
            text-align: center;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗺️ تخصيص خريطة الموقع</h1>
        
        <div class="section">
            <h3>📍 إعداد موقع مكتبة أنوار دارس</h3>
            <p>استخدم هذه الأداة لتخصيص موقع المكتبة على الخريطة وإنشاء رابط الخريطة المناسب</p>
        </div>
        
        <div class="grid">
            <div>
                <div class="section">
                    <h3>🔍 البحث عن الموقع</h3>
                    
                    <div class="input-group">
                        <label for="searchLocation">ابحث عن الموقع:</label>
                        <input type="text" id="searchLocation" placeholder="مثال: مكتبة أنوار دارس، صنعاء" />
                    </div>
                    
                    <button class="button" onclick="searchLocation()">🔍 بحث</button>
                    <button class="button" onclick="useCurrentLocation()">📍 موقعي الحالي</button>
                </div>
                
                <div class="section">
                    <h3>📊 الإحداثيات</h3>
                    
                    <div class="input-group">
                        <label for="latitude">خط العرض (Latitude):</label>
                        <input type="number" id="latitude" step="any" placeholder="15.3694" value="15.3694" />
                    </div>
                    
                    <div class="input-group">
                        <label for="longitude">خط الطول (Longitude):</label>
                        <input type="number" id="longitude" step="any" placeholder="44.2066" value="44.2066" />
                    </div>
                    
                    <button class="button success" onclick="updateMap()">🔄 تحديث الخريطة</button>
                </div>
                
                <div class="section">
                    <h3>🏢 معلومات المكان</h3>
                    
                    <div class="input-group">
                        <label for="placeName">اسم المكان:</label>
                        <input type="text" id="placeName" placeholder="مكتبة أنوار دارس" value="مكتبة أنوار دارس" />
                    </div>
                    
                    <div class="input-group">
                        <label for="placeAddress">العنوان:</label>
                        <input type="text" id="placeAddress" placeholder="صنعاء، اليمن - شارع الزبيري" value="صنعاء، اليمن - شارع الزبيري" />
                    </div>
                </div>
            </div>
            
            <div>
                <div class="section">
                    <h3>🗺️ معاينة الخريطة</h3>
                    <iframe 
                        id="mapPreview" 
                        class="map-preview"
                        src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3840.1234567890123!2d44.2066!3d15.3694!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMTXCsDIyJzA5LjgiTiA0NMKwMTInMjMuOCJF!5e0!3m2!1sar!2sye!4v1234567890123!5m2!1sar!2sye"
                        allowfullscreen
                        loading="lazy"
                        referrerpolicy="no-referrer-when-downgrade">
                    </iframe>
                </div>
                
                <div class="section">
                    <h3>🔗 روابط الخريطة</h3>
                    
                    <div class="coordinates-display">
                        <strong>رابط جوجل مابس:</strong><br>
                        <span id="googleMapsLink">https://www.google.com/maps/search/مكتبة+أنوار+دارس+صنعاء</span>
                    </div>
                    
                    <div class="coordinates-display">
                        <strong>رابط خرائط آبل:</strong><br>
                        <span id="appleMapsLink">https://maps.apple.com/?q=مكتبة أنوار دارس صنعاء</span>
                    </div>
                    
                    <div class="coordinates-display">
                        <strong>كود الخريطة المدمجة:</strong><br>
                        <textarea id="embedCode" rows="4" style="width: 100%; font-size: 12px;" readonly></textarea>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h3>⚙️ الإجراءات</h3>
            
            <button class="button success" onclick="generateMapCode()">🔧 إنشاء كود الخريطة</button>
            <button class="button" onclick="testGoogleMaps()">🧪 اختبار جوجل مابس</button>
            <button class="button" onclick="testAppleMaps()">🧪 اختبار خرائط آبل</button>
            <button class="button danger" onclick="copyEmbedCode()">📋 نسخ كود الخريطة</button>
        </div>
        
        <div id="status" class="status" style="display: none;"></div>
        
        <div class="section">
            <h3>📋 تعليمات الاستخدام</h3>
            <ol style="text-align: right;">
                <li><strong>البحث:</strong> ابحث عن موقع المكتبة باستخدام اسمها أو عنوانها</li>
                <li><strong>الإحداثيات:</strong> أدخل خط العرض وخط الطول للموقع الدقيق</li>
                <li><strong>التحديث:</strong> اضغط "تحديث الخريطة" لرؤية الموقع الجديد</li>
                <li><strong>الكود:</strong> انسخ كود الخريطة المدمجة واستخدمه في الموقع</li>
                <li><strong>الاختبار:</strong> اختبر الروابط للتأكد من صحة الموقع</li>
            </ol>
        </div>
    </div>

    <script>
        function showStatus(message, type = 'success') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
            statusDiv.style.display = 'block';
            
            setTimeout(() => {
                statusDiv.style.display = 'none';
            }, 5000);
        }
        
        function updateMap() {
            const lat = document.getElementById('latitude').value;
            const lng = document.getElementById('longitude').value;
            
            if (!lat || !lng) {
                showStatus('يرجى إدخال خط العرض وخط الطول', 'error');
                return;
            }
            
            // تحديث الخريطة
            const mapPreview = document.getElementById('mapPreview');
            const embedUrl = `https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3840.123!2d${lng}!3d${lat}!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2z${encodeURIComponent(lat + ',' + lng)}!5e0!3m2!1sar!2sye!4v${Date.now()}!5m2!1sar!2sye`;
            
            mapPreview.src = embedUrl;
            
            // تحديث الروابط
            updateLinks();
            
            showStatus('تم تحديث الخريطة بنجاح!', 'success');
        }
        
        function updateLinks() {
            const placeName = document.getElementById('placeName').value || 'مكتبة أنوار دارس';
            const placeAddress = document.getElementById('placeAddress').value || 'صنعاء';
            const lat = document.getElementById('latitude').value;
            const lng = document.getElementById('longitude').value;
            
            // تحديث رابط جوجل مابس
            const googleMapsLink = `https://www.google.com/maps/search/${encodeURIComponent(placeName + ' ' + placeAddress)}`;
            document.getElementById('googleMapsLink').textContent = googleMapsLink;
            
            // تحديث رابط خرائط آبل
            const appleMapsLink = `https://maps.apple.com/?q=${encodeURIComponent(placeName + ' ' + placeAddress)}`;
            document.getElementById('appleMapsLink').textContent = appleMapsLink;
        }
        
        function generateMapCode() {
            const lat = document.getElementById('latitude').value;
            const lng = document.getElementById('longitude').value;
            const placeName = document.getElementById('placeName').value || 'مكتبة أنوار دارس';
            
            if (!lat || !lng) {
                showStatus('يرجى إدخال الإحداثيات أولاً', 'error');
                return;
            }
            
            const embedCode = `<iframe
  src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3840.123!2d${lng}!3d${lat}!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2z${encodeURIComponent(lat + ',' + lng)}!5e0!3m2!1sar!2sye!4v${Date.now()}!5m2!1sar!2sye"
  width="100%"
  height="100%"
  style={{ border: 0 }}
  allowFullScreen
  loading="lazy"
  referrerPolicy="no-referrer-when-downgrade"
  title="موقع ${placeName} على الخريطة"
/>`;
            
            document.getElementById('embedCode').value = embedCode;
            showStatus('تم إنشاء كود الخريطة بنجاح!', 'success');
        }
        
        function searchLocation() {
            const searchTerm = document.getElementById('searchLocation').value;
            if (!searchTerm) {
                showStatus('يرجى إدخال اسم المكان للبحث', 'error');
                return;
            }
            
            // فتح جوجل مابس للبحث
            const searchUrl = `https://www.google.com/maps/search/${encodeURIComponent(searchTerm)}`;
            window.open(searchUrl, '_blank');
            
            showStatus('تم فتح جوجل مابس للبحث. انسخ الإحداثيات من الرابط', 'success');
        }
        
        function useCurrentLocation() {
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(function(position) {
                    document.getElementById('latitude').value = position.coords.latitude.toFixed(6);
                    document.getElementById('longitude').value = position.coords.longitude.toFixed(6);
                    updateMap();
                    showStatus('تم الحصول على موقعك الحالي!', 'success');
                }, function(error) {
                    showStatus('لا يمكن الحصول على الموقع الحالي', 'error');
                });
            } else {
                showStatus('المتصفح لا يدعم تحديد الموقع', 'error');
            }
        }
        
        function testGoogleMaps() {
            const link = document.getElementById('googleMapsLink').textContent;
            window.open(link, '_blank');
            showStatus('تم فتح جوجل مابس للاختبار', 'success');
        }
        
        function testAppleMaps() {
            const link = document.getElementById('appleMapsLink').textContent;
            window.open(link, '_blank');
            showStatus('تم فتح خرائط آبل للاختبار', 'success');
        }
        
        function copyEmbedCode() {
            const embedCode = document.getElementById('embedCode');
            if (!embedCode.value) {
                showStatus('يرجى إنشاء كود الخريطة أولاً', 'error');
                return;
            }
            
            embedCode.select();
            document.execCommand('copy');
            showStatus('تم نسخ كود الخريطة! يمكنك لصقه في الموقع', 'success');
        }
        
        // تحديث الروابط عند تحميل الصفحة
        window.onload = function() {
            updateLinks();
            generateMapCode();
        };
        
        // تحديث الروابط عند تغيير المعلومات
        document.getElementById('placeName').addEventListener('input', updateLinks);
        document.getElementById('placeAddress').addEventListener('input', updateLinks);
    </script>
</body>
</html>
