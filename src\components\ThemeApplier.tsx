'use client';

import React, { useEffect } from 'react';
import { useAdmin } from '@/contexts/AdminContext';

const ThemeApplier: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { settings } = useAdmin();

  useEffect(() => {
    if (!settings?.designSettings) {
      console.log('Design settings not available yet');
      return;
    }

    const { designSettings } = settings;
    const root = document.documentElement;

    // Apply CSS Custom Properties
    const cssVars = {
      // Typography - Headings
      '--headings-font-family': designSettings.typography?.headings?.fontFamily || 'Cairo, sans-serif',
      '--headings-font-size': designSettings.typography?.headings?.fontSize || '2rem',
      '--headings-font-weight': designSettings.typography?.headings?.fontWeight || '700',
      '--headings-line-height': designSettings.typography?.headings?.lineHeight || '1.2',
      '--headings-letter-spacing': designSettings.typography?.headings?.letterSpacing || '0.025em',

      // Typography - Subheadings
      '--subheadings-font-family': designSettings.typography?.subheadings?.fontFamily || 'Cairo, sans-serif',
      '--subheadings-font-size': designSettings.typography?.subheadings?.fontSize || '1.5rem',
      '--subheadings-font-weight': designSettings.typography?.subheadings?.fontWeight || '600',
      '--subheadings-line-height': designSettings.typography?.subheadings?.lineHeight || '1.3',
      '--subheadings-letter-spacing': designSettings.typography?.subheadings?.letterSpacing || '0.025em',

      // Typography - Paragraphs
      '--paragraphs-font-family': designSettings.typography?.paragraphs?.fontFamily || 'Cairo, sans-serif',
      '--paragraphs-font-size': designSettings.typography?.paragraphs?.fontSize || '1rem',
      '--paragraphs-font-weight': designSettings.typography?.paragraphs?.fontWeight || '400',
      '--paragraphs-line-height': designSettings.typography?.paragraphs?.lineHeight || '1.6',
      '--paragraphs-letter-spacing': designSettings.typography?.paragraphs?.letterSpacing || '0.025em',

      // Typography - Notes
      '--notes-font-family': designSettings.typography?.notes?.fontFamily || 'Cairo, sans-serif',
      '--notes-font-size': designSettings.typography?.notes?.fontSize || '0.875rem',
      '--notes-font-weight': designSettings.typography?.notes?.fontWeight || '400',
      '--notes-line-height': designSettings.typography?.notes?.lineHeight || '1.5',
      '--notes-letter-spacing': designSettings.typography?.notes?.letterSpacing || '0.025em',

      // Layout
      '--border-radius': designSettings.layout?.borderRadius || '0.5rem',
      '--spacing': designSettings.layout?.spacing || '1rem',
      '--container-max-width': designSettings.layout?.containerMaxWidth || '1200px',
      '--card-shadow': designSettings.layout?.cardShadow || '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
      '--border-width': designSettings.layout?.borderWidth || '1px',

      // Colors
      '--color-primary': designSettings.colors?.primary || '#2563eb',
      '--color-secondary': designSettings.colors?.secondary || '#64748b',
      '--color-accent': designSettings.colors?.accent || '#d946ef',
      '--color-success': designSettings.colors?.success || '#10b981',
      '--color-warning': designSettings.colors?.warning || '#f59e0b',
      '--color-error': designSettings.colors?.error || '#ef4444',
      '--color-info': designSettings.colors?.info || '#3b82f6',
      '--color-background': designSettings.colors?.background || '#f8fafc',
      '--color-surface': designSettings.colors?.surface || '#ffffff',
      '--color-text-primary': designSettings.colors?.text?.primary || '#1f2937',
      '--color-text-secondary': designSettings.colors?.text?.secondary || '#6b7280',
      '--color-text-muted': designSettings.colors?.text?.muted || '#9ca3af',
      '--color-text-inverse': designSettings.colors?.text?.inverse || '#ffffff',
      '--color-border': designSettings.colors?.border || '#e5e7eb',

      // Animations
      '--animation-duration': designSettings.animations?.duration || '300ms',
      '--animation-easing': designSettings.animations?.easing || 'cubic-bezier(0.4, 0, 0.2, 1)',
      '--hover-scale': designSettings.animations?.hoverScale || '1.05',

      // Derived colors for Tailwind compatibility
      '--primary-50': lightenColor(designSettings.colors?.primary || '#2563eb', 0.95),
      '--primary-100': lightenColor(designSettings.colors?.primary || '#2563eb', 0.9),
      '--primary-200': lightenColor(designSettings.colors?.primary || '#2563eb', 0.8),
      '--primary-300': lightenColor(designSettings.colors?.primary || '#2563eb', 0.6),
      '--primary-400': lightenColor(designSettings.colors?.primary || '#2563eb', 0.4),
      '--primary-500': designSettings.colors?.primary || '#2563eb',
      '--primary-600': darkenColor(designSettings.colors?.primary || '#2563eb', 0.1),
      '--primary-700': darkenColor(designSettings.colors?.primary || '#2563eb', 0.2),
      '--primary-800': darkenColor(designSettings.colors?.primary || '#2563eb', 0.3),
      '--primary-900': darkenColor(designSettings.colors?.primary || '#2563eb', 0.4),
    };

    // Apply all CSS variables
    Object.entries(cssVars).forEach(([property, value]) => {
      root.style.setProperty(property, value);
    });

    // Apply body styles with better font handling
    const bodyFontFamily = designSettings.typography?.paragraphs?.fontFamily || 'Cairo, sans-serif';
    document.body.style.fontFamily = bodyFontFamily;
    document.body.style.fontSize = designSettings.typography?.paragraphs?.fontSize || '1rem';
    document.body.style.lineHeight = designSettings.typography?.paragraphs?.lineHeight || '1.6';
    document.body.style.letterSpacing = designSettings.typography?.paragraphs?.letterSpacing || '0.025em';
    document.body.style.backgroundColor = designSettings.colors?.background || '#f8fafc';
    document.body.style.color = designSettings.colors?.text?.primary || '#1f2937';

    // تحسين عرض الخطوط
    document.body.style.webkitFontSmoothing = 'antialiased';
    document.body.style.mozOsxFontSmoothing = 'grayscale';
    document.body.style.textRendering = 'optimizeLegibility';

    // Create dynamic CSS for complex styles
    const styleId = 'dynamic-theme-styles';
    let styleElement = document.getElementById(styleId) as HTMLStyleElement;
    
    if (!styleElement) {
      styleElement = document.createElement('style');
      styleElement.id = styleId;
      document.head.appendChild(styleElement);
    }

    styleElement.textContent = `
      /* Dynamic Theme Styles */

      /* Typography - Headings */
      h1, .heading-1, .text-3xl, .text-4xl, .text-5xl {
        font-family: ${designSettings.typography?.headings?.fontFamily || 'Cairo, sans-serif'} !important;
        font-size: ${designSettings.typography?.headings?.fontSize || '2rem'} !important;
        font-weight: ${designSettings.typography?.headings?.fontWeight || '700'} !important;
        line-height: ${designSettings.typography?.headings?.lineHeight || '1.2'} !important;
        letter-spacing: ${designSettings.typography?.headings?.letterSpacing || '0.025em'} !important;
      }

      /* Typography - Subheadings */
      h2, h3, .heading-2, .heading-3 {
        font-family: ${designSettings.typography?.subheadings?.fontFamily || 'Cairo, sans-serif'} !important;
        font-size: ${designSettings.typography?.subheadings?.fontSize || '1.5rem'} !important;
        font-weight: ${designSettings.typography?.subheadings?.fontWeight || '600'} !important;
        line-height: ${designSettings.typography?.subheadings?.lineHeight || '1.3'} !important;
        letter-spacing: ${designSettings.typography?.subheadings?.letterSpacing || '0.025em'} !important;
      }

      /* Typography - Paragraphs */
      p, .paragraph, .text-content, div, span, a, button, input, textarea, select {
        font-family: ${designSettings.typography?.paragraphs?.fontFamily || 'Cairo, sans-serif'} !important;
        font-size: ${designSettings.typography?.paragraphs?.fontSize || '1rem'} !important;
        font-weight: ${designSettings.typography?.paragraphs?.fontWeight || '400'} !important;
        line-height: ${designSettings.typography?.paragraphs?.lineHeight || '1.6'} !important;
        letter-spacing: ${designSettings.typography?.paragraphs?.letterSpacing || '0.025em'} !important;
      }

      /* Typography - Notes */
      .text-sm, .notes, .caption, small {
        font-family: ${designSettings.typography?.notes?.fontFamily || 'Cairo, sans-serif'} !important;
        font-size: ${designSettings.typography?.notes?.fontSize || '0.875rem'} !important;
        font-weight: ${designSettings.typography?.notes?.fontWeight || '400'} !important;
        line-height: ${designSettings.typography?.notes?.lineHeight || '1.5'} !important;
        letter-spacing: ${designSettings.typography?.notes?.letterSpacing || '0.025em'} !important;
      }

      /* Layout */
      .rounded-theme { border-radius: ${designSettings.layout?.borderRadius || '0.5rem'} !important; }
      .shadow-theme { box-shadow: ${designSettings.layout?.cardShadow || '0 4px 6px -1px rgba(0, 0, 0, 0.1)'} !important; }
      .border-theme { border-width: ${designSettings.layout?.borderWidth || '1px'} !important; }
      .spacing-theme { padding: ${designSettings.layout?.spacing || '1rem'} !important; }
      .margin-theme { margin: ${designSettings.layout?.spacing || '1rem'} !important; }

      /* Colors */
      .bg-primary { background-color: ${designSettings.colors?.primary || '#2563eb'} !important; }
      .bg-secondary { background-color: ${designSettings.colors?.secondary || '#64748b'} !important; }
      .bg-accent { background-color: ${designSettings.colors?.accent || '#d946ef'} !important; }
      .bg-success { background-color: ${designSettings.colors?.success || '#10b981'} !important; }
      .bg-warning { background-color: ${designSettings.colors?.warning || '#f59e0b'} !important; }
      .bg-error { background-color: ${designSettings.colors?.error || '#ef4444'} !important; }
      .bg-surface { background-color: ${designSettings.colors?.surface || '#ffffff'} !important; }

      .text-primary-color { color: ${designSettings.colors?.primary || '#2563eb'} !important; }
      .text-secondary-color { color: ${designSettings.colors?.secondary || '#64748b'} !important; }
      .text-accent-color { color: ${designSettings.colors?.accent || '#d946ef'} !important; }
      .text-theme-primary { color: ${designSettings.colors?.text?.primary || '#1f2937'} !important; }
      .text-theme-secondary { color: ${designSettings.colors?.text?.secondary || '#6b7280'} !important; }
      .text-theme-muted { color: ${designSettings.colors?.text?.muted || '#9ca3af'} !important; }
      .text-theme-inverse { color: ${designSettings.colors?.text?.inverse || '#ffffff'} !important; }

      .border-theme-color { border-color: ${designSettings.colors?.border || '#e5e7eb'} !important; }

      /* Animations */
      .transition-theme {
        transition-duration: ${designSettings.animations?.duration || '300ms'} !important;
        transition-timing-function: ${designSettings.animations?.easing || 'ease'} !important;
      }

      .hover-scale:hover {
        transform: scale(${designSettings.animations?.hoverScale || '1.05'}) !important;
        transition: transform ${designSettings.animations?.duration || '300ms'} ${designSettings.animations?.easing || 'ease'} !important;
      }

      /* Button Styles */
      .btn-primary {
        background-color: ${designSettings.colors?.primary || '#2563eb'} !important;
        color: ${designSettings.colors?.text?.inverse || '#ffffff'} !important;
        border-radius: ${designSettings.layout?.borderRadius || '0.5rem'} !important;
        transition: all ${designSettings.animations?.duration || '300ms'} ${designSettings.animations?.easing || 'ease'} !important;
      }

      .btn-primary:hover {
        background-color: ${darkenColor(designSettings.colors?.primary || '#2563eb', 0.1)} !important;
        transform: scale(${designSettings.animations?.hoverScale || '1.05'}) !important;
      }

      .btn-secondary {
        background-color: ${designSettings.colors?.secondary || '#64748b'} !important;
        color: ${designSettings.colors?.text?.inverse || '#ffffff'} !important;
        border-radius: ${designSettings.layout?.borderRadius || '0.5rem'} !important;
        transition: all ${designSettings.animations?.duration || '300ms'} ${designSettings.animations?.easing || 'ease'} !important;
      }

      .btn-secondary:hover {
        background-color: ${darkenColor(designSettings.colors?.secondary || '#64748b', 0.1)} !important;
        transform: scale(${designSettings.animations?.hoverScale || '1.05'}) !important;
      }

      /* Card Styles */
      .card-theme {
        background-color: ${designSettings.colors?.surface || '#ffffff'} !important;
        border-radius: ${designSettings.layout?.borderRadius || '0.5rem'} !important;
        box-shadow: ${designSettings.layout?.cardShadow || '0 4px 6px -1px rgba(0, 0, 0, 0.1)'} !important;
        border: ${designSettings.layout?.borderWidth || '1px'} solid ${designSettings.colors?.border || '#e5e7eb'} !important;
        transition: all ${designSettings.animations?.duration || '300ms'} ${designSettings.animations?.easing || 'ease'} !important;
      }

      .card-theme:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
      }

      /* Container */
      .container-theme {
        max-width: ${designSettings.layout?.containerMaxWidth || '1200px'} !important;
        margin: 0 auto !important;
        padding: 0 ${designSettings.layout?.spacing || '1rem'} !important;
      }

      /* Input Styles */
      .input-theme {
        border-radius: ${designSettings.layout?.borderRadius || '0.5rem'} !important;
        border: ${designSettings.layout?.borderWidth || '1px'} solid ${designSettings.colors?.border || '#e5e7eb'} !important;
        font-family: ${designSettings.typography?.paragraphs?.fontFamily || 'Cairo, sans-serif'} !important;
        font-size: ${designSettings.typography?.paragraphs?.fontSize || '1rem'} !important;
        transition: all ${designSettings.animations?.duration || '300ms'} ${designSettings.animations?.easing || 'ease'} !important;
      }
      
      .input-theme:focus {
        border-color: ${designSettings.colors.primary} !important;
        box-shadow: 0 0 0 3px ${lightenColor(designSettings.colors.primary, 0.9)} !important;
      }

      /* Override Tailwind primary colors */
      .bg-primary-50 { background-color: ${lightenColor(designSettings.colors.primary, 0.95)} !important; }
      .bg-primary-100 { background-color: ${lightenColor(designSettings.colors.primary, 0.9)} !important; }
      .bg-primary-200 { background-color: ${lightenColor(designSettings.colors.primary, 0.8)} !important; }
      .bg-primary-300 { background-color: ${lightenColor(designSettings.colors.primary, 0.6)} !important; }
      .bg-primary-400 { background-color: ${lightenColor(designSettings.colors.primary, 0.4)} !important; }
      .bg-primary-500 { background-color: ${designSettings.colors.primary} !important; }
      .bg-primary-600 { background-color: ${darkenColor(designSettings.colors.primary, 0.1)} !important; }
      .bg-primary-700 { background-color: ${darkenColor(designSettings.colors.primary, 0.2)} !important; }
      .bg-primary-800 { background-color: ${darkenColor(designSettings.colors.primary, 0.3)} !important; }
      .bg-primary-900 { background-color: ${darkenColor(designSettings.colors.primary, 0.4)} !important; }
      
      .text-primary-50 { color: ${lightenColor(designSettings.colors.primary, 0.95)} !important; }
      .text-primary-100 { color: ${lightenColor(designSettings.colors.primary, 0.9)} !important; }
      .text-primary-200 { color: ${lightenColor(designSettings.colors.primary, 0.8)} !important; }
      .text-primary-300 { color: ${lightenColor(designSettings.colors.primary, 0.6)} !important; }
      .text-primary-400 { color: ${lightenColor(designSettings.colors.primary, 0.4)} !important; }
      .text-primary-500 { color: ${designSettings.colors.primary} !important; }
      .text-primary-600 { color: ${darkenColor(designSettings.colors.primary, 0.1)} !important; }
      .text-primary-700 { color: ${darkenColor(designSettings.colors.primary, 0.2)} !important; }
      .text-primary-800 { color: ${darkenColor(designSettings.colors.primary, 0.3)} !important; }
      .text-primary-900 { color: ${darkenColor(designSettings.colors.primary, 0.4)} !important; }
      
      .border-primary-50 { border-color: ${lightenColor(designSettings.colors.primary, 0.95)} !important; }
      .border-primary-100 { border-color: ${lightenColor(designSettings.colors.primary, 0.9)} !important; }
      .border-primary-200 { border-color: ${lightenColor(designSettings.colors.primary, 0.8)} !important; }
      .border-primary-300 { border-color: ${lightenColor(designSettings.colors.primary, 0.6)} !important; }
      .border-primary-400 { border-color: ${lightenColor(designSettings.colors.primary, 0.4)} !important; }
      .border-primary-500 { border-color: ${designSettings.colors.primary} !important; }
      .border-primary-600 { border-color: ${darkenColor(designSettings.colors.primary, 0.1)} !important; }
      .border-primary-700 { border-color: ${darkenColor(designSettings.colors.primary, 0.2)} !important; }
      .border-primary-800 { border-color: ${darkenColor(designSettings.colors.primary, 0.3)} !important; }
      .border-primary-900 { border-color: ${darkenColor(designSettings.colors.primary, 0.4)} !important; }
    `;

    // إعادة تحميل الخطوط المخصصة
    reloadCustomFonts();

  }, [settings]);

  const reloadCustomFonts = async () => {
    try {
      const response = await fetch('/api/fonts');
      const data = await response.json();

      if (data.fonts && data.fonts.length > 0) {
        const fontCSS = data.fonts.map((font: any) => {
          const fontType = font.type === 'woff2' ? 'woff2' :
                         font.type === 'woff' ? 'woff' :
                         font.type === 'ttf' ? 'truetype' :
                         font.type === 'otf' ? 'opentype' : 'truetype';

          return `
            @font-face {
              font-family: '${font.family}';
              src: url('${font.path}') format('${fontType}');
              font-weight: normal;
              font-style: normal;
              font-display: swap;
            }
          `;
        }).join('\n');

        const styleId = 'theme-applier-custom-fonts';
        let styleElement = document.getElementById(styleId) as HTMLStyleElement;

        if (!styleElement) {
          styleElement = document.createElement('style');
          styleElement.id = styleId;
          document.head.appendChild(styleElement);
        }

        styleElement.textContent = fontCSS;
        console.log('تم إعادة تحميل الخطوط المخصصة في ThemeApplier');
      }
    } catch (error) {
      console.error('خطأ في إعادة تحميل الخطوط المخصصة:', error);
    }
  };

  return <>{children}</>;
};

// Helper functions for color manipulation
function hexToRgb(hex: string): { r: number; g: number; b: number } | null {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
}

function rgbToHex(r: number, g: number, b: number): string {
  return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
}

function lightenColor(hex: string, amount: number): string {
  const rgb = hexToRgb(hex);
  if (!rgb) return hex;
  
  const r = Math.min(255, Math.round(rgb.r + (255 - rgb.r) * amount));
  const g = Math.min(255, Math.round(rgb.g + (255 - rgb.g) * amount));
  const b = Math.min(255, Math.round(rgb.b + (255 - rgb.b) * amount));
  
  return rgbToHex(r, g, b);
}

function darkenColor(hex: string, amount: number): string {
  const rgb = hexToRgb(hex);
  if (!rgb) return hex;
  
  const r = Math.max(0, Math.round(rgb.r * (1 - amount)));
  const g = Math.max(0, Math.round(rgb.g * (1 - amount)));
  const b = Math.max(0, Math.round(rgb.b * (1 - amount)));
  
  return rgbToHex(r, g, b);
}

export default ThemeApplier;
