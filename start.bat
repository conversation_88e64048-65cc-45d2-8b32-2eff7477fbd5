@echo off
echo ========================================
echo       متجر اليمن الإلكتروني
echo ========================================
echo.

echo جاري التحقق من Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Node.js غير مثبت!
    echo يرجى تحميل وتثبيت Node.js من: https://nodejs.org
    echo.
    pause
    exit /b 1
)

echo Node.js مثبت بنجاح!
echo.

echo جاري تثبيت المكتبات المطلوبة...
npm install

if %errorlevel% neq 0 (
    echo خطأ في تثبيت المكتبات!
    pause
    exit /b 1
)

echo.
echo تم تثبيت المكتبات بنجاح!
echo.

echo جاري تشغيل المشروع...
echo سيتم فتح الموقع على: http://localhost:3000
echo.

npm run dev
