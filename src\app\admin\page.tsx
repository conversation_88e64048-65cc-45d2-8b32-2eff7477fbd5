'use client';

import React, { useState } from 'react';
import { useAdmin } from '@/contexts/AdminContext';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import StoreSettings from '@/components/admin/StoreSettings';
import Reports from '@/components/admin/Reports';
import UserManagement from '@/components/admin/UserManagement';
import AddProductForm from '@/components/admin/AddProductForm';
import DataTestPanel from '@/components/admin/DataTestPanel';
import CategoryManagement from '@/components/admin/CategoryManagement';
import AdvancedDesignSettings from '@/components/admin/AdvancedDesignSettings';

import {
  Package,
  ShoppingCart,
  Users,
  Settings,
  Plus,
  Edit,
  Trash2,
  Eye,
  TrendingUp,
  DollarSign,
  BarChart3,
  FolderOpen,
  Palette,
} from 'lucide-react';

export default function AdminPage() {
  const {
    products,
    categories,
    orders,
    isAdmin,
    adminLogin,
    adminLogout,
    resetAdminData,
    addProduct,
    updateProduct,
    deleteProduct
  } = useAdmin();
  
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [activeTab, setActiveTab] = useState('dashboard');
  const [showAddProduct, setShowAddProduct] = useState(false);
  const [editingProduct, setEditingProduct] = useState<string | null>(null);

  // Login form
  if (!isAdmin) {
    const handleLogin = (e: React.FormEvent) => {
      e.preventDefault();
      if (adminLogin(username, password)) {
        setUsername('');
        setPassword('');
      } else {
        alert('اسم المستخدم أو كلمة المرور غير صحيحة');
      }
    };

    return (
      <div className="min-h-screen gradient-bg">
        <Header />
        <main className="container mx-auto px-4 py-16">
          <div className="max-w-md mx-auto card animate-fade-in-up">
            <div className="text-center mb-8">
              <div className="gradient-bg w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 animate-float">
                <Settings className="w-8 h-8 text-white" />
              </div>
              <h1 className="text-2xl font-bold gradient-text">لوحة التحكم الإدارية</h1>
              <p className="text-gray-600 mt-2">أدخل بياناتك للوصول إلى لوحة التحكم</p>
            </div>
            
            <form onSubmit={handleLogin}>
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  اسم المستخدم
                </label>
                <input
                  type="text"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  className="input-field animate-fade-in-left"
                  placeholder="أدخل اسم المستخدم"
                  required
                />
              </div>

              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  كلمة المرور
                </label>
                <input
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="input-field animate-fade-in-right"
                  placeholder="أدخل كلمة المرور"
                  required
                />
              </div>
              
              <button
                type="submit"
                className="w-full btn-primary animate-fade-in-up"
              >
                دخول
              </button>
            </form>
            
            <div className="mt-6 text-center text-sm text-gray-500 animate-fade-in-up">
              <p className="text-xs">أدخل بيانات الدخول الخاصة بك للوصول إلى لوحة التحكم</p>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  // Admin Dashboard
  const stats = {
    totalProducts: products.length,
    totalOrders: orders.length,
    totalRevenue: orders.reduce((sum, order) => sum + order.totalAmount, 0),
    pendingOrders: orders.filter(order => order.status === 'pending').length
  };

  return (
    <div className="min-h-screen gradient-bg">
      <Header />

      <main className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8 animate-fade-in-up">
          <div>
            <h1 className="text-3xl font-bold gradient-text">لوحة التحكم الإدارية</h1>
            <p className="text-gray-600">إدارة المتجر والمنتجات والطلبات</p>
          </div>
          <button
            onClick={adminLogout}
            className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
          >
            تسجيل الخروج
          </button>
        </div>

        {/* Navigation Tabs */}
        <div className="bg-white rounded-lg shadow-md mb-8">
          <div className="flex border-b">
            {[
              { id: 'dashboard', name: 'لوحة المعلومات', icon: TrendingUp },
              { id: 'products', name: 'المنتجات', icon: Package },
              { id: 'categories', name: 'الفئات والتصنيفات', icon: FolderOpen },
              { id: 'orders', name: 'الطلبات', icon: ShoppingCart },
              { id: 'users', name: 'المستخدمين', icon: Users },
              { id: 'reports', name: 'التقارير', icon: BarChart3 },
              { id: 'settings', name: 'الإعدادات', icon: Settings },
              { id: 'design', name: 'التصميم والمظهر', icon: Palette },
              { id: 'test', name: 'اختبار البيانات', icon: Settings }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center px-6 py-4 font-medium transition-all duration-300 nav-item ${
                  activeTab === tab.id
                    ? 'text-primary-600 border-b-2 border-primary-600 active'
                    : 'text-gray-600 hover:text-gray-800'
                }`}
              >
                <tab.icon className={`w-5 h-5 ml-2 ${activeTab === tab.id ? 'animate-pulse' : ''}`} />
                {tab.name}
              </button>
            ))}
          </div>
        </div>

        {/* Dashboard Tab */}
        {activeTab === 'dashboard' && (
          <div className="space-y-8">
            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="card animate-fade-in-left">
                <div className="flex items-center">
                  <div className="bg-blue-100 p-3 rounded-full animate-float">
                    <Package className="w-6 h-6 text-blue-600" />
                  </div>
                  <div className="mr-4">
                    <p className="text-sm text-gray-600">إجمالي المنتجات</p>
                    <p className="text-2xl font-bold gradient-text">{stats.totalProducts}</p>
                  </div>
                </div>
              </div>
              
              <div className="card animate-fade-in-up">
                <div className="flex items-center">
                  <div className="bg-green-100 p-3 rounded-full animate-float">
                    <ShoppingCart className="w-6 h-6 text-green-600" />
                  </div>
                  <div className="mr-4">
                    <p className="text-sm text-gray-600">إجمالي الطلبات</p>
                    <p className="text-2xl font-bold gradient-text">{stats.totalOrders}</p>
                  </div>
                </div>
              </div>
              
              <div className="card animate-fade-in-right">
                <div className="flex items-center">
                  <div className="bg-yellow-100 p-3 rounded-full animate-float">
                    <DollarSign className="w-6 h-6 text-yellow-600" />
                  </div>
                  <div className="mr-4">
                    <p className="text-sm text-gray-600">إجمالي المبيعات</p>
                    <p className="text-2xl font-bold gradient-text">
                      {stats.totalRevenue.toLocaleString()} ريال
                    </p>
                  </div>
                </div>
              </div>

              <div className="card animate-fade-in-left">
                <div className="flex items-center">
                  <div className="bg-red-100 p-3 rounded-full animate-float">
                    <Users className="w-6 h-6 text-red-600" />
                  </div>
                  <div className="mr-4">
                    <p className="text-sm text-gray-600">طلبات معلقة</p>
                    <p className="text-2xl font-bold gradient-text">{stats.pendingOrders}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Recent Orders */}
            <div className="bg-white rounded-lg shadow-md">
              <div className="p-6 border-b">
                <h2 className="text-xl font-semibold">آخر الطلبات</h2>
              </div>
              <div className="p-6">
                {orders.length === 0 ? (
                  <p className="text-gray-500 text-center py-8">لا توجد طلبات حتى الآن</p>
                ) : (
                  <div className="space-y-4">
                    {orders.slice(0, 5).map((order) => (
                      <div key={order.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div>
                          <p className="font-semibold">طلب #{order.id}</p>
                          <p className="text-sm text-gray-600">{order.customerInfo.name}</p>
                        </div>
                        <div className="text-left">
                          <p className="font-semibold">{order.totalAmount.toLocaleString()} ريال</p>
                          <span className={`text-xs px-2 py-1 rounded-full ${
                            order.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                            order.status === 'processing' ? 'bg-blue-100 text-blue-800' :
                            order.status === 'delivered' ? 'bg-green-100 text-green-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {order.status === 'pending' ? 'معلق' :
                             order.status === 'processing' ? 'قيد التحضير' :
                             order.status === 'delivered' ? 'تم التوصيل' : order.status}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Categories Tab */}
        {activeTab === 'categories' && (
          <CategoryManagement />
        )}

        {/* Products Tab */}
        {activeTab === 'products' && (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-semibold">إدارة المنتجات</h2>
              <button
                onClick={() => setShowAddProduct(true)}
                className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors flex items-center"
              >
                <Plus className="w-5 h-5 ml-2" />
                إضافة منتج
              </button>
            </div>

            <div className="bg-white rounded-lg shadow-md">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">المنتج</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الفئة</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">السعر</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الكمية</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحالة</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {products.map((product) => (
                      <tr key={product.id}>
                        <td className="px-6 py-4">
                          <div className="flex items-center">
                            <img
                              src={product.image}
                              alt={product.name}
                              className="w-12 h-12 rounded-lg object-cover ml-4"
                            />
                            <div>
                              <p className="font-semibold">{product.name}</p>
                              <p className="text-sm text-gray-500">{product.brand}</p>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          {categories.find(cat => cat.id === product.category)?.name}
                        </td>
                        <td className="px-6 py-4 font-semibold">
                          {product.price.toLocaleString()} ريال
                        </td>
                        <td className="px-6 py-4">{product.quantity}</td>
                        <td className="px-6 py-4">
                          <span className={`px-2 py-1 rounded-full text-xs font-semibold ${
                            product.inStock ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                          }`}>
                            {product.inStock ? 'متوفر' : 'نفدت الكمية'}
                          </span>
                        </td>
                        <td className="px-6 py-4">
                          <div className="flex space-x-2 space-x-reverse">
                            <button className="text-blue-600 hover:text-blue-800">
                              <Eye className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => {
                                setEditingProduct(product.id);
                                setShowAddProduct(true);
                              }}
                              className="text-green-600 hover:text-green-800"
                            >
                              <Edit className="w-4 h-4" />
                            </button>
                            <button 
                              onClick={() => deleteProduct(product.id)}
                              className="text-red-600 hover:text-red-800"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}

        {/* Orders Tab */}
        {activeTab === 'orders' && (
          <div className="space-y-6">
            <h2 className="text-2xl font-semibold">إدارة الطلبات</h2>
            
            <div className="bg-white rounded-lg shadow-md">
              {orders.length === 0 ? (
                <div className="p-8 text-center text-gray-500">
                  لا توجد طلبات حتى الآن
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">رقم الطلب</th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">العميل</th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">المبلغ</th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحالة</th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">التاريخ</th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الإجراءات</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {orders.map((order) => (
                        <tr key={order.id}>
                          <td className="px-6 py-4 font-semibold">#{order.id}</td>
                          <td className="px-6 py-4">
                            <div>
                              <p className="font-semibold">{order.customerInfo.name}</p>
                              <p className="text-sm text-gray-500">{order.customerInfo.phone}</p>
                            </div>
                          </td>
                          <td className="px-6 py-4 font-semibold">
                            {order.totalAmount.toLocaleString()} ريال
                          </td>
                          <td className="px-6 py-4">
                            <span className={`px-2 py-1 rounded-full text-xs font-semibold ${
                              order.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                              order.status === 'processing' ? 'bg-blue-100 text-blue-800' :
                              order.status === 'delivered' ? 'bg-green-100 text-green-800' :
                              'bg-gray-100 text-gray-800'
                            }`}>
                              {order.status === 'pending' ? 'معلق' :
                               order.status === 'processing' ? 'قيد التحضير' :
                               order.status === 'delivered' ? 'تم التوصيل' : order.status}
                            </span>
                          </td>
                          <td className="px-6 py-4 text-sm text-gray-500">
                            {order.createdAt.toLocaleDateString('ar-YE')}
                          </td>
                          <td className="px-6 py-4">
                            <button className="text-blue-600 hover:text-blue-800">
                              <Eye className="w-4 h-4" />
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Users Tab */}
        {activeTab === 'users' && (
          <UserManagement />
        )}

        {/* Reports Tab */}
        {activeTab === 'reports' && (
          <Reports />
        )}

        {/* Settings Tab */}
        {activeTab === 'settings' && (
          <StoreSettings />
        )}

        {/* Design Tab */}
        {activeTab === 'design' && (
          <AdvancedDesignSettings />
        )}

        {/* Test Tab */}
        {activeTab === 'test' && (
          <DataTestPanel />
        )}
      </main>

      {/* Add Product Form Modal */}
      <AddProductForm
        isOpen={showAddProduct}
        onClose={() => {
          setShowAddProduct(false);
          setEditingProduct(null);
        }}
        editingProduct={editingProduct ? products.find(p => p.id === editingProduct) : null}
      />

      <Footer />
    </div>
  );
}
