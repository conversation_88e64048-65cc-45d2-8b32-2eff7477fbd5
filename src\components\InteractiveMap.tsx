'use client';

import React, { useState } from 'react';
import { MapPin, Settings, Navigation, MessageCircle, ExternalLink } from 'lucide-react';
import { useAdmin } from '@/contexts/AdminContext';

interface InteractiveMapProps {
  address: string;
  whatsapp?: string;
}

const InteractiveMap: React.FC<InteractiveMapProps> = ({ address, whatsapp }) => {
  const { isAdmin, settings, updateSettings } = useAdmin();

  // تحميل الموقع المحفوظ من localStorage أو الإعدادات
  const getStoredLocation = () => {
    if (typeof window !== 'undefined') {
      const storedSettings = localStorage.getItem('admin-settings');
      if (storedSettings) {
        try {
          const parsed = JSON.parse(storedSettings);
          if (parsed.storeLocation) {
            return parsed.storeLocation;
          }
        } catch (e) {
          console.error('خطأ في قراءة الموقع المحفوظ:', e);
        }
      }
    }
    return settings.storeLocation || { lat: '15.434276', lng: '44.2152547' };
  };

  const [savedLocation, setSavedLocation] = useState(getStoredLocation());
  const [showLocationForm, setShowLocationForm] = useState(false);
  const [coordinates, setCoordinates] = useState({
    lat: savedLocation.lat,
    lng: savedLocation.lng
  });
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // تحديث الخريطة عند تحميل المكون
  React.useEffect(() => {
    const location = getStoredLocation();
    setSavedLocation(location);
    setCoordinates({ lat: location.lat, lng: location.lng });
  }, [settings.storeLocation]);

  const updateMapLocation = () => {
    setShowLocationForm(false);
    setHasUnsavedChanges(true);
    alert('تم تحديث موقع المتجر على الخريطة! اضغط "حفظ الموقع" لحفظ التغييرات بشكل دائم.');
  };

  const saveStoreLocation = () => {
    const updatedSettings = {
      ...settings,
      storeLocation: {
        lat: coordinates.lat,
        lng: coordinates.lng,
        lastUpdated: new Date().toISOString()
      }
    };

    updateSettings(updatedSettings);
    setHasUnsavedChanges(false);
    setSavedLocation({ lat: coordinates.lat, lng: coordinates.lng });

    // حفظ في localStorage أيضاً
    if (typeof window !== 'undefined') {
      localStorage.setItem('admin-settings', JSON.stringify(updatedSettings));
    }

    alert('✅ تم حفظ موقع المتجر بنجاح! سيظهر هذا الموقع لجميع العملاء والزوار.');
  };

  const quickSetLocation = () => {
    const lat = prompt('أدخل خط العرض (Latitude):', coordinates.lat);
    const lng = prompt('أدخل خط الطول (Longitude):', coordinates.lng);

    if (lat && lng) {
      setCoordinates({ lat, lng });
      const newSrc = generateMapSrc(lat, lng);
      setMapSrc(newSrc);
      setHasUnsavedChanges(true);
      alert('تم تحديث موقع الدبوس على الخريطة! اضغط "حفظ الموقع" لحفظ التغييرات بشكل دائم.');
    }
  };

  const getCurrentLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const lat = position.coords.latitude.toString();
          const lng = position.coords.longitude.toString();
          setCoordinates({ lat, lng });
          const newSrc = generateMapSrc(lat, lng);
          setMapSrc(newSrc);
          setHasUnsavedChanges(true);
          alert('تم تحديث الخريطة بموقعك الحالي! اضغط "حفظ الموقع" لحفظ التغييرات بشكل دائم.');
        },
        () => {
          alert('لا يمكن الحصول على موقعك الحالي');
        }
      );
    } else {
      alert('المتصفح لا يدعم تحديد الموقع');
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <MapPin className="w-6 h-6 text-red-600" />
          <h2 className="text-2xl font-bold text-gray-800">موقع المتجر</h2>
        </div>

        {/* أزرار التحكم - للمدير فقط */}
        {isAdmin && (
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => setShowLocationForm(!showLocationForm)}
              className="flex items-center gap-2 bg-blue-600 text-white px-3 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm"
            >
              <Settings className="w-4 h-4" />
              تعديل الموقع
            </button>
            <button
              onClick={quickSetLocation}
              className="flex items-center gap-2 bg-green-600 text-white px-3 py-2 rounded-lg hover:bg-green-700 transition-colors text-sm"
            >
              📍 وضع دبوس
            </button>
            <button
              onClick={getCurrentLocation}
              className="flex items-center gap-2 bg-purple-600 text-white px-3 py-2 rounded-lg hover:bg-purple-700 transition-colors text-sm"
            >
              🎯 موقعي
            </button>
            {hasUnsavedChanges && (
              <button
                onClick={saveStoreLocation}
                className="flex items-center gap-2 bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors text-sm font-semibold animate-pulse"
              >
                💾 حفظ الموقع
              </button>
            )}
          </div>
        )}
      </div>

      {/* نموذج تحديد الموقع - للمدير فقط */}
      {isAdmin && showLocationForm && (
        <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h3 className="font-semibold text-blue-800 mb-3">تحديد موقع المتجر</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-blue-700 mb-2">
                خط العرض (Latitude):
              </label>
              <input
                type="number"
                step="any"
                value={coordinates.lat}
                onChange={(e) => setCoordinates(prev => ({ ...prev, lat: e.target.value }))}
                className="w-full px-3 py-2 border border-blue-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="15.3694"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-blue-700 mb-2">
                خط الطول (Longitude):
              </label>
              <input
                type="number"
                step="any"
                value={coordinates.lng}
                onChange={(e) => setCoordinates(prev => ({ ...prev, lng: e.target.value }))}
                className="w-full px-3 py-2 border border-blue-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="44.2066"
              />
            </div>
          </div>
          <div className="mt-4 flex gap-2">
            <button
              onClick={updateMapLocation}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              تحديث الخريطة
            </button>
            <button
              onClick={() => setShowLocationForm(false)}
              className="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors"
            >
              إلغاء
            </button>
          </div>
        </div>
      )}
      
      {/* الخريطة */}
      <div className="h-80 rounded-lg overflow-hidden shadow-md mb-4">
        <iframe
          src={`https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d1920!2d${coordinates.lng}!3d${coordinates.lat}!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x1603d9e4b0964ecf:0x3927f400397d0531!2z2YXZg9iq2KjYqSDYp9mG2YjYp9ixINiv2KfYsdizINmF2YPYqtio2Kkg2KfZhtmI2KfYsSDYr9in2LHYsw!5e0!3m2!1sar!2sye!4v${Date.now()}!5m2!1sar!2sye`}
          width="100%"
          height="100%"
          style={{ border: 0 }}
          allowFullScreen
          loading="lazy"
          referrerPolicy="no-referrer-when-downgrade"
          title="موقع مكتبة أنوار دارس على الخريطة"
        />
      </div>
      
      {/* معلومات الموقع وأزرار التنقل */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="font-semibold text-gray-800 mb-2 flex items-center gap-2">
            <MapPin className="w-4 h-4" />
            العنوان
          </h3>
          <p className="text-gray-600 text-sm">{address}</p>
          <p className="text-gray-500 text-xs mt-2">صنعاء، اليمن</p>
          {/* الإحداثيات - للمدير فقط */}
          {isAdmin && (
            <div className="mt-3 text-xs text-gray-500">
              <p>📍 الإحداثيات: {coordinates.lat}, {coordinates.lng}</p>
            </div>
          )}
        </div>
        
        <div className="flex flex-col gap-2">
          <h3 className="font-semibold text-gray-800 mb-2 flex items-center gap-2">
            <Navigation className="w-4 h-4" />
            التنقل والإرشاد
          </h3>
          <div className="flex flex-wrap gap-2">
            <a
              href={`https://www.google.com/maps/search/مكتبة+أنوار+دارس+صنعاء/@${coordinates.lat},${coordinates.lng},15z`}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-2 bg-blue-600 text-white px-3 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm"
            >
              <ExternalLink className="w-4 h-4" />
              جوجل مابس
            </a>
            
            <a
              href={`https://maps.apple.com/?q=${coordinates.lat},${coordinates.lng}`}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-2 bg-gray-800 text-white px-3 py-2 rounded-lg hover:bg-gray-900 transition-colors text-sm"
            >
              <ExternalLink className="w-4 h-4" />
              آبل مابس
            </a>
            
            {whatsapp && (
              <a
                href={`https://wa.me/${whatsapp.replace(/[^0-9]/g, '')}?text=مرحباً، أريد معرفة طريق الوصول إليكم. الموقع: ${coordinates.lat},${coordinates.lng}`}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-2 bg-green-600 text-white px-3 py-2 rounded-lg hover:bg-green-700 transition-colors text-sm"
              >
                <MessageCircle className="w-4 h-4" />
                اسأل عن الطريق
              </a>
            )}
          </div>
          
          {/* أداة تخصيص الخريطة المتقدمة - للمدير فقط */}
          {isAdmin && (
            <div className="mt-2">
              <button
                onClick={() => window.open('/map-customizer.html', '_blank')}
                className="flex items-center gap-2 bg-indigo-600 text-white px-3 py-2 rounded-lg hover:bg-indigo-700 transition-colors text-sm w-full justify-center"
              >
                <Settings className="w-4 h-4" />
                أداة تخصيص الخريطة المتقدمة
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default InteractiveMap;
