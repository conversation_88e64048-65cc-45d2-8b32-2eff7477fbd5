'use client';

import React, { useState } from 'react';
import { useParams } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { useAdmin } from '@/contexts/AdminContext';
import { useCart } from '@/contexts/CartContext';
import { useToast } from '@/components/ToastContainer';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import ProductCard from '@/components/ProductCard';
import { 
  ShoppingCart, 
  Heart, 
  Share2, 
  Star, 
  Truck, 
  Shield, 
  RotateCcw,
  Plus,
  Minus,
  ArrowRight
} from 'lucide-react';

export default function ProductPage() {
  const params = useParams();
  const productId = params.id as string;
  const { products, categories } = useAdmin();
  const { addToCart, isInCart } = useCart();
  const { showSuccess, showError } = useToast();
  
  const [quantity, setQuantity] = useState(1);
  const [selectedImage, setSelectedImage] = useState(0);

  const product = products.find(p => p.id === productId);
  
  if (!product) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <main className="container mx-auto px-4 py-16 text-center">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">المنتج غير موجود</h1>
          <p className="text-gray-600 mb-8">عذراً، لم يتم العثور على المنتج المطلوب</p>
          <Link
            href="/products"
            className="bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors inline-flex items-center"
          >
            <ArrowRight className="w-5 h-5 ml-2" />
            العودة للمنتجات
          </Link>
        </main>
        <Footer />
      </div>
    );
  }

  const category = categories.find(cat => cat.id === product.category);
  const relatedProducts = products
    .filter(p => p.category === product.category && p.id !== product.id)
    .slice(0, 4);

  const handleAddToCart = () => {
    if (!product.inStock) {
      showError('غير متوفر', 'عذراً، هذا المنتج غير متوفر حالياً');
      return;
    }

    try {
      addToCart(product, quantity);
      showSuccess('تمت الإضافة!', `تم إضافة ${quantity} من "${product.name}" إلى سلة التسوق`);
    } catch (error) {
      console.error('خطأ في إضافة المنتج إلى السلة:', error);
      showError('خطأ', 'حدث خطأ أثناء إضافة المنتج إلى السلة');
    }
  };

  const handleQuantityChange = (newQuantity: number) => {
    if (newQuantity >= 1 && newQuantity <= product.quantity) {
      setQuantity(newQuantity);
    }
  };

  const discountPercentage = product.originalPrice 
    ? Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)
    : 0;

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <nav className="mb-8">
          <div className="flex items-center space-x-2 space-x-reverse text-sm text-gray-600">
            <Link href="/" className="hover:text-primary-600">الرئيسية</Link>
            <span>/</span>
            <Link href="/products" className="hover:text-primary-600">المنتجات</Link>
            <span>/</span>
            <Link 
              href={`/category/${product.category}`} 
              className="hover:text-primary-600"
            >
              {category?.name}
            </Link>
            <span>/</span>
            <span className="text-gray-800">{product.name}</span>
          </div>
        </nav>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
          {/* Product Images */}
          <div className="space-y-4">
            {/* Main Image */}
            <div className="relative aspect-square bg-white rounded-lg overflow-hidden shadow-md">
              <Image
                src={product.image}
                alt={product.name}
                fill
                className="object-cover"
                sizes="(max-width: 768px) 100vw, 50vw"
              />
              
              {/* Badges */}
              <div className="absolute top-4 right-4 space-y-2">
                {discountPercentage > 0 && (
                  <div className="bg-red-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                    -{discountPercentage}%
                  </div>
                )}
                {product.featured && (
                  <div className="bg-yellow-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                    مميز
                  </div>
                )}
              </div>

              {/* Action Buttons */}
              <div className="absolute top-4 left-4 space-y-2">
                <button className="bg-white p-2 rounded-full shadow-md hover:bg-gray-100 transition-colors">
                  <Heart className="w-5 h-5 text-gray-600" />
                </button>
                <button className="bg-white p-2 rounded-full shadow-md hover:bg-gray-100 transition-colors">
                  <Share2 className="w-5 h-5 text-gray-600" />
                </button>
              </div>
            </div>

            {/* Thumbnail Images (if available) */}
            {product.images && product.images.length > 1 && (
              <div className="flex space-x-2 space-x-reverse">
                {product.images.map((image, index) => (
                  <button
                    key={index}
                    onClick={() => setSelectedImage(index)}
                    className={`relative w-20 h-20 rounded-lg overflow-hidden border-2 ${
                      selectedImage === index ? 'border-primary-600' : 'border-gray-200'
                    }`}
                  >
                    <Image
                      src={image}
                      alt={`${product.name} ${index + 1}`}
                      fill
                      className="object-cover"
                      sizes="80px"
                    />
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Product Info */}
          <div className="space-y-6">
            {/* Brand */}
            {product.brand && (
              <div className="text-sm text-gray-500 uppercase font-medium">
                {product.brand}
              </div>
            )}

            {/* Product Name */}
            <h1 className="text-3xl font-bold text-gray-800">{product.name}</h1>

            {/* Rating */}
            {product.rating && (
              <div className="flex items-center space-x-2 space-x-reverse">
                <div className="flex">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`w-5 h-5 ${
                        i < Math.floor(product.rating!)
                          ? 'text-yellow-400 fill-current'
                          : 'text-gray-300'
                      }`}
                    />
                  ))}
                </div>
                <span className="text-sm text-gray-600">
                  ({product.reviews || 0} تقييم)
                </span>
              </div>
            )}

            {/* Price */}
            <div className="space-y-2">
              <div className="flex items-center space-x-3 space-x-reverse">
                <span className="text-3xl font-bold text-primary-600">
                  {product.price.toLocaleString()} ريال
                </span>
                {product.originalPrice && (
                  <span className="text-xl text-gray-500 line-through">
                    {product.originalPrice.toLocaleString()} ريال
                  </span>
                )}
              </div>
              {discountPercentage > 0 && (
                <p className="text-green-600 font-medium">
                  وفر {(product.originalPrice! - product.price).toLocaleString()} ريال
                </p>
              )}
            </div>

            {/* Stock Status */}
            <div className="flex items-center space-x-2 space-x-reverse">
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                product.inStock 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-red-100 text-red-800'
              }`}>
                {product.inStock ? 'متوفر' : 'نفدت الكمية'}
              </span>
              {product.inStock && product.quantity <= 5 && (
                <span className="text-orange-600 text-sm">
                  متبقي {product.quantity} قطع فقط
                </span>
              )}
            </div>

            {/* Description */}
            <div>
              <h3 className="font-semibold text-gray-800 mb-2">الوصف</h3>
              <p className="text-gray-600 leading-relaxed">{product.description}</p>
            </div>

            {/* Specifications */}
            {product.specifications && (
              <div>
                <h3 className="font-semibold text-gray-800 mb-3">المواصفات</h3>
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="grid grid-cols-1 gap-2">
                    {Object.entries(product.specifications).map(([key, value]) => (
                      <div key={key} className="flex justify-between py-1">
                        <span className="text-gray-600">{key}:</span>
                        <span className="font-medium">{value}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Quantity and Add to Cart */}
            {product.inStock && (
              <div className="space-y-4">
                <div className="flex items-center space-x-4 space-x-reverse">
                  <span className="font-medium">الكمية:</span>
                  <div className="flex items-center border border-gray-300 rounded-lg">
                    <button
                      onClick={() => handleQuantityChange(quantity - 1)}
                      className="p-2 hover:bg-gray-100 transition-colors"
                      disabled={quantity <= 1}
                    >
                      <Minus className="w-4 h-4" />
                    </button>
                    <span className="px-4 py-2 font-semibold">{quantity}</span>
                    <button
                      onClick={() => handleQuantityChange(quantity + 1)}
                      className="p-2 hover:bg-gray-100 transition-colors"
                      disabled={quantity >= product.quantity}
                    >
                      <Plus className="w-4 h-4" />
                    </button>
                  </div>
                </div>

                <button
                  onClick={handleAddToCart}
                  className="w-full bg-primary-600 text-white py-4 rounded-lg font-semibold hover:bg-primary-700 transition-colors flex items-center justify-center space-x-2 space-x-reverse"
                >
                  <ShoppingCart className="w-5 h-5" />
                  <span>
                    {isInCart(product.id) ? 'تمت الإضافة للسلة' : 'أضف للسلة'}
                  </span>
                </button>
              </div>
            )}

            {/* Features */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-6 border-t">
              <div className="flex items-center space-x-3 space-x-reverse">
                <div className="bg-blue-100 p-2 rounded-full">
                  <Truck className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <p className="font-medium text-sm">توصيل سريع</p>
                  <p className="text-xs text-gray-600">1-3 أيام عمل</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3 space-x-reverse">
                <div className="bg-green-100 p-2 rounded-full">
                  <Shield className="w-5 h-5 text-green-600" />
                </div>
                <div>
                  <p className="font-medium text-sm">ضمان الجودة</p>
                  <p className="text-xs text-gray-600">منتج أصلي</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3 space-x-reverse">
                <div className="bg-yellow-100 p-2 rounded-full">
                  <RotateCcw className="w-5 h-5 text-yellow-600" />
                </div>
                <div>
                  <p className="font-medium text-sm">إرجاع مجاني</p>
                  <p className="text-xs text-gray-600">خلال 7 أيام</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Related Products */}
        {relatedProducts.length > 0 && (
          <section>
            <div className="flex justify-between items-center mb-8">
              <h2 className="text-2xl font-bold text-gray-800">منتجات ذات صلة</h2>
              <Link
                href={`/category/${product.category}`}
                className="text-primary-600 hover:text-primary-700 font-medium flex items-center"
              >
                عرض الكل
                <ArrowRight className="w-4 h-4 mr-2" />
              </Link>
            </div>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {relatedProducts.map((relatedProduct) => (
                <ProductCard key={relatedProduct.id} product={relatedProduct} />
              ))}
            </div>
          </section>
        )}
      </main>

      <Footer />
    </div>
  );
}
