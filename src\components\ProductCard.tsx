'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Product } from '@/types';
import { useCart } from '@/contexts/CartContext';
import { useToast } from '@/components/ToastContainer';
import { ShoppingCart, Star, Heart, Eye } from 'lucide-react';

interface ProductCardProps {
  product: Product;
  className?: string;
}

const ProductCard: React.FC<ProductCardProps> = ({ product, className = '' }) => {
  const { addToCart, isInCart } = useCart();
  const { showSuccess, showError } = useToast();

  const handleAddToCart = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (!product.inStock) {
      showError('غير متوفر', 'عذراً، هذا المنتج غير متوفر حالياً');
      return;
    }

    try {
      addToCart(product);
      showSuccess('تمت الإضافة!', `تم إضافة "${product.name}" إلى سلة التسوق`);
    } catch (error) {
      console.error('خطأ في إضافة المنتج إلى السلة:', error);
      showError('خطأ', 'حدث خطأ أثناء إضافة المنتج إلى السلة');
    }
  };

  const discountPercentage = product.originalPrice 
    ? Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)
    : 0;

  return (
    <div className={`card p-4 group relative overflow-hidden ${className}`}>
      {/* Discount Badge */}
      {discountPercentage > 0 && (
        <div className="absolute top-2 right-2 bg-red-500 text-white px-2 py-1 rounded-full text-xs font-bold z-10">
          -{discountPercentage}%
        </div>
      )}

      {/* Featured Badge */}
      {product.featured && (
        <div className="absolute top-2 left-2 bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-bold z-10">
          مميز
        </div>
      )}

      {/* Product Image */}
      <Link href={`/product/${product.id}`}>
        <div className="relative aspect-square mb-4 overflow-hidden rounded-lg bg-gray-100">
          <img
            src={product.image}
            alt={product.name}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
            onError={(e) => {
              e.currentTarget.src = '/images/products/placeholder-default.svg';
            }}
          />
          
          {/* Overlay Actions */}
          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
            <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex space-x-2 space-x-reverse">
              <button className="bg-white p-2 rounded-full shadow-lg hover:bg-gray-100 transition-colors">
                <Eye className="w-4 h-4 text-gray-600" />
              </button>
              <button className="bg-white p-2 rounded-full shadow-lg hover:bg-gray-100 transition-colors">
                <Heart className="w-4 h-4 text-gray-600" />
              </button>
            </div>
          </div>

          {/* Out of Stock Overlay */}
          {!product.inStock && (
            <div className="absolute inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center">
              <span className="text-white font-semibold">نفدت الكمية</span>
            </div>
          )}
        </div>
      </Link>

      {/* Product Info */}
      <div className="space-y-2">
        {/* Brand */}
        {product.brand && (
          <p className="text-xs text-gray-500 uppercase">{product.brand}</p>
        )}

        {/* Product Name */}
        <Link href={`/product/${product.id}`}>
          <h3 className="font-semibold text-gray-800 hover:text-primary-600 transition-colors line-clamp-2">
            {product.name}
          </h3>
        </Link>

        {/* Rating */}
        {product.rating && (
          <div className="flex items-center space-x-1 space-x-reverse">
            <div className="flex">
              {[...Array(5)].map((_, i) => (
                <Star
                  key={i}
                  className={`w-4 h-4 ${
                    i < Math.floor(product.rating!)
                      ? 'text-yellow-400 fill-current'
                      : 'text-gray-300'
                  }`}
                />
              ))}
            </div>
            <span className="text-sm text-gray-500">
              ({product.reviews || 0})
            </span>
          </div>
        )}

        {/* Price */}
        <div className="flex items-center space-x-2 space-x-reverse">
          <span className="text-lg font-bold text-primary-600">
            {product.price.toLocaleString()} ريال
          </span>
          {product.originalPrice && (
            <span className="text-sm text-gray-500 line-through">
              {product.originalPrice.toLocaleString()} ريال
            </span>
          )}
        </div>

        {/* Stock Status */}
        <div className="flex items-center justify-between">
          <span className={`text-xs px-2 py-1 rounded-full ${
            product.inStock 
              ? 'bg-green-100 text-green-800' 
              : 'bg-red-100 text-red-800'
          }`}>
            {product.inStock ? 'متوفر' : 'نفدت الكمية'}
          </span>
          {product.inStock && product.quantity <= 5 && (
            <span className="text-xs text-orange-600">
              متبقي {product.quantity} قطع
            </span>
          )}
        </div>

        {/* Add to Cart Button */}
        <button
          onClick={handleAddToCart}
          disabled={!product.inStock}
          className={`w-full py-2 px-4 rounded-lg font-medium transition-all duration-200 flex items-center justify-center space-x-2 space-x-reverse ${
            product.inStock
              ? isInCart(product.id)
                ? 'bg-green-600 hover:bg-green-700 text-white'
                : 'bg-primary-600 hover:bg-primary-700 text-white'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
          }`}
        >
          <ShoppingCart className="w-4 h-4" />
          <span>
            {!product.inStock 
              ? 'نفدت الكمية'
              : isInCart(product.id)
                ? 'تمت الإضافة'
                : 'أضف للسلة'
            }
          </span>
        </button>
      </div>
    </div>
  );
};

export default ProductCard;
