'use client';

import React, { useState } from 'react';
import { useAdmin } from '@/contexts/AdminContext';
import { useToast } from '@/components/ToastContainer';
import { 
  Palette, 
  Type, 
  Layout, 
  Zap, 
  Save, 
  RotateCcw,
  Eye,
  Monitor,
  Smartphone,
  Tablet
} from 'lucide-react';

const DesignSettings: React.FC = () => {
  const { settings, updateSettings } = useAdmin();
  const { showSuccess, showError } = useToast();

  const [activeTab, setActiveTab] = useState<'typography' | 'colors' | 'layout' | 'animations'>('typography');
  const [previewMode, setPreviewMode] = useState<'desktop' | 'tablet' | 'mobile'>('desktop');

  // Default design settings if not available
  const defaultDesignSettings = {
    typography: {
      fontFamily: 'Cairo, system-ui, -apple-system, sans-serif',
      fontSize: {
        small: '0.875rem',
        medium: '1rem',
        large: '1.25rem',
        xlarge: '1.5rem'
      },
      fontWeight: {
        light: '300',
        normal: '400',
        medium: '500',
        bold: '700'
      },
      lineHeight: '1.6',
      letterSpacing: '0.025em'
    },
    layout: {
      borderRadius: '0.5rem',
      spacing: '1rem',
      containerMaxWidth: '1200px',
      cardShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
      borderWidth: '1px'
    },
    colors: {
      primary: '#2563eb',
      secondary: '#64748b',
      accent: '#d946ef',
      success: '#10b981',
      warning: '#f59e0b',
      error: '#ef4444',
      info: '#3b82f6',
      background: '#f8fafc',
      surface: '#ffffff',
      text: {
        primary: '#1f2937',
        secondary: '#6b7280',
        muted: '#9ca3af',
        inverse: '#ffffff'
      },
      border: '#e5e7eb'
    },
    animations: {
      duration: '300ms',
      easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
      hoverScale: '1.05'
    }
  };

  const [designSettings, setDesignSettings] = useState(
    settings?.designSettings || defaultDesignSettings
  );

  const handleSave = () => {
    try {
      updateSettings({ designSettings });
      showSuccess('تم الحفظ!', 'تم حفظ إعدادات التصميم بنجاح');
    } catch (error) {
      showError('خطأ', 'حدث خطأ أثناء حفظ الإعدادات');
    }
  };

  const handleReset = () => {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع إعدادات التصميم؟')) {
      setDesignSettings(settings.designSettings);
      showSuccess('تم الإعادة', 'تم إعادة تعيين إعدادات التصميم');
    }
  };

  const updateTypography = (field: string, value: string) => {
    setDesignSettings(prev => ({
      ...prev,
      typography: {
        ...prev.typography,
        [field]: value
      }
    }));
  };

  const updateFontSize = (size: string, value: string) => {
    setDesignSettings(prev => ({
      ...prev,
      typography: {
        ...prev.typography,
        fontSize: {
          ...prev.typography.fontSize,
          [size]: value
        }
      }
    }));
  };

  const updateFontWeight = (weight: string, value: string) => {
    setDesignSettings(prev => ({
      ...prev,
      typography: {
        ...prev.typography,
        fontWeight: {
          ...prev.typography.fontWeight,
          [weight]: value
        }
      }
    }));
  };

  const updateColors = (field: string, value: string) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setDesignSettings(prev => ({
        ...prev,
        colors: {
          ...prev.colors,
          [parent]: {
            ...(prev.colors[parent as keyof typeof prev.colors] as any),
            [child]: value
          }
        }
      }));
    } else {
      setDesignSettings(prev => ({
        ...prev,
        colors: {
          ...prev.colors,
          [field]: value
        }
      }));
    }
  };

  const updateLayout = (field: string, value: string) => {
    setDesignSettings(prev => ({
      ...prev,
      layout: {
        ...prev.layout,
        [field]: value
      }
    }));
  };

  const updateAnimations = (field: string, value: string) => {
    setDesignSettings(prev => ({
      ...prev,
      animations: {
        ...prev.animations,
        [field]: value
      }
    }));
  };

  const fontFamilies = [
    'Cairo, system-ui, -apple-system, sans-serif',
    'Tajawal, system-ui, -apple-system, sans-serif',
    'Amiri, serif',
    'Noto Sans Arabic, sans-serif',
    'IBM Plex Sans Arabic, sans-serif',
    'Almarai, sans-serif',
    'Markazi Text, serif',
    'system-ui, -apple-system, sans-serif',
    'Georgia, serif',
    'Times New Roman, serif'
  ];

  const colorPresets = [
    { name: 'الأزرق الكلاسيكي', primary: '#2563eb', secondary: '#64748b', accent: '#d946ef' },
    { name: 'الأخضر الطبيعي', primary: '#059669', secondary: '#6b7280', accent: '#f59e0b' },
    { name: 'البرتقالي الدافئ', primary: '#ea580c', secondary: '#64748b', accent: '#8b5cf6' },
    { name: 'الأحمر الجريء', primary: '#dc2626', secondary: '#6b7280', accent: '#06b6d4' },
    { name: 'البنفسجي الأنيق', primary: '#7c3aed', secondary: '#64748b', accent: '#10b981' },
    { name: 'الرمادي المحايد', primary: '#374151', secondary: '#6b7280', accent: '#f59e0b' }
  ];

  const tabs = [
    { id: 'typography', name: 'الخطوط والنصوص', icon: Type },
    { id: 'colors', name: 'الألوان', icon: Palette },
    { id: 'layout', name: 'التخطيط والأشكال', icon: Layout },
    { id: 'animations', name: 'الحركات والتأثيرات', icon: Zap }
  ];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-semibold">إعدادات التصميم والمظهر</h2>
        <div className="flex space-x-4 space-x-reverse">
          <button
            onClick={handleReset}
            className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors flex items-center"
          >
            <RotateCcw className="w-4 h-4 ml-2" />
            إعادة تعيين
          </button>
          <button
            onClick={handleSave}
            className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors flex items-center"
          >
            <Save className="w-4 h-4 ml-2" />
            حفظ التغييرات
          </button>
        </div>
      </div>

      {/* Preview Mode Selector */}
      <div className="bg-white rounded-lg shadow-md p-4">
        <div className="flex items-center justify-between">
          <h3 className="font-semibold flex items-center">
            <Eye className="w-5 h-5 ml-2" />
            معاينة التصميم
          </h3>
          <div className="flex space-x-2 space-x-reverse">
            {[
              { id: 'desktop', icon: Monitor, name: 'سطح المكتب' },
              { id: 'tablet', icon: Tablet, name: 'تابلت' },
              { id: 'mobile', icon: Smartphone, name: 'موبايل' }
            ].map(({ id, icon: Icon, name }) => (
              <button
                key={id}
                onClick={() => setPreviewMode(id as any)}
                className={`p-2 rounded-lg transition-colors ${
                  previewMode === id 
                    ? 'bg-primary-100 text-primary-700' 
                    : 'text-gray-600 hover:bg-gray-100'
                }`}
                title={name}
              >
                <Icon className="w-4 h-4" />
              </button>
            ))}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Tabs */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow-md p-4">
            <h3 className="font-semibold mb-4">أقسام التصميم</h3>
            <div className="space-y-2">
              {tabs.map(({ id, name, icon: Icon }) => (
                <button
                  key={id}
                  onClick={() => setActiveTab(id as any)}
                  className={`w-full text-right p-3 rounded-lg transition-colors flex items-center ${
                    activeTab === id
                      ? 'bg-primary-100 text-primary-700 border border-primary-200'
                      : 'text-gray-600 hover:bg-gray-50'
                  }`}
                >
                  <Icon className="w-4 h-4 ml-3" />
                  {name}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="lg:col-span-3">
          <div className="bg-white rounded-lg shadow-md p-6">
            {/* Typography Tab */}
            {activeTab === 'typography' && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold mb-4">إعدادات الخطوط والنصوص</h3>
                
                {/* Font Family */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    نوع الخط
                  </label>
                  <select
                    value={designSettings.typography.fontFamily}
                    onChange={(e) => updateTypography('fontFamily', e.target.value)}
                    className="input-field"
                  >
                    {fontFamilies.map((font) => (
                      <option key={font} value={font} style={{ fontFamily: font }}>
                        {font.split(',')[0]}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Font Sizes */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    أحجام الخط
                  </label>
                  <div className="grid grid-cols-2 gap-4">
                    {Object.entries(designSettings.typography.fontSize).map(([size, value]) => (
                      <div key={size}>
                        <label className="block text-xs text-gray-600 mb-1">
                          {size === 'small' ? 'صغير' : 
                           size === 'medium' ? 'متوسط' : 
                           size === 'large' ? 'كبير' : 'كبير جداً'}
                        </label>
                        <input
                          type="text"
                          value={value}
                          onChange={(e) => updateFontSize(size, e.target.value)}
                          className="input-field text-sm"
                          placeholder="1rem"
                        />
                      </div>
                    ))}
                  </div>
                </div>

                {/* Font Weights */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    أوزان الخط
                  </label>
                  <div className="grid grid-cols-2 gap-4">
                    {Object.entries(designSettings.typography.fontWeight).map(([weight, value]) => (
                      <div key={weight}>
                        <label className="block text-xs text-gray-600 mb-1">
                          {weight === 'light' ? 'خفيف' : 
                           weight === 'normal' ? 'عادي' : 
                           weight === 'medium' ? 'متوسط' : 'عريض'}
                        </label>
                        <input
                          type="text"
                          value={value}
                          onChange={(e) => updateFontWeight(weight, e.target.value)}
                          className="input-field text-sm"
                          placeholder="400"
                        />
                      </div>
                    ))}
                  </div>
                </div>

                {/* Line Height & Letter Spacing */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      ارتفاع السطر
                    </label>
                    <input
                      type="text"
                      value={designSettings.typography.lineHeight}
                      onChange={(e) => updateTypography('lineHeight', e.target.value)}
                      className="input-field"
                      placeholder="1.6"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      تباعد الأحرف
                    </label>
                    <input
                      type="text"
                      value={designSettings.typography.letterSpacing}
                      onChange={(e) => updateTypography('letterSpacing', e.target.value)}
                      className="input-field"
                      placeholder="0.025em"
                    />
                  </div>
                </div>

                {/* Preview */}
                <div className="border rounded-lg p-4 bg-gray-50">
                  <h4 className="text-sm font-medium text-gray-700 mb-3">معاينة النص</h4>
                  <div 
                    style={{
                      fontFamily: designSettings.typography.fontFamily,
                      lineHeight: designSettings.typography.lineHeight,
                      letterSpacing: designSettings.typography.letterSpacing
                    }}
                    className="space-y-2"
                  >
                    <p style={{ fontSize: designSettings.typography.fontSize.xlarge, fontWeight: designSettings.typography.fontWeight.bold }}>
                      عنوان رئيسي كبير
                    </p>
                    <p style={{ fontSize: designSettings.typography.fontSize.large, fontWeight: designSettings.typography.fontWeight.medium }}>
                      عنوان فرعي متوسط
                    </p>
                    <p style={{ fontSize: designSettings.typography.fontSize.medium, fontWeight: designSettings.typography.fontWeight.normal }}>
                      نص عادي للمحتوى والفقرات الطويلة التي تحتوي على معلومات مفصلة
                    </p>
                    <p style={{ fontSize: designSettings.typography.fontSize.small, fontWeight: designSettings.typography.fontWeight.light }}>
                      نص صغير للملاحظات والتفاصيل الإضافية
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Colors Tab */}
            {activeTab === 'colors' && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold mb-4">إعدادات الألوان</h3>
                
                {/* Color Presets */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    مجموعات ألوان جاهزة
                  </label>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    {colorPresets.map((preset) => (
                      <button
                        key={preset.name}
                        onClick={() => {
                          updateColors('primary', preset.primary);
                          updateColors('secondary', preset.secondary);
                          updateColors('accent', preset.accent);
                        }}
                        className="p-3 border rounded-lg hover:border-gray-400 transition-colors text-right"
                      >
                        <div className="flex space-x-2 space-x-reverse mb-2">
                          <div 
                            className="w-4 h-4 rounded-full" 
                            style={{ backgroundColor: preset.primary }}
                          />
                          <div 
                            className="w-4 h-4 rounded-full" 
                            style={{ backgroundColor: preset.secondary }}
                          />
                          <div 
                            className="w-4 h-4 rounded-full" 
                            style={{ backgroundColor: preset.accent }}
                          />
                        </div>
                        <div className="text-sm font-medium">{preset.name}</div>
                      </button>
                    ))}
                  </div>
                </div>

                {/* Main Colors */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    الألوان الأساسية
                  </label>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {[
                      { key: 'primary', label: 'اللون الأساسي', value: designSettings.colors.primary },
                      { key: 'secondary', label: 'اللون الثانوي', value: designSettings.colors.secondary },
                      { key: 'accent', label: 'لون التمييز', value: designSettings.colors.accent }
                    ].map(({ key, label, value }) => (
                      <div key={key}>
                        <label className="block text-xs text-gray-600 mb-1">{label}</label>
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <input
                            type="color"
                            value={value}
                            onChange={(e) => updateColors(key, e.target.value)}
                            className="w-10 h-10 rounded border border-gray-300"
                          />
                          <input
                            type="text"
                            value={value}
                            onChange={(e) => updateColors(key, e.target.value)}
                            className="input-field flex-1 text-sm"
                            placeholder="#000000"
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Status Colors */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    ألوان الحالة
                  </label>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    {[
                      { key: 'success', label: 'نجاح', value: designSettings.colors.success },
                      { key: 'warning', label: 'تحذير', value: designSettings.colors.warning },
                      { key: 'error', label: 'خطأ', value: designSettings.colors.error },
                      { key: 'info', label: 'معلومات', value: designSettings.colors.info }
                    ].map(({ key, label, value }) => (
                      <div key={key}>
                        <label className="block text-xs text-gray-600 mb-1">{label}</label>
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <input
                            type="color"
                            value={value}
                            onChange={(e) => updateColors(key, e.target.value)}
                            className="w-8 h-8 rounded border border-gray-300"
                          />
                          <input
                            type="text"
                            value={value}
                            onChange={(e) => updateColors(key, e.target.value)}
                            className="input-field flex-1 text-xs"
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Text Colors */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    ألوان النصوص
                  </label>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    {[
                      { key: 'text.primary', label: 'نص أساسي', value: designSettings.colors.text.primary },
                      { key: 'text.secondary', label: 'نص ثانوي', value: designSettings.colors.text.secondary },
                      { key: 'text.muted', label: 'نص خافت', value: designSettings.colors.text.muted },
                      { key: 'text.inverse', label: 'نص معكوس', value: designSettings.colors.text.inverse }
                    ].map(({ key, label, value }) => (
                      <div key={key}>
                        <label className="block text-xs text-gray-600 mb-1">{label}</label>
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <input
                            type="color"
                            value={value}
                            onChange={(e) => updateColors(key, e.target.value)}
                            className="w-8 h-8 rounded border border-gray-300"
                          />
                          <input
                            type="text"
                            value={value}
                            onChange={(e) => updateColors(key, e.target.value)}
                            className="input-field flex-1 text-xs"
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Background Colors */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    ألوان الخلفيات
                  </label>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {[
                      { key: 'background', label: 'خلفية الصفحة', value: designSettings.colors.background },
                      { key: 'surface', label: 'خلفية المكونات', value: designSettings.colors.surface },
                      { key: 'border', label: 'لون الحدود', value: designSettings.colors.border }
                    ].map(({ key, label, value }) => (
                      <div key={key}>
                        <label className="block text-xs text-gray-600 mb-1">{label}</label>
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <input
                            type="color"
                            value={value}
                            onChange={(e) => updateColors(key, e.target.value)}
                            className="w-10 h-10 rounded border border-gray-300"
                          />
                          <input
                            type="text"
                            value={value}
                            onChange={(e) => updateColors(key, e.target.value)}
                            className="input-field flex-1 text-sm"
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Layout Tab */}
            {activeTab === 'layout' && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold mb-4">إعدادات التخطيط والأشكال</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {[
                    { key: 'borderRadius', label: 'انحناء الزوايا', value: designSettings.layout.borderRadius, placeholder: '0.5rem' },
                    { key: 'spacing', label: 'التباعد العام', value: designSettings.layout.spacing, placeholder: '1rem' },
                    { key: 'containerMaxWidth', label: 'عرض الحاوية الأقصى', value: designSettings.layout.containerMaxWidth, placeholder: '1200px' },
                    { key: 'cardShadow', label: 'ظل البطاقات', value: designSettings.layout.cardShadow, placeholder: '0 4px 6px rgba(0,0,0,0.1)' },
                    { key: 'borderWidth', label: 'سمك الحدود', value: designSettings.layout.borderWidth, placeholder: '1px' }
                  ].map(({ key, label, value, placeholder }) => (
                    <div key={key}>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        {label}
                      </label>
                      <input
                        type="text"
                        value={value}
                        onChange={(e) => updateLayout(key, e.target.value)}
                        className="input-field"
                        placeholder={placeholder}
                      />
                    </div>
                  ))}
                </div>

                {/* Layout Preview */}
                <div className="border rounded-lg p-4 bg-gray-50">
                  <h4 className="text-sm font-medium text-gray-700 mb-3">معاينة التخطيط</h4>
                  <div className="space-y-4">
                    <div 
                      className="bg-white p-4 border"
                      style={{
                        borderRadius: designSettings.layout.borderRadius,
                        boxShadow: designSettings.layout.cardShadow,
                        borderWidth: designSettings.layout.borderWidth,
                        borderColor: designSettings.colors.border
                      }}
                    >
                      <h5 className="font-medium mb-2">بطاقة تجريبية</h5>
                      <p className="text-sm text-gray-600">
                        هذا مثال على شكل البطاقات مع الإعدادات الحالية
                      </p>
                    </div>
                    <div className="flex space-x-2 space-x-reverse">
                      <button 
                        className="px-4 py-2 bg-primary-600 text-white"
                        style={{ borderRadius: designSettings.layout.borderRadius }}
                      >
                        زر أساسي
                      </button>
                      <button 
                        className="px-4 py-2 border border-gray-300"
                        style={{ 
                          borderRadius: designSettings.layout.borderRadius,
                          borderWidth: designSettings.layout.borderWidth 
                        }}
                      >
                        زر ثانوي
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Animations Tab */}
            {activeTab === 'animations' && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold mb-4">إعدادات الحركات والتأثيرات</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {[
                    { key: 'duration', label: 'مدة الحركة', value: designSettings.animations.duration, placeholder: '300ms' },
                    { key: 'easing', label: 'نوع الحركة', value: designSettings.animations.easing, placeholder: 'ease-in-out' },
                    { key: 'hoverScale', label: 'تكبير عند التمرير', value: designSettings.animations.hoverScale, placeholder: '1.05' }
                  ].map(({ key, label, value, placeholder }) => (
                    <div key={key}>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        {label}
                      </label>
                      <input
                        type="text"
                        value={value}
                        onChange={(e) => updateAnimations(key, e.target.value)}
                        className="input-field"
                        placeholder={placeholder}
                      />
                    </div>
                  ))}
                </div>

                {/* Animation Preview */}
                <div className="border rounded-lg p-4 bg-gray-50">
                  <h4 className="text-sm font-medium text-gray-700 mb-3">معاينة الحركات</h4>
                  <div className="space-y-4">
                    <div 
                      className="bg-white p-4 border cursor-pointer transition-transform"
                      style={{
                        borderRadius: designSettings.layout.borderRadius,
                        transitionDuration: designSettings.animations.duration,
                        transitionTimingFunction: designSettings.animations.easing
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.transform = `scale(${designSettings.animations.hoverScale})`;
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.transform = 'scale(1)';
                      }}
                    >
                      <h5 className="font-medium mb-2">عنصر تفاعلي</h5>
                      <p className="text-sm text-gray-600">
                        مرر الماوس فوق هذا العنصر لرؤية التأثير
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DesignSettings;
