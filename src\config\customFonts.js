// ملف إعداد الخطوط المخصصة لـ Tailwind CSS
// Custom Fonts Configuration for Tailwind CSS

// هذا الملف يحتوي على إعدادات الخطوط المخصصة
// يمكنك إضافة خطوطك المخصصة هنا واستخدامها في Tailwind

export const customFonts = {
  // الخطوط العربية المخصصة
  arabic: {
    // خط عربي تقليدي
    'amiri': ['Amiri', 'serif'],
    // خط عربي حديث
    'cairo': ['Cairo', 'sans-serif'],
    // خط عربي عصري
    'tajawal': ['Tajawal', 'sans-serif'],
    // خط عربي أنيق
    'almarai': ['Almarai', 'sans-serif'],
    // خط مخصص (استبدل بخطك)
    'custom-arabic': ['CustomArabicFont', 'Cairo', 'sans-serif'],
  },

  // الخطوط الإنجليزية المخصصة
  english: {
    // خط إنجليزي حديث
    'inter': ['Inter', 'sans-serif'],
    // خط إنجليزي كلاسيكي
    'roboto': ['Roboto', 'sans-serif'],
    // خط إنجليزي أنيق
    'poppins': ['Poppins', 'sans-serif'],
    // خط مخصص (استبدل بخطك)
    'custom-english': ['CustomEnglishFont', 'Inter', 'sans-serif'],
  },

  // خطوط للعناوين
  headings: {
    // خط للعناوين الرئيسية
    'main-heading': ['Cairo', 'Amiri', 'serif'],
    // خط للعناوين الفرعية
    'sub-heading': ['Tajawal', 'Cairo', 'sans-serif'],
    // خط مخصص للعناوين
    'custom-heading': ['CustomHeadingFont', 'Cairo', 'sans-serif'],
  },

  // خطوط للنصوص
  body: {
    // خط للنصوص العادية
    'main-text': ['Cairo', 'sans-serif'],
    // خط للنصوص الصغيرة
    'small-text': ['Almarai', 'Cairo', 'sans-serif'],
    // خط مخصص للنصوص
    'custom-body': ['CustomBodyFont', 'Cairo', 'sans-serif'],
  },

  // خطوط خاصة
  special: {
    // خط للشعار
    'logo': ['CustomLogoFont', 'Cairo', 'sans-serif'],
    // خط للأرقام
    'numbers': ['Roboto', 'monospace'],
    // خط للأكواد
    'code': ['Fira Code', 'Consolas', 'monospace'],
  }
};

// دالة لتحويل إعدادات الخطوط إلى تنسيق Tailwind
export const getTailwindFontConfig = () => {
  const fontConfig = {};
  
  // دمج جميع فئات الخطوط
  Object.keys(customFonts).forEach(category => {
    Object.keys(customFonts[category]).forEach(fontName => {
      fontConfig[fontName] = customFonts[category][fontName];
    });
  });

  return fontConfig;
};

// إعدادات الخطوط الافتراضية للموقع
export const defaultFontSettings = {
  // الخط الافتراضي للعناوين الرئيسية
  primaryHeading: 'font-cairo',
  // الخط الافتراضي للعناوين الفرعية
  secondaryHeading: 'font-tajawal',
  // الخط الافتراضي للنصوص
  bodyText: 'font-cairo',
  // الخط الافتراضي للنصوص الصغيرة
  smallText: 'font-almarai',
  // الخط الافتراضي للأرقام
  numbers: 'font-numbers',
};

// أمثلة على كيفية الاستخدام في المكونات
export const fontExamples = {
  // استخدام في JSX
  jsx: `
    // العناوين الرئيسية
    <h1 className="font-cairo text-3xl font-bold">عنوان رئيسي</h1>
    
    // العناوين الفرعية
    <h2 className="font-tajawal text-xl font-semibold">عنوان فرعي</h2>
    
    // النصوص العادية
    <p className="font-cairo text-base">نص عادي</p>
    
    // النصوص الصغيرة
    <span className="font-almarai text-sm">نص صغير</span>
    
    // الأرقام
    <div className="font-numbers text-lg">123,456</div>
    
    // خط مخصص
    <div className="font-custom-arabic text-2xl">نص بخط مخصص</div>
  `,

  // استخدام في CSS
  css: `
    /* العناوين الرئيسية */
    .main-title {
      font-family: 'Cairo', sans-serif;
      font-weight: 700;
    }
    
    /* النصوص العادية */
    .body-text {
      font-family: 'Cairo', sans-serif;
      font-weight: 400;
    }
    
    /* خط مخصص */
    .custom-text {
      font-family: 'CustomArabicFont', 'Cairo', sans-serif;
    }
  `
};

// تصدير الإعدادات للاستخدام في tailwind.config.js
export default {
  customFonts,
  getTailwindFontConfig,
  defaultFontSettings,
  fontExamples
};
