'use client';

import React from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import { useAdmin } from '@/contexts/AdminContext';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import ProductCard from '@/components/ProductCard';
import { ArrowRight, Package } from 'lucide-react';

export default function CategoryPage() {
  const params = useParams();
  const categoryId = params.id as string;
  const { products, categories } = useAdmin();
  
  const category = categories.find(cat => cat.id === categoryId);
  const categoryProducts = products.filter(product => product.category === categoryId);
  
  if (!category) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <main className="container mx-auto px-4 py-16 text-center">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">الفئة غير موجودة</h1>
          <p className="text-gray-600 mb-8">عذراً، لم يتم العثور على الفئة المطلوبة</p>
          <Link
            href="/products"
            className="bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors inline-flex items-center"
          >
            <ArrowRight className="w-5 h-5 ml-2" />
            العودة للمنتجات
          </Link>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <nav className="mb-8">
          <div className="flex items-center space-x-2 space-x-reverse text-sm text-gray-600">
            <Link href="/" className="hover:text-primary-600">الرئيسية</Link>
            <span>/</span>
            <Link href="/products" className="hover:text-primary-600">المنتجات</Link>
            <span>/</span>
            <span className="text-gray-800">{category.name}</span>
          </div>
        </nav>

        {/* Category Header */}
        <div className="bg-white rounded-lg shadow-md p-8 mb-8">
          <div className="flex items-center mb-4">
            <div className="text-4xl ml-4">{category.icon}</div>
            <div>
              <h1 className="text-3xl font-bold text-gray-800">{category.name}</h1>
              {category.description && (
                <p className="text-gray-600 mt-2">{category.description}</p>
              )}
            </div>
          </div>
          
          <div className="flex items-center text-sm text-gray-600">
            <Package className="w-4 h-4 ml-2" />
            <span>{categoryProducts.length} منتج متاح</span>
          </div>
        </div>

        {/* Subcategories */}
        {category.subcategories && category.subcategories.length > 0 && (
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">الفئات الفرعية</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {category.subcategories.map((subcategory) => {
                const subcategoryProducts = products.filter(
                  product => product.subcategory === subcategory.id
                );
                
                return (
                  <Link
                    key={subcategory.id}
                    href={`/products?subcategory=${subcategory.id}`}
                    className="bg-white rounded-lg shadow-md p-4 hover:shadow-lg transition-shadow text-center"
                  >
                    <h3 className="font-semibold text-gray-800 mb-2">{subcategory.name}</h3>
                    <p className="text-sm text-gray-600">
                      {subcategoryProducts.length} منتج
                    </p>
                  </Link>
                );
              })}
            </div>
          </div>
        )}

        {/* Products */}
        {categoryProducts.length === 0 ? (
          <div className="bg-white rounded-lg shadow-md p-12 text-center">
            <div className="text-gray-400 mb-4">
              <Package className="w-16 h-16 mx-auto" />
            </div>
            <h3 className="text-xl font-semibold text-gray-800 mb-2">لا توجد منتجات</h3>
            <p className="text-gray-600 mb-6">لا توجد منتجات في هذه الفئة حالياً</p>
            <Link
              href="/products"
              className="bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors inline-flex items-center"
            >
              <ArrowRight className="w-5 h-5 ml-2" />
              تصفح جميع المنتجات
            </Link>
          </div>
        ) : (
          <>
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-semibold text-gray-800">
                منتجات {category.name}
              </h2>
              <p className="text-gray-600">
                {categoryProducts.length} منتج
              </p>
            </div>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {categoryProducts.map((product) => (
                <ProductCard key={product.id} product={product} />
              ))}
            </div>
          </>
        )}
      </main>

      <Footer />
    </div>
  );
}
