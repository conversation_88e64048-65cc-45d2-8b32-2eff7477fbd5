🚀 تعليمات تشغيل متجر اليمن الإلكتروني
==========================================

📋 المتطلبات الأساسية:
-----------------------
1. Node.js (الإصدار 18 أو أحدث)
2. اتصال إنترنت (لتحميل المكتبات)
3. 2 جيجابايت مساحة فارغة على القرص
4. 4 جيجابايت RAM على الأقل

🔧 خطوات التثبيت:
------------------

الخطوة 1: تثبيت Node.js
------------------------
1. اذهب إلى الموقع: https://nodejs.org
2. حمل النسخة LTS (Long Term Support)
3. شغ<PERSON> الملف المحمل واتبع التعليمات
4. أعد تشغيل الكمبيوتر بعد التثبيت

الخطوة 2: فحص التثبيت
----------------------
1. افتح Command Prompt (اضغط Win + R واكتب cmd)
2. اكتب: node --version
3. اكتب: npm --version
4. يجب أن تظهر أرقام الإصدارات

الخطوة 3: تشغيل المشروع
------------------------
1. انقر مرتين على ملف "check-requirements.bat" للفحص
2. إذا كان كل شيء جاهز، انقر على "start.bat"
3. انتظر حتى يكتمل تحميل المكتبات
4. سيفتح الموقع تلقائياً على: http://localhost:3000

🔧 الطريقة اليدوية:
-------------------
1. افتح Command Prompt في مجلد المشروع
2. اكتب: npm install
3. انتظر حتى ينتهي التحميل
4. اكتب: npm run dev
5. افتح المتصفح على: http://localhost:3000

🔐 الوصول للوحة التحكم:
------------------------
- الرابط: http://localhost:3000/admin
- كلمة المرور: admin123

❌ حل المشاكل الشائعة:
-----------------------

مشكلة: "node is not recognized"
الحل: 
- تأكد من تثبيت Node.js
- أعد تشغيل Command Prompt
- أعد تشغيل الكمبيوتر

مشكلة: "npm install" بطيء جداً
الحل:
- تأكد من الاتصال بالإنترنت
- جرب: npm install --registry https://registry.npmmirror.com/

مشكلة: "Port 3000 is already in use"
الحل:
- أغلق أي برنامج يستخدم المنفذ 3000
- أو استخدم منفذ آخر: npm run dev -- -p 3001

مشكلة: الموقع لا يفتح
الحل:
- تأكد من أن الخادم يعمل (يجب أن ترى "Ready" في Command Prompt)
- جرب: http://127.0.0.1:3000 بدلاً من localhost

📞 للمساعدة:
--------------
إذا واجهت أي مشكلة، تأكد من:
1. تثبيت Node.js بشكل صحيح
2. وجود اتصال إنترنت مستقر
3. عدم وجود برامج حماية تمنع التشغيل
4. تشغيل Command Prompt كمدير (Run as Administrator)

🎯 نصائح مهمة:
---------------
- لا تغلق نافذة Command Prompt أثناء التشغيل
- للإيقاف: اضغط Ctrl + C في نافذة Command Prompt
- للتشغيل مرة أخرى: npm run dev
- البيانات محفوظة في المتصفح (localStorage)

✅ علامات نجاح التشغيل:
------------------------
- ظهور رسالة "Ready - started server on 0.0.0.0:3000"
- فتح الموقع بنجاح على http://localhost:3000
- ظهور الصفحة الرئيسية مع المنتجات
- إمكانية الوصول للوحة التحكم

🔄 للتحديث المستقبلي:
---------------------
- أوقف الخادم (Ctrl + C)
- اكتب: npm install (لتحديث المكتبات)
- اكتب: npm run dev (لإعادة التشغيل)
