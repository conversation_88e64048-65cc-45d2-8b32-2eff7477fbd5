'use client';

import React from 'react';
import { useAdmin } from '@/contexts/AdminContext';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import CategoryGrid from '@/components/CategoryGrid';
import { FolderOpen, Package } from 'lucide-react';

export default function CategoriesPage() {
  const { categories, products } = useAdmin();

  // Calculate products count for each category
  const categoriesWithCounts = categories.map(category => ({
    ...category,
    productCount: products.filter(product => product.category === category.id).length
  }));

  const totalProducts = products.length;
  const totalSubcategories = categories.reduce((total, cat) => total + (cat.subcategories?.length || 0), 0);

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="pt-20">
        {/* Hero Section */}
        <section className="bg-gradient-to-r from-primary-600 to-primary-800 text-white py-16">
          <div className="container mx-auto px-4">
            <div className="text-center">
              <FolderOpen className="w-16 h-16 mx-auto mb-6 opacity-90" />
              <h1 className="text-4xl md:text-5xl font-bold mb-4">
                جميع الفئات والتصنيفات
              </h1>
              <p className="text-xl opacity-90 max-w-2xl mx-auto">
                استكشف مجموعتنا الشاملة من الفئات المنظمة بعناية لتسهيل عملية التسوق
              </p>
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-12 bg-white">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
              <div className="bg-blue-50 rounded-lg p-6">
                <div className="text-3xl font-bold text-blue-600 mb-2">{categories.length}</div>
                <div className="text-gray-600">فئة رئيسية</div>
              </div>
              <div className="bg-green-50 rounded-lg p-6">
                <div className="text-3xl font-bold text-green-600 mb-2">{totalSubcategories}</div>
                <div className="text-gray-600">فئة فرعية</div>
              </div>
              <div className="bg-purple-50 rounded-lg p-6">
                <div className="text-3xl font-bold text-purple-600 mb-2">{totalProducts}</div>
                <div className="text-gray-600">منتج متاح</div>
              </div>
            </div>
          </div>
        </section>

        {/* Categories Grid */}
        <CategoryGrid 
          title="جميع الفئات"
          showAll={true}
          className="bg-gray-50"
        />

        {/* Detailed Categories List */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-gray-800 mb-8 text-center">
              تفاصيل الفئات
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {categoriesWithCounts.map((category) => (
                <div key={category.id} className="bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden">
                  {/* Category Header */}
                  <div className="bg-gradient-to-r from-gray-50 to-gray-100 p-6 border-b">
                    <div className="flex items-center space-x-4 space-x-reverse">
                      <div className="text-3xl">{category.icon || '📁'}</div>
                      <div className="flex-1">
                        <h3 className="text-xl font-semibold text-gray-800 mb-1">
                          {category.name}
                        </h3>
                        <p className="text-sm text-gray-600">
                          {category.productCount} منتج
                        </p>
                      </div>
                    </div>
                    {category.description && (
                      <p className="text-gray-600 mt-3 text-sm">
                        {category.description}
                      </p>
                    )}
                  </div>

                  {/* Subcategories */}
                  <div className="p-6">
                    {category.subcategories && category.subcategories.length > 0 ? (
                      <div>
                        <h4 className="font-medium text-gray-800 mb-3 flex items-center">
                          <Package className="w-4 h-4 ml-2" />
                          الفئات الفرعية ({category.subcategories.length})
                        </h4>
                        <div className="space-y-2">
                          {category.subcategories.map((subcategory) => (
                            <div 
                              key={subcategory.id}
                              className="bg-gray-50 rounded-lg p-3 border border-gray-200"
                            >
                              <div className="font-medium text-gray-800 text-sm">
                                {subcategory.name}
                              </div>
                              {subcategory.description && (
                                <div className="text-xs text-gray-600 mt-1">
                                  {subcategory.description}
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    ) : (
                      <div className="text-center text-gray-500 py-4">
                        <Package className="w-8 h-8 mx-auto mb-2 opacity-50" />
                        <p className="text-sm">لا توجد فئات فرعية</p>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>

            {categories.length === 0 && (
              <div className="text-center py-16">
                <FolderOpen className="w-16 h-16 mx-auto mb-4 text-gray-300" />
                <h3 className="text-xl font-semibold text-gray-600 mb-2">
                  لا توجد فئات بعد
                </h3>
                <p className="text-gray-500">
                  سيتم عرض الفئات هنا عند إضافتها من لوحة التحكم
                </p>
              </div>
            )}
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
}
