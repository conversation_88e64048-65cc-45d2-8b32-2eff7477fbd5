<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعادة تعيين إعدادات الموقع</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 600px;
            width: 100%;
            text-align: center;
        }
        
        h1 {
            color: #333;
            margin-bottom: 20px;
            font-size: 2.5rem;
        }
        
        .description {
            color: #666;
            margin-bottom: 30px;
            font-size: 1.1rem;
            line-height: 1.6;
        }
        
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        .button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 1.1rem;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
            min-width: 200px;
        }
        
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        
        .button.danger {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
        }
        
        .button.success {
            background: linear-gradient(45deg, #00b894, #00cec9);
        }
        
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin-top: 30px;
            text-align: right;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 5px;
            border-radius: 5px;
        }
        
        .log-success {
            background: #d4edda;
            color: #155724;
        }
        
        .log-info {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        .log-warning {
            background: #fff3cd;
            color: #856404;
        }
        
        .hidden {
            display: none;
        }
        
        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 10px;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 إعادة تعيين إعدادات الموقع</h1>
        
        <div class="description">
            <p>هذه الأداة تساعدك على إعادة تعيين إعدادات الموقع وحل مشاكل البيانات المحفوظة.</p>
            <p>ستقوم بتغيير اسم الموقع من "متجر اليمن الإلكتروني" إلى "مكتبة أنوار دارس" وإصلاح مشاكل السلايدر.</p>
        </div>
        
        <div class="warning">
            <strong>⚠️ تحذير:</strong> هذه العملية ستمسح جميع الإعدادات المحفوظة وتعيد الموقع للإعدادات الجديدة.
        </div>
        
        <div class="buttons">
            <button class="button" onclick="checkCurrentSettings()">
                🔍 فحص الإعدادات الحالية
            </button>
            
            <button class="button danger" onclick="clearStoredData()">
                🧹 مسح البيانات المحفوظة
            </button>
            
            <button class="button success" onclick="resetToNewSettings()">
                🔄 إعادة تعيين شاملة
            </button>
            
            <button class="button" onclick="reloadPage()">
                🔄 إعادة تحميل الصفحة
            </button>
        </div>
        
        <div id="status" class="status hidden"></div>
        
        <div id="log" class="log hidden">
            <h3>سجل العمليات:</h3>
            <div id="logContent"></div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const logContent = document.getElementById('logContent');
            
            logDiv.classList.remove('hidden');
            
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `${new Date().toLocaleTimeString()} - ${message}`;
            
            logContent.appendChild(entry);
            logContent.scrollTop = logContent.scrollHeight;
        }
        
        function showStatus(message, type = 'success') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
            statusDiv.classList.remove('hidden');
        }
        
        function checkCurrentSettings() {
            log('🔍 فحص الإعدادات الحالية...', 'info');
            
            const keys = Object.keys(localStorage);
            const relevantKeys = keys.filter(key => 
                key.includes('admin') || 
                key.includes('settings') || 
                key.includes('yemen') || 
                key.includes('ecommerce')
            );
            
            if (relevantKeys.length === 0) {
                log('✅ لا توجد إعدادات محفوظة', 'success');
                showStatus('لا توجد إعدادات محفوظة - الموقع سيستخدم الإعدادات الافتراضية الجديدة', 'success');
            } else {
                log(`📋 تم العثور على ${relevantKeys.length} إعدادات محفوظة:`, 'warning');
                relevantKeys.forEach(key => {
                    log(`   - ${key}`, 'warning');
                });
                
                // فحص اسم الموقع
                const adminSettings = localStorage.getItem('admin-settings');
                if (adminSettings) {
                    try {
                        const settings = JSON.parse(adminSettings);
                        if (settings.siteName) {
                            log(`🏪 اسم الموقع الحالي: ${settings.siteName}`, 'info');
                        }
                    } catch (e) {
                        log('❌ خطأ في قراءة إعدادات الموقع', 'warning');
                    }
                }
                
                showStatus('تم العثور على إعدادات محفوظة - قد تحتاج لإعادة التعيين', 'error');
            }
        }
        
        function clearStoredData() {
            log('🧹 بدء مسح البيانات المحفوظة...', 'info');
            
            const keysToRemove = [
                'admin-settings',
                'admin-products', 
                'admin-orders',
                'admin-categories',
                'admin-design-settings',
                'admin-slider-settings',
                'admin-colors',
                'admin-fonts',
                'admin-typography',
                'yemenecommerce-settings',
                'ecommerce-settings',
                'site-settings',
                'slider-settings',
                'design-settings'
            ];
            
            let removedCount = 0;
            
            // مسح المفاتيح المحددة
            keysToRemove.forEach(key => {
                if (localStorage.getItem(key)) {
                    localStorage.removeItem(key);
                    log(`✅ تم مسح: ${key}`, 'success');
                    removedCount++;
                }
            });
            
            // مسح أي مفاتيح أخرى تحتوي على كلمات مفتاحية
            const allKeys = Object.keys(localStorage);
            allKeys.forEach(key => {
                if (key.includes('admin') || key.includes('settings') || key.includes('yemen') || key.includes('ecommerce')) {
                    localStorage.removeItem(key);
                    log(`✅ تم مسح: ${key}`, 'success');
                    removedCount++;
                }
            });
            
            log(`🎉 تم مسح ${removedCount} عنصر من البيانات المحفوظة!`, 'success');
            showStatus(`تم مسح ${removedCount} عنصر بنجاح`, 'success');
        }
        
        function resetToNewSettings() {
            log('🔄 إعادة تعيين الإعدادات إلى القيم الجديدة...', 'info');
            
            // أولاً مسح البيانات القديمة
            clearStoredData();
            
            // تعيين الإعدادات الجديدة
            const newDefaultSettings = {
                siteName: 'مكتبة أنوار دارس',
                siteDescription: 'مكتبتك الشاملة للكتب والقرطاسية',
                contactInfo: {
                    phone: '+967-1-234567',
                    email: '<EMAIL>',
                    address: 'صنعاء، اليمن',
                },
                paymentMethods: {
                    bankTransfer: {
                        enabled: true,
                        banks: [
                            {
                                id: '1',
                                bankName: 'البنك الأهلي اليمني',
                                accountNumber: '*********',
                                accountName: 'مكتبة أنوار دارس',
                                enabled: true,
                                description: 'الحساب الرئيسي للمكتبة'
                            }
                        ]
                    }
                },
                designSettings: {
                    slider: {
                        enabled: true,
                        height: '400px',
                        autoplay: true,
                        autoplaySpeed: 5000,
                        showDots: true,
                        showArrows: true,
                        pauseOnHover: true,
                        transition: 'slide',
                        images: [
                            {
                                id: '1',
                                url: '/images/products/slider/55555.jpg',
                                title: 'مرحباً بكم في مكتبة أنوار دارس',
                                description: 'اكتشف أفضل الكتب والقرطاسية',
                                link: '/products'
                            },
                            {
                                id: '2',
                                url: '/images/products/slider/6666.jpg',
                                title: 'عروض خاصة',
                                description: 'خصومات تصل إلى 50% على الكتب المختارة',
                                link: '/offers'
                            },
                            {
                                id: '3',
                                url: '/images/products/slider/777.jpg',
                                title: 'شحن مجاني',
                                description: 'شحن مجاني للطلبات أكثر من 200 ريال',
                                link: '/shipping'
                            },
                            {
                                id: '4',
                                url: '/images/products/slider/8888.jpg',
                                title: 'خدمة عملاء ممتازة',
                                description: 'فريق دعم متاح 24/7 لخدمتكم',
                                link: '/contact'
                            }
                        ]
                    }
                }
            };
            
            // حفظ الإعدادات الجديدة
            localStorage.setItem('admin-settings', JSON.stringify(newDefaultSettings));
            log('✅ تم حفظ الإعدادات الجديدة', 'success');
            log(`🏪 اسم الموقع الجديد: ${newDefaultSettings.siteName}`, 'success');
            
            showStatus('تم إعادة التعيين بنجاح! سيتم إعادة تحميل الصفحة...', 'success');
            
            // إعادة تحميل الصفحة بعد ثانيتين
            setTimeout(() => {
                log('🔄 إعادة تحميل الصفحة...', 'info');
                window.location.href = '/';
            }, 2000);
        }
        
        function reloadPage() {
            log('🔄 إعادة تحميل الصفحة...', 'info');
            window.location.reload();
        }
        
        // فحص تلقائي عند تحميل الصفحة
        window.onload = function() {
            log('🚀 أداة إعادة التعيين جاهزة للاستخدام', 'info');
            checkCurrentSettings();
        };
    </script>
</body>
</html>
