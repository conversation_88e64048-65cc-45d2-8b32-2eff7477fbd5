حالة التشغيل:
الموقع يعمل بنجاح على: http://localhost:3000

إصدار Next.js: 14.0.4

زمن التحميل: 6.6 ثواني (جيد جداً)

التحذيرات الظاهرة:
خيار غير معترف به في next.config.js:

js
experimental: {
  appDir: true // هذا الخيار لم يعد مدعوماً في Next.js 14
}
الحل: افتح next.config.js واحذف السطر الذي يحتوي على appDir

حزم قديمة (يمكن تحديثها لاحقاً):

inflight@1.0.6

rimraf@3.0.2

glob@7.1.7

ثغرة أمنية حرجة واحدة:

cmd
npm audit fix --force
الخطوات التالية:
افتح الموقع في المتصفح:
http://localhost:3000

أصلح ملف next.config.js:

cmd
notepad next.config.js
احذف أو علّق سطر appDir: true

أصلح الثغرة الأمنية:

cmd
npm audit fix --force
(اختياري) حدث الحزم القديمة:

cmd
npm update
للتشغيل المستقبلي:
بما أنك الآن في المسار الصحيح (C:\vert)، كل ما تحتاجه هو:

cmd
npm run dev
نصائح مهمة:
احفظ هذا الأمر للوصول السريع:

cmd
cd C:\vert && npm run dev
استخدم ملف start.bat الموجود في المجلد لتشغيل تلقائي:

cmd
start.bat
إذا أردت إيقاف الخادم: Ctrl + C في نافذة CMD

الآن يمكنك البدء في تطوير موقع التجارة الإلكترونية اليمني! إذا واجهتك أي مشاكل تقنية أخرى، أنا هنا لمساعدتك. 🌟

----------------------------------------------------
0:49 AM
لدخول لوحة التحكم لإدارة المحتوى، اتبع الخطوات التالية:

طريقة الدخول إلى لوحة التحكم:
افتح المتصفح وانتقل إلى الموقع
اذهب إلى صفحة الإدارة عبر إضافة /admin إلى رابط الموقع
مثال: http://localhost:3000/admin
أدخل كلمة المرور الافتراضية:
كلمة المرور: admin123

15.434488009150575, 44.215254699999996