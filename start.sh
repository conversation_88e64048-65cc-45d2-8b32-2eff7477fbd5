#!/bin/bash

echo "========================================"
echo "       متجر اليمن الإلكتروني"
echo "========================================"
echo ""

echo "جاري التحقق من Node.js..."
if ! command -v node &> /dev/null; then
    echo "خطأ: Node.js غير مثبت!"
    echo "يرجى تحميل وتثبيت Node.js من: https://nodejs.org"
    echo ""
    exit 1
fi

echo "Node.js مثبت بنجاح!"
echo ""

echo "جاري تثبيت المكتبات المطلوبة..."
npm install

if [ $? -ne 0 ]; then
    echo "خطأ في تثبيت المكتبات!"
    exit 1
fi

echo ""
echo "تم تثبيت المكتبات بنجاح!"
echo ""

echo "جاري تشغيل المشروع..."
echo "سيتم فتح الموقع على: http://localhost:3000"
echo ""

npm run dev
