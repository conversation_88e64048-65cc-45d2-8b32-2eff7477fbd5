'use client';

import React, { useState, useEffect } from 'react';
import { useAdmin } from '@/contexts/AdminContext';
import { useToast } from '@/components/ToastContainer';
import { AdminUser } from '@/types';
import { PERMISSIONS, ROLE_PERMISSIONS, getRoleDisplayName, hasPermission } from '@/utils/permissions';
import {
  Users,
  Plus,
  Edit,
  Trash2,
  Eye,
  EyeOff,
  Shield,
  UserCheck,
  UserX,
  Save,
  X,
  Key,
  User,
  Settings,
  Lock
} from 'lucide-react';

const UserManagement: React.FC = () => {
  const { adminUsers, currentUser, addAdminUser, updateAdminUser, deleteAdminUser, adminCredentials, updateAdminCredentials, adminLogout } = useAdmin();
  const { showSuccess, showError } = useToast();
  const [showAddUser, setShowAddUser] = useState(false);
  const [editingUser, setEditingUser] = useState<string | null>(null);
  const [showPassword, setShowPassword] = useState<Record<string, boolean>>({});

  // حالة إدارة بيانات الاعتماد
  const [isChangingCredentials, setIsChangingCredentials] = useState(false);
  const [credentialsForm, setCredentialsForm] = useState({
    currentPassword: '',
    newUsername: adminCredentials.username,
    newPassword: '',
    confirmPassword: ''
  });
  const [showCredentialPasswords, setShowCredentialPasswords] = useState({
    current: false,
    new: false,
    confirm: false
  });

  // تحديث النموذج عند تغيير بيانات الاعتماد
  useEffect(() => {
    setCredentialsForm(prev => ({
      ...prev,
      newUsername: adminCredentials.username
    }));
  }, [adminCredentials.username]);
  const [newUser, setNewUser] = useState<Omit<AdminUser, 'id' | 'createdAt' | 'createdBy'>>({
    username: '',
    email: '',
    name: '',
    password: '',
    role: 'editor',
    permissions: ROLE_PERMISSIONS.editor,
    isActive: true
  });

  // Check if current user can manage users
  const canManageUsers = currentUser && hasPermission(currentUser.permissions, 'users.view');
  const canCreateUsers = currentUser && hasPermission(currentUser.permissions, 'users.create');
  const canEditUsers = currentUser && hasPermission(currentUser.permissions, 'users.edit');
  const canDeleteUsers = currentUser && hasPermission(currentUser.permissions, 'users.delete');

  if (!canManageUsers) {
    return (
      <div className="text-center py-8">
        <Shield className="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-600">غير مصرح لك بالوصول</h3>
        <p className="text-gray-500">ليس لديك صلاحية لإدارة المستخدمين</p>
      </div>
    );
  }

  const handleAddUser = () => {
    if (newUser.username && newUser.password && newUser.name && newUser.email) {
      addAdminUser(newUser);
      setNewUser({
        username: '',
        email: '',
        name: '',
        password: '',
        role: 'editor',
        permissions: ROLE_PERMISSIONS.editor,
        isActive: true
      });
      setShowAddUser(false);
    }
  };

  const handleRoleChange = (role: string) => {
    setNewUser(prev => ({
      ...prev,
      role: role as AdminUser['role'],
      permissions: ROLE_PERMISSIONS[role as keyof typeof ROLE_PERMISSIONS] || []
    }));
  };

  const handleUpdateUser = (userId: string, userData: Partial<AdminUser>) => {
    updateAdminUser(userId, userData);
    setEditingUser(null);
  };

  const handleDeleteUser = (userId: string) => {
    if (confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
      deleteAdminUser(userId);
    }
  };

  const togglePasswordVisibility = (userId: string) => {
    setShowPassword(prev => ({
      ...prev,
      [userId]: !prev[userId]
    }));
  };

  // دوال إدارة بيانات الاعتماد
  const handleCredentialInputChange = (field: string, value: string) => {
    setCredentialsForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const toggleCredentialPasswordVisibility = (field: 'current' | 'new' | 'confirm') => {
    setShowCredentialPasswords(prev => ({
      ...prev,
      [field]: !prev[field]
    }));
  };

  const validateCredentialsForm = () => {
    if (credentialsForm.currentPassword !== adminCredentials.password) {
      showError('خطأ', 'كلمة المرور الحالية غير صحيحة');
      return false;
    }

    if (!credentialsForm.newUsername.trim()) {
      showError('خطأ', 'يجب إدخال اسم المستخدم الجديد');
      return false;
    }

    if (credentialsForm.newUsername.length < 3) {
      showError('خطأ', 'اسم المستخدم يجب أن يكون 3 أحرف على الأقل');
      return false;
    }

    if (!credentialsForm.newPassword.trim()) {
      showError('خطأ', 'يجب إدخال كلمة المرور الجديدة');
      return false;
    }

    if (credentialsForm.newPassword.length < 6) {
      showError('خطأ', 'كلمة المرور يجب أن تكون 6 أحرف على الأقل');
      return false;
    }

    if (credentialsForm.newPassword !== credentialsForm.confirmPassword) {
      showError('خطأ', 'كلمة المرور الجديدة وتأكيد كلمة المرور غير متطابقتين');
      return false;
    }

    return true;
  };

  const handleSaveCredentials = () => {
    if (!validateCredentialsForm()) return;

    // تأكيد من المستخدم
    if (!confirm('سيتم تحديث بيانات الدخول وتسجيل الخروج تلقائياً. هل تريد المتابعة؟')) {
      return;
    }

    try {
      updateAdminCredentials({
        username: credentialsForm.newUsername,
        password: credentialsForm.newPassword
      });

      setCredentialsForm({
        currentPassword: '',
        newUsername: credentialsForm.newUsername,
        newPassword: '',
        confirmPassword: ''
      });

      setIsChangingCredentials(false);
      showSuccess('تم الحفظ!', 'تم تحديث بيانات الدخول بنجاح. سيتم تسجيل الخروج الآن.');

      // تسجيل خروج تلقائي بعد 2 ثانية
      setTimeout(() => {
        window.location.reload();
      }, 2000);

    } catch (error) {
      showError('خطأ', 'حدث خطأ أثناء حفظ بيانات الدخول');
    }
  };

  const handleCancelCredentialsChange = () => {
    setCredentialsForm({
      currentPassword: '',
      newUsername: adminCredentials.username,
      newPassword: '',
      confirmPassword: ''
    });
    setIsChangingCredentials(false);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-semibold">إدارة المستخدمين</h2>
          <p className="text-gray-600">إدارة المستخدمين الإداريين وصلاحياتهم</p>
        </div>
        {canCreateUsers && (
          <button
            onClick={() => setShowAddUser(true)}
            className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors flex items-center"
          >
            <Plus className="w-5 h-5 ml-2" />
            إضافة مستخدم
          </button>
        )}
      </div>

      {/* قسم إدارة بيانات الاعتماد */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <Shield className="w-6 h-6 text-blue-600" />
            <h3 className="text-xl font-bold text-gray-800">إدارة بيانات الدخول الرئيسية</h3>
          </div>
        </div>

        {/* معلومات المستخدم الحالي */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <div className="flex items-center gap-3 mb-3">
            <UserCheck className="w-5 h-5 text-blue-600" />
            <h4 className="text-lg font-semibold text-blue-800">المستخدم الحالي</h4>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-blue-700 mb-1">
                اسم المستخدم
              </label>
              <div className="flex items-center gap-2 px-3 py-2 bg-white border border-blue-300 rounded-md">
                <User className="w-4 h-4 text-blue-600" />
                <span className="text-gray-800 font-medium">{adminCredentials.username}</span>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-blue-700 mb-1">
                آخر تحديث
              </label>
              <div className="flex items-center gap-2 px-3 py-2 bg-white border border-blue-300 rounded-md">
                <Settings className="w-4 h-4 text-blue-600" />
                <span className="text-gray-800">
                  {new Date(adminCredentials.lastUpdated).toLocaleDateString('ar-SA')}
                </span>
              </div>
            </div>
          </div>

          <div className="mt-3">
            <div className="flex items-center gap-2 px-3 py-2 bg-white border border-blue-300 rounded-md">
              <Lock className="w-4 h-4 text-blue-600" />
              <span className="text-sm text-blue-700">
                {adminCredentials.isDefault
                  ? 'بيانات الدخول الافتراضية (يُنصح بتغييرها)'
                  : 'بيانات دخول مخصصة'}
              </span>
            </div>
          </div>
        </div>

        {/* تغيير بيانات الدخول */}
        <div className="border rounded-lg p-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <Key className="w-5 h-5 text-gray-600" />
              <h4 className="text-lg font-semibold text-gray-800">تغيير بيانات الدخول</h4>
            </div>

            {!isChangingCredentials && (
              <button
                onClick={() => setIsChangingCredentials(true)}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                تغيير بيانات الدخول
              </button>
            )}
          </div>

          {isChangingCredentials ? (
            <div className="space-y-4">
              {/* كلمة المرور الحالية */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  كلمة المرور الحالية *
                </label>
                <div className="relative">
                  <input
                    type={showCredentialPasswords.current ? 'text' : 'password'}
                    value={credentialsForm.currentPassword}
                    onChange={(e) => handleCredentialInputChange('currentPassword', e.target.value)}
                    className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="أدخل كلمة المرور الحالية"
                  />
                  <button
                    type="button"
                    onClick={() => toggleCredentialPasswordVisibility('current')}
                    className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    {showCredentialPasswords.current ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  </button>
                </div>
              </div>

              {/* اسم المستخدم الجديد */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  اسم المستخدم الجديد *
                </label>
                <div className="relative">
                  <input
                    type="text"
                    value={credentialsForm.newUsername}
                    onChange={(e) => handleCredentialInputChange('newUsername', e.target.value)}
                    className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="أدخل اسم المستخدم الجديد"
                  />
                  <User className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                </div>
              </div>

              {/* كلمة المرور الجديدة */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  كلمة المرور الجديدة *
                </label>
                <div className="relative">
                  <input
                    type={showCredentialPasswords.new ? 'text' : 'password'}
                    value={credentialsForm.newPassword}
                    onChange={(e) => handleCredentialInputChange('newPassword', e.target.value)}
                    className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="أدخل كلمة المرور الجديدة"
                  />
                  <button
                    type="button"
                    onClick={() => toggleCredentialPasswordVisibility('new')}
                    className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    {showCredentialPasswords.new ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  </button>
                </div>
              </div>

              {/* تأكيد كلمة المرور */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  تأكيد كلمة المرور الجديدة *
                </label>
                <div className="relative">
                  <input
                    type={showCredentialPasswords.confirm ? 'text' : 'password'}
                    value={credentialsForm.confirmPassword}
                    onChange={(e) => handleCredentialInputChange('confirmPassword', e.target.value)}
                    className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="أعد إدخال كلمة المرور الجديدة"
                  />
                  <button
                    type="button"
                    onClick={() => toggleCredentialPasswordVisibility('confirm')}
                    className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    {showCredentialPasswords.confirm ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  </button>
                </div>
              </div>

              {/* تحذير قبل الحفظ */}
              <div className="bg-orange-50 border border-orange-200 rounded-lg p-3 mb-4">
                <div className="flex items-start gap-2">
                  <Shield className="w-4 h-4 text-orange-600 mt-0.5" />
                  <div className="text-sm text-orange-700">
                    <p className="font-medium">تنبيه مهم:</p>
                    <p>بعد حفظ بيانات الدخول الجديدة، سيتم تسجيل الخروج تلقائياً وستحتاج لتسجيل الدخول مرة أخرى بالبيانات الجديدة.</p>
                  </div>
                </div>
              </div>

              {/* أزرار الإجراءات */}
              <div className="flex justify-end gap-3 pt-4 border-t">
                <button
                  onClick={handleCancelCredentialsChange}
                  className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                >
                  إلغاء
                </button>

                <button
                  onClick={handleSaveCredentials}
                  className="flex items-center gap-2 px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  <Save className="w-4 h-4" />
                  حفظ التغييرات
                </button>
              </div>
            </div>
          ) : (
            <div className="text-center py-6 text-gray-500">
              <Lock className="w-12 h-12 mx-auto mb-3 text-gray-400" />
              <p>اضغط على "تغيير بيانات الدخول" لتحديث اسم المستخدم وكلمة المرور</p>
            </div>
          )}
        </div>

        {/* تحذير أمني */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mt-4">
          <div className="flex items-start gap-3">
            <Shield className="w-5 h-5 text-yellow-600 mt-0.5" />
            <div>
              <h5 className="font-semibold text-yellow-800 mb-1">تحذير أمني</h5>
              <ul className="text-sm text-yellow-700 space-y-1">
                <li>• احرص على استخدام كلمة مرور قوية (6 أحرف على الأقل)</li>
                <li>• لا تشارك بيانات الدخول مع أي شخص آخر</li>
                <li>• قم بتغيير كلمة المرور بانتظام لضمان الأمان</li>
                <li>• تأكد من حفظ بيانات الدخول الجديدة في مكان آمن</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Add User Form */}
      {showAddUser && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold">إضافة مستخدم جديد</h3>
            <button
              onClick={() => setShowAddUser(false)}
              className="text-gray-500 hover:text-gray-700"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                اسم المستخدم *
              </label>
              <input
                type="text"
                value={newUser.username}
                onChange={(e) => setNewUser(prev => ({ ...prev, username: e.target.value }))}
                className="input-field"
                placeholder="username"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                الاسم الكامل *
              </label>
              <input
                type="text"
                value={newUser.name}
                onChange={(e) => setNewUser(prev => ({ ...prev, name: e.target.value }))}
                className="input-field"
                placeholder="الاسم الكامل"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                البريد الإلكتروني *
              </label>
              <input
                type="email"
                value={newUser.email}
                onChange={(e) => setNewUser(prev => ({ ...prev, email: e.target.value }))}
                className="input-field"
                placeholder="<EMAIL>"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                كلمة المرور *
              </label>
              <input
                type="password"
                value={newUser.password}
                onChange={(e) => setNewUser(prev => ({ ...prev, password: e.target.value }))}
                className="input-field"
                placeholder="كلمة المرور"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                الدور
              </label>
              <select
                value={newUser.role}
                onChange={(e) => handleRoleChange(e.target.value)}
                className="input-field"
              >
                <option value="editor">محرر</option>
                <option value="manager">مشرف</option>
                <option value="admin">مدير</option>
                {currentUser?.role === 'super_admin' && (
                  <option value="super_admin">مدير عام</option>
                )}
              </select>
            </div>

            <div className="flex items-center">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={newUser.isActive}
                  onChange={(e) => setNewUser(prev => ({ ...prev, isActive: e.target.checked }))}
                  className="ml-2"
                />
                <span className="text-sm">مفعل</span>
              </label>
            </div>
          </div>

          <div className="flex justify-end space-x-2 space-x-reverse mt-6">
            <button
              onClick={() => setShowAddUser(false)}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              إلغاء
            </button>
            <button
              onClick={handleAddUser}
              className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 flex items-center"
            >
              <Save className="w-4 h-4 ml-2" />
              حفظ
            </button>
          </div>
        </div>
      )}

      {/* Users List */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  المستخدم
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الدور
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الحالة
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  آخر دخول
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الإجراءات
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {adminUsers.map((user) => (
                <tr key={user.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-full bg-primary-100 flex items-center justify-center">
                          <Users className="h-5 w-5 text-primary-600" />
                        </div>
                      </div>
                      <div className="mr-4">
                        <div className="text-sm font-medium text-gray-900">{user.name}</div>
                        <div className="text-sm text-gray-500">{user.email}</div>
                        <div className="text-xs text-gray-400">@{user.username}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      user.role === 'super_admin' ? 'bg-purple-100 text-purple-800' :
                      user.role === 'admin' ? 'bg-blue-100 text-blue-800' :
                      user.role === 'manager' ? 'bg-green-100 text-green-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {getRoleDisplayName(user.role)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      user.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {user.isActive ? 'مفعل' : 'معطل'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {user.lastLogin ? new Date(user.lastLogin).toLocaleDateString('ar-YE') : 'لم يدخل بعد'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2 space-x-reverse">
                      <button
                        onClick={() => togglePasswordVisibility(user.id)}
                        className="text-gray-600 hover:text-gray-800"
                        title="عرض/إخفاء كلمة المرور"
                      >
                        {showPassword[user.id] ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                      </button>
                      
                      {canEditUsers && user.id !== currentUser?.id && (
                        <button
                          onClick={() => setEditingUser(user.id)}
                          className="text-blue-600 hover:text-blue-800"
                          title="تعديل"
                        >
                          <Edit className="w-4 h-4" />
                        </button>
                      )}
                      
                      {canDeleteUsers && user.id !== currentUser?.id && user.role !== 'super_admin' && (
                        <button
                          onClick={() => handleDeleteUser(user.id)}
                          className="text-red-600 hover:text-red-800"
                          title="حذف"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      )}
                    </div>
                    
                    {showPassword[user.id] && (
                      <div className="mt-2 text-xs text-gray-600 bg-gray-100 p-2 rounded">
                        كلمة المرور: {user.password}
                      </div>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Permissions Info */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="font-semibold text-blue-800 mb-2">معلومات الأدوار والصلاحيات:</h4>
        <div className="text-sm text-blue-700 space-y-1">
          <p><strong>مدير عام:</strong> جميع الصلاحيات</p>
          <p><strong>مدير:</strong> إدارة المنتجات والطلبات والإعدادات والتقارير</p>
          <p><strong>مشرف:</strong> إدارة المنتجات والطلبات وعرض التقارير</p>
          <p><strong>محرر:</strong> إدارة المنتجات وعرض الطلبات فقط</p>
        </div>
      </div>
    </div>
  );
};

export default UserManagement;
