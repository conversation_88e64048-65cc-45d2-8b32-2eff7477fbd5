# دليل تحرير المحتوى والأقسام

## 1. تحرير النصوص الأساسية

### الطريقة السهلة - ملف التكوين:
افتح الملف: `src/config/siteConfig.ts`

```typescript
// تغيير اسم الموقع
siteName: 'اسم متجرك هنا',

// تغيير وصف الموقع  
siteDescription: 'وصف متجرك هنا',

// تغيير العنوان الرئيسي
hero: {
  title: 'العنوان الرئيسي لموقعك',
  subtitle: 'الوصف التفصيلي',
  ctaButton: 'نص الزر',
}
```

## 2. إدارة الفئات (الأقسام)

### من لوحة التحكم:
1. اذهب إلى: `http://localhost:3000/admin`
2. بيانات الدخول:
   - اسم المستخدم: `admin`
   - كلمة المرور: `admin77111`
3. تبويب "الفئات"
4. اضغط "إضافة فئة جديدة"

### من الكود:
في ملف `src/config/siteConfig.ts`:

```typescript
defaultCategories: [
  {
    id: 'category-id',
    name: 'اسم الفئة',
    description: 'وصف الفئة',
    icon: 'اسم الأيقونة' // مثل: smartphone, shirt, home
  }
]
```

## 3. تحرير معلومات الاتصال

```typescript
contact: {
  phone: 'رقم هاتفك',
  email: 'بريدك الإلكتروني',
  address: 'عنوانك',
  workingHours: 'ساعات العمل',
  socialMedia: {
    facebook: 'رابط الفيسبوك',
    instagram: 'رابط الإنستغرام',
    whatsapp: 'رقم الواتساب',
  }
}
```

## 4. إعدادات الدفع والبنك

```typescript
payment: {
  bankTransfer: {
    enabled: true, // تفعيل/إلغاء التحويل البنكي
    bankName: 'اسم البنك',
    accountNumber: 'رقم الحساب',
    accountName: 'اسم صاحب الحساب',
  }
}
```

## 5. إعدادات الشحن

```typescript
shipping: {
  freeShippingThreshold: 10000, // الحد الأدنى للشحن المجاني
  shippingCost: 500, // تكلفة الشحن
  deliveryAreas: ['المدن المتاحة للتوصيل'],
}
```

## 6. تحرير النصوص في الصفحات

### صفحة السلة:
```typescript
cart: {
  emptyCart: 'نص السلة الفارغة',
  checkout: 'نص زر الدفع',
  continueShopping: 'نص متابعة التسوق',
}
```

### صفحة الدفع:
```typescript
checkout: {
  title: 'عنوان صفحة الدفع',
  personalInfo: 'المعلومات الشخصية',
  paymentMethod: 'طريقة الدفع',
}
```

## 7. إضافة منتجات جديدة

### من لوحة التحكم:
1. لوحة التحكم → المنتجات
2. "إضافة منتج جديد"
3. املأ البيانات:
   - اسم المنتج
   - الوصف
   - السعر
   - الفئة
   - رابط الصورة
   - الكمية المتوفرة

## 8. تخصيص الألوان والتصميم

في ملف `tailwind.config.js`:

```javascript
colors: {
  primary: {
    600: '#لون أساسي', // اللون الأساسي للموقع
    700: '#لون أساسي داكن',
  }
}
```

## 9. تحرير Footer (التذييل)

في ملف `src/components/Footer.tsx` أو في `siteConfig.ts`:

```typescript
footer: {
  description: 'وصف متجرك في التذييل',
  quickLinks: 'الروابط السريعة',
  followUs: 'تابعنا على',
}
```

## 10. نصائح مهمة:

### بعد التعديل:
1. احفظ الملفات
2. أعد تشغيل الخادم: `npm run dev`
3. تحقق من التغييرات في المتصفح

### النسخ الاحتياطي:
- انسخ الملفات قبل التعديل
- استخدم Git لحفظ التغييرات

### الأيقونات المتاحة:
- smartphone, laptop, tablet
- shirt, dress, shoe
- home, kitchen, bed
- book, pen, notebook
- dumbbell, ball, bike
- car, truck, plane
- heart, star, shield
- credit-card, bank, money

## 11. ملفات مهمة للتحرير:

- `src/config/siteConfig.ts` - النصوص والإعدادات
- `src/contexts/AdminContext.tsx` - الإعدادات الافتراضية
- `src/components/Header.tsx` - رأس الصفحة
- `src/components/Footer.tsx` - تذييل الصفحة
- `src/app/page.tsx` - الصفحة الرئيسية
- `tailwind.config.js` - الألوان والتصميم

## 12. للمساعدة:

إذا واجهت أي مشكلة:
1. تأكد من صحة الكود (الأقواس والفواصل)
2. تحقق من رسائل الخطأ في المتصفح (F12)
3. أعد تشغيل الخادم
4. راجع هذا الدليل

---

**ملاحظة:** بعد أي تعديل، تأكد من حفظ الملف وإعادة تشغيل الخادم لرؤية التغييرات.
