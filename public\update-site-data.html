<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحديث بيانات الموقع - مكتبة أنوار دارس</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 800px;
            width: 100%;
            text-align: center;
        }
        
        h1 {
            color: #333;
            margin-bottom: 20px;
            font-size: 2.5rem;
        }
        
        .description {
            color: #666;
            margin-bottom: 30px;
            font-size: 1.1rem;
            line-height: 1.6;
        }
        
        .button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 1.1rem;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
            min-width: 200px;
        }
        
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        
        .button.success {
            background: linear-gradient(45deg, #00b894, #00cec9);
        }
        
        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 10px;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .data-preview {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: right;
        }
        
        .data-item {
            margin-bottom: 10px;
            padding: 8px;
            background: white;
            border-radius: 5px;
            border-left: 4px solid #667eea;
        }
        
        .old-data {
            background: #f8d7da;
            border-left-color: #dc3545;
        }
        
        .new-data {
            background: #d4edda;
            border-left-color: #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 تحديث بيانات الموقع</h1>
        
        <div class="description">
            <p>هذه الأداة ستقوم بتحديث جميع بيانات الموقع من "متجر اليمن الإلكتروني" إلى "مكتبة أنوار دارس"</p>
            <p>سيتم تطبيق جميع التعديلات التي قمت بها وحفظها بشكل دائم.</p>
        </div>
        
        <div class="data-preview">
            <h3>📋 البيانات التي سيتم تحديثها:</h3>
            
            <div class="data-item old-data">
                <strong>❌ القديم:</strong> متجر اليمن الإلكتروني
            </div>
            <div class="data-item new-data">
                <strong>✅ الجديد:</strong> مكتبة أنوار دارس
            </div>
            
            <div class="data-item old-data">
                <strong>❌ القديم:</strong> كل ما تحتاجه في مكان واحد
            </div>
            <div class="data-item new-data">
                <strong>✅ الجديد:</strong> مكتبتك الشاملة للكتب والقرطاسية
            </div>
            
            <div class="data-item old-data">
                <strong>❌ القديم:</strong> <EMAIL>
            </div>
            <div class="data-item new-data">
                <strong>✅ الجديد:</strong> <EMAIL>
            </div>
            
            <div class="data-item new-data">
                <strong>✅ إضافي:</strong> جميع التعديلات الأخرى التي قمت بها
            </div>
        </div>
        
        <div class="buttons">
            <button class="button success" onclick="updateSiteData()">
                🚀 تحديث البيانات الآن
            </button>
            
            <button class="button" onclick="previewChanges()">
                👁️ معاينة التغييرات
            </button>
        </div>
        
        <div id="status" class="status" style="display: none;"></div>
    </div>

    <script>
        function showStatus(message, type = 'success') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
            statusDiv.style.display = 'block';
        }
        
        function updateSiteData() {
            console.log('🚀 بدء تحديث بيانات الموقع...');
            
            // مسح جميع البيانات القديمة
            const keysToRemove = [
                'admin-settings',
                'admin-products', 
                'admin-orders',
                'admin-categories',
                'admin-design-settings',
                'yemenecommerce-settings',
                'ecommerce-settings',
                'site-settings'
            ];
            
            keysToRemove.forEach(key => {
                if (localStorage.getItem(key)) {
                    localStorage.removeItem(key);
                    console.log(`✅ تم مسح: ${key}`);
                }
            });
            
            // تعيين البيانات الجديدة المحدثة
            const newSiteData = {
                siteName: 'مكتبة أنوار دارس',
                siteDescription: 'مكتبتك الشاملة للكتب والقرطاسية',
                contactInfo: {
                    phone: '+967-777-123456',
                    email: '<EMAIL>',
                    address: 'صنعاء، اليمن - شارع الزبيري',
                    whatsapp: '+967-777-123456'
                },
                paymentMethods: {
                    bankTransfer: {
                        enabled: true,
                        banks: [
                            {
                                id: '1',
                                bankName: 'البنك الأهلي اليمني',
                                accountNumber: '*********',
                                accountName: 'مكتبة أنوار دارس',
                                enabled: true,
                                description: 'الحساب الرئيسي للمكتبة'
                            },
                            {
                                id: '2',
                                bankName: 'بنك اليمن الدولي',
                                accountNumber: '*********',
                                accountName: 'مكتبة أنوار دارس',
                                enabled: true,
                                description: 'حساب احتياطي'
                            }
                        ]
                    },
                    cashOnDelivery: {
                        enabled: true,
                        description: 'الدفع عند الاستلام متاح داخل صنعاء'
                    }
                },
                designSettings: {
                    theme: {
                        primaryColor: '#2563eb',
                        secondaryColor: '#64748b',
                        accentColor: '#d946ef'
                    },
                    typography: {
                        headings: {
                            fontFamily: 'Cairo, system-ui, -apple-system, sans-serif',
                            fontSize: '2rem',
                            fontWeight: '700',
                            lineHeight: '1.2',
                            letterSpacing: '0.025em'
                        },
                        subheadings: {
                            fontFamily: 'Cairo, system-ui, -apple-system, sans-serif',
                            fontSize: '1.5rem',
                            fontWeight: '600',
                            lineHeight: '1.3',
                            letterSpacing: '0.025em'
                        },
                        paragraphs: {
                            fontFamily: 'Cairo, system-ui, -apple-system, sans-serif',
                            fontSize: '1rem',
                            fontWeight: '400',
                            lineHeight: '1.6',
                            letterSpacing: '0.025em'
                        }
                    },
                    slider: {
                        enabled: true,
                        height: '400px',
                        autoplay: true,
                        autoplaySpeed: 5000,
                        showDots: true,
                        showArrows: true,
                        pauseOnHover: true,
                        transition: 'slide',
                        images: [
                            {
                                id: '1',
                                url: '/images/products/slider/55555.jpg',
                                title: 'مرحباً بكم في مكتبة أنوار دارس',
                                description: 'اكتشف أفضل الكتب والقرطاسية التعليمية',
                                link: '/products'
                            },
                            {
                                id: '2',
                                url: '/images/products/slider/6666.jpg',
                                title: 'عروض خاصة على الكتب',
                                description: 'خصومات تصل إلى 50% على الكتب المدرسية',
                                link: '/offers'
                            },
                            {
                                id: '3',
                                url: '/images/products/slider/777.jpg',
                                title: 'شحن مجاني',
                                description: 'شحن مجاني للطلبات أكثر من 200 ريال',
                                link: '/shipping'
                            },
                            {
                                id: '4',
                                url: '/images/products/slider/8888.jpg',
                                title: 'خدمة عملاء ممتازة',
                                description: 'فريق دعم متاح 24/7 لخدمتكم',
                                link: '/contact'
                            }
                        ]
                    }
                },
                categories: [
                    {
                        id: '1',
                        name: 'الكتب والمراجع',
                        description: 'كتب تعليمية ومراجع أكاديمية',
                        image: '/images/categories/books.jpg',
                        enabled: true
                    },
                    {
                        id: '2',
                        name: 'القرطاسية',
                        description: 'أدوات مكتبية وقرطاسية متنوعة',
                        image: '/images/categories/stationery.jpg',
                        enabled: true
                    },
                    {
                        id: '3',
                        name: 'الأدوات التعليمية',
                        description: 'أدوات ووسائل تعليمية حديثة',
                        image: '/images/categories/educational.jpg',
                        enabled: true
                    }
                ],
                adminPassword: 'admin77111',
                lastUpdated: new Date().toISOString()
            };
            
            // حفظ البيانات الجديدة
            localStorage.setItem('admin-settings', JSON.stringify(newSiteData));
            
            console.log('✅ تم حفظ البيانات الجديدة');
            console.log('📊 البيانات المحفوظة:', newSiteData);
            
            showStatus('✅ تم تحديث جميع بيانات الموقع بنجاح! سيتم إعادة توجيهك للصفحة الرئيسية...', 'success');
            
            // إعادة توجيه للصفحة الرئيسية بعد 3 ثوان
            setTimeout(() => {
                window.location.href = '/';
            }, 3000);
        }
        
        function previewChanges() {
            const currentSettings = localStorage.getItem('admin-settings');
            
            if (currentSettings) {
                try {
                    const settings = JSON.parse(currentSettings);
                    console.log('📋 الإعدادات الحالية:', settings);
                    
                    if (settings.siteName === 'مكتبة أنوار دارس') {
                        showStatus('✅ البيانات محدثة بالفعل - الموقع يعرض "مكتبة أنوار دارس"', 'success');
                    } else {
                        showStatus(`⚠️ البيانات الحالية: "${settings.siteName}" - تحتاج للتحديث`, 'warning');
                    }
                } catch (e) {
                    showStatus('❌ خطأ في قراءة البيانات الحالية', 'error');
                }
            } else {
                showStatus('📝 لا توجد إعدادات محفوظة - سيتم استخدام الإعدادات الافتراضية الجديدة', 'success');
            }
        }
        
        // فحص تلقائي عند تحميل الصفحة
        window.onload = function() {
            console.log('🔍 فحص البيانات الحالية...');
            previewChanges();
        };
    </script>
</body>
</html>
