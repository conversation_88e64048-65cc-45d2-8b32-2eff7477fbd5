Console.log("Console.log("Console.log("");");");'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useCart } from '@/contexts/CartContext';
import { useAdmin } from '@/contexts/AdminContext';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Minus, Plus, Trash2, ShoppingBag, ArrowRight } from 'lucide-react';

export default function CartPage() {
  const router = useRouter();
  const { items, total, updateQuantity, removeFromCart, clearCart, saveCart } = useCart();
  const { settings } = useAdmin();
  const [isCheckingOut, setIsCheckingOut] = useState(false);

  const shippingCost = total >= settings.shippingSettings.freeShippingThreshold ? 0 : settings.shippingSettings.shippingCost;
  const finalTotal = total + shippingCost;

  const handleQuantityChange = (productId: string, newQuantity: number) => {
    if (newQuantity < 1) {
      removeFromCart(productId);
    } else {
      updateQuantity(productId, newQuantity);
    }
  };

  const handleCheckout = () => {
    setIsCheckingOut(true);
    // Save cart before navigation to ensure data persistence
    saveCart();
    // Redirect to checkout page
    router.push('/checkout');
  };

  if (items.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <main className="container mx-auto px-4 py-16">
          <div className="text-center">
            <div className="bg-gray-200 w-32 h-32 rounded-full flex items-center justify-center mx-auto mb-8">
              <ShoppingBag className="w-16 h-16 text-gray-400" />
            </div>
            <h1 className="text-3xl font-bold text-gray-800 mb-4">سلة التسوق فارغة</h1>
            <p className="text-gray-600 mb-8">لم تقم بإضافة أي منتجات إلى سلة التسوق بعد</p>
            <Link
              href="/"
              className="bg-primary-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-primary-700 transition-colors inline-flex items-center"
            >
              <ArrowRight className="w-5 h-5 ml-2" />
              العودة للتسوق
            </Link>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">سلة التسوق</h1>
          <p className="text-gray-600">لديك {items.length} منتج في سلة التسوق</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Cart Items */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-md">
              <div className="p-6 border-b">
                <div className="flex justify-between items-center">
                  <h2 className="text-xl font-semibold">المنتجات</h2>
                  <button
                    onClick={clearCart}
                    className="text-red-600 hover:text-red-700 text-sm font-medium"
                  >
                    إفراغ السلة
                  </button>
                </div>
              </div>
              
              <div className="divide-y">
                {items.map((item) => (
                  <div key={item.id} className="p-6">
                    <div className="flex items-center space-x-4 space-x-reverse">
                      {/* Product Image */}
                      <div className="relative w-20 h-20 bg-gray-100 rounded-lg overflow-hidden">
                        <Image
                          src={item.product.image}
                          alt={item.product.name}
                          fill
                          className="object-cover"
                          sizes="80px"
                        />
                      </div>

                      {/* Product Info */}
                      <div className="flex-1 min-w-0">
                        <Link
                          href={`/product/${item.product.id}`}
                          className="text-lg font-semibold text-gray-800 hover:text-primary-600 transition-colors"
                        >
                          {item.product.name}
                        </Link>
                        {item.product.brand && (
                          <p className="text-sm text-gray-500 mt-1">{item.product.brand}</p>
                        )}
                        <div className="flex items-center mt-2">
                          <span className="text-lg font-bold text-primary-600">
                            {item.product.price.toLocaleString()} ريال
                          </span>
                          {item.product.originalPrice && (
                            <span className="text-sm text-gray-500 line-through mr-2">
                              {item.product.originalPrice.toLocaleString()} ريال
                            </span>
                          )}
                        </div>
                      </div>

                      {/* Quantity Controls */}
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <button
                          onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                          className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-100 transition-colors"
                        >
                          <Minus className="w-4 h-4" />
                        </button>
                        <span className="w-12 text-center font-semibold">{item.quantity}</span>
                        <button
                          onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                          className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-100 transition-colors"
                        >
                          <Plus className="w-4 h-4" />
                        </button>
                      </div>

                      {/* Item Total */}
                      <div className="text-left">
                        <p className="text-lg font-bold text-gray-800">
                          {(item.product.price * item.quantity).toLocaleString()} ريال
                        </p>
                      </div>

                      {/* Remove Button */}
                      <button
                        onClick={() => removeFromCart(item.id)}
                        className="text-red-600 hover:text-red-700 p-2"
                      >
                        <Trash2 className="w-5 h-5" />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-md p-6 sticky top-8">
              <h2 className="text-xl font-semibold mb-6">ملخص الطلب</h2>
              
              <div className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-gray-600">المجموع الفرعي</span>
                  <span className="font-semibold">{total.toLocaleString()} ريال</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-gray-600">الشحن</span>
                  <span className="font-semibold">
                    {shippingCost === 0 ? 'مجاني' : `${shippingCost.toLocaleString()} ريال`}
                  </span>
                </div>
                
                {shippingCost > 0 && (
                  <div className="text-sm text-gray-500 bg-blue-50 p-3 rounded-lg">
                    أضف {(settings.shippingSettings.freeShippingThreshold - total).toLocaleString()} ريال للحصول على الشحن المجاني
                  </div>
                )}
                
                <div className="border-t pt-4">
                  <div className="flex justify-between text-lg font-bold">
                    <span>المجموع الكلي</span>
                    <span className="text-primary-600">{finalTotal.toLocaleString()} ريال</span>
                  </div>
                </div>
              </div>

              <button
                onClick={handleCheckout}
                disabled={isCheckingOut}
                className="w-full bg-primary-600 text-white py-3 rounded-lg font-semibold hover:bg-primary-700 transition-colors mt-6 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isCheckingOut ? 'جاري التحويل...' : 'إتمام الطلب'}
              </button>

              <Link
                href="/"
                className="w-full border border-gray-300 text-gray-700 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-colors mt-3 inline-flex items-center justify-center"
              >
                <ArrowRight className="w-5 h-5 ml-2" />
                متابعة التسوق
              </Link>

              {/* Payment Methods */}
              <div className="mt-6 pt-6 border-t">
                <h3 className="font-semibold mb-3">طرق الدفع المقبولة</h3>
                <div className="space-y-2">
                  {settings.paymentMethods.cashOnDelivery.enabled && (
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-green-500 rounded-full ml-2"></div>
                      <span className="text-sm">نقداً عند الاستلام</span>
                    </div>
                  )}
                  {settings.paymentMethods.bankTransfer.enabled && (
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-blue-500 rounded-full ml-2"></div>
                      <span className="text-sm">تحويل بنكي</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
