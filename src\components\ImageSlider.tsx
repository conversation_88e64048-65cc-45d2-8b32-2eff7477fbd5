'use client';

import React, { useState, useEffect } from 'react';
import { useAdmin } from '@/contexts/AdminContext';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import Link from 'next/link';

const ImageSlider: React.FC = () => {
  const { settings } = useAdmin();
  const [currentSlide, setCurrentSlide] = useState(0);
  const [imageLoadStates, setImageLoadStates] = useState<Record<string, boolean>>({});
  const [hasError, setHasError] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [isSliderReady, setIsSliderReady] = useState(false);

  // إعدادات افتراضية للسلايدر
  const defaultSliderSettings = {
    enabled: true,
    height: '400px',
    autoplay: true,
    autoplaySpeed: 5000,
    showDots: true,
    showArrows: true,
    pauseOnHover: true,
    transition: 'slide',
    images: [
      {
        id: '1',
        url: '/images/products/slider/welcome-banner.svg',
        title: 'مرحباً بكم في متجرنا',
        description: 'اكتشف أفضل المنتجات بأسعار تنافسية',
        link: '/products'
      },
      {
        id: '2',
        url: '/images/products/slider/special-offers.svg',
        title: 'عروض خاصة',
        description: 'خصومات تصل إلى 50% على منتجات مختارة',
        link: '/offers'
      },
      {
        id: '3',
        url: '/images/products/slider/free-shipping.svg',
        title: 'شحن مجاني',
        description: 'شحن مجاني للطلبات أكثر من 200 ريال',
        link: '/shipping'
      }
    ]
  };

  const sliderSettings = {
    ...defaultSliderSettings,
    ...(settings?.designSettings?.slider || {})
  };

  const images = (sliderSettings.images && sliderSettings.images.length > 0)
    ? sliderSettings.images
    : defaultSliderSettings.images;

  // تحميل مسبق للصور
  useEffect(() => {
    images.forEach((image) => {
      const img = new Image();
      img.onload = () => {
        setImageLoadStates(prev => ({ ...prev, [image.id]: true }));
        console.log(`✅ تم تحميل الصورة مسبقاً: ${image.url}`);
      };
      img.onerror = () => {
        setImageLoadStates(prev => ({ ...prev, [image.id]: false }));
        console.error(`❌ فشل تحميل الصورة مسبقاً: ${image.url}`);
      };
      img.src = image.url;
    });
  }, [images]);

  // تحديد حالة جاهزية السلايدر
  useEffect(() => {
    if (images && images.length > 0) {
      setIsSliderReady(true);
      console.log('🎯 السلايدر جاهز مع', images.length, 'صور');

      // التأكد من أن currentSlide لا يتجاوز عدد الصور
      if (currentSlide >= images.length) {
        setCurrentSlide(0);
        console.log('🔄 تم إعادة تعيين الصورة الحالية إلى 0');
      }
    } else {
      setIsSliderReady(false);
      console.log('⏳ السلايدر غير جاهز - لا توجد صور');
    }
  }, [images, currentSlide]);

  console.log('إعدادات السلايدر:', sliderSettings);
  console.log('صور السلايدر:', images);
  console.log('السلايدر مفعل:', sliderSettings.enabled);
  console.log('عدد الصور:', images.length);
  console.log('الصورة الحالية:', currentSlide);
  console.log('حالة تحميل الصور:', imageLoadStates);
  console.log('إعدادات التحكم:', {
    showDots: sliderSettings.showDots,
    showArrows: sliderSettings.showArrows,
    pauseOnHover: sliderSettings.pauseOnHover,
    isPaused: isPaused,
    isSliderReady: isSliderReady,
    shouldShowDots: sliderSettings.showDots && isSliderReady && images.length > 1
  });

  // التشغيل التلقائي
  useEffect(() => {
    if (!sliderSettings.autoplay || images.length <= 1 || isPaused) return;

    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % images.length);
    }, sliderSettings.autoplaySpeed);

    return () => clearInterval(interval);
  }, [sliderSettings.autoplay, sliderSettings.autoplaySpeed, images.length, isPaused]);

  // إذا كان السلايدر معطل أو لا توجد صور
  if (!sliderSettings.enabled || images.length === 0) {
    return null;
  }

  // في حالة وجود خطأ في جميع الصور
  if (hasError && Object.keys(imageLoadStates).length === images.length &&
      Object.values(imageLoadStates).every(loaded => !loaded)) {
    return (
      <div className="relative w-full overflow-hidden bg-gradient-to-r from-blue-600 to-blue-800 rounded-lg shadow-lg">
        <div style={{ height: sliderSettings.height }} className="flex items-center justify-center">
          <div className="text-white text-center p-8">
            <h2 className="text-3xl font-bold mb-4">مرحباً بكم في متجرنا</h2>
            <p className="text-xl mb-6">اكتشف أفضل المنتجات بأسعار تنافسية</p>
            <Link href="/products" className="bg-yellow-500 text-blue-900 px-6 py-3 rounded-lg font-semibold hover:bg-yellow-400 transition-colors">
              تصفح المنتجات
            </Link>
          </div>
        </div>
      </div>
    );
  }

  const nextSlide = () => {
    const newSlide = (currentSlide + 1) % images.length;
    console.log(`🔄 الانتقال للصورة التالية: ${currentSlide} → ${newSlide}`);
    setCurrentSlide(newSlide);
  };

  const prevSlide = () => {
    const newSlide = (currentSlide - 1 + images.length) % images.length;
    console.log(`🔄 الانتقال للصورة السابقة: ${currentSlide} → ${newSlide}`);
    setCurrentSlide(newSlide);
  };

  const goToSlide = (index: number) => {
    console.log(`🎯 الانتقال للصورة رقم: ${currentSlide} → ${index}`);
    setCurrentSlide(index);
  };

  // معالجة أحداث الماوس للإيقاف عند التمرير
  const handleMouseEnter = () => {
    if (sliderSettings.pauseOnHover) {
      setIsPaused(true);
      console.log('🛑 تم إيقاف السلايدر عند التمرير');
    }
  };

  const handleMouseLeave = () => {
    if (sliderSettings.pauseOnHover) {
      setIsPaused(false);
      console.log('▶️ تم استئناف السلايدر بعد التمرير');
    }
  };

  return (
    <>
      {/* CSS مباشر لضمان عدم اختفاء النقاط والأسهم */}
      <style jsx>{`
        .slider-container .slider-dots,
        .slider-container .slider-arrow {
          display: block !important;
          visibility: visible !important;
          opacity: 1 !important;
          pointer-events: auto !important;
        }
        .slider-container .slider-dots {
          display: flex !important;
        }
        .force-visible {
          display: block !important;
          visibility: visible !important;
          opacity: 1 !important;
        }
      `}</style>

      <div
        className="slider-container relative w-full overflow-hidden bg-gray-900 rounded-lg shadow-lg"
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        style={{ position: 'relative', isolation: 'isolate' }}
      >
      {/* Container */}
      <div
        className="relative"
        style={{ height: sliderSettings.height }}
      >
        {/* Images */}
        {images.map((image, index) => (
          <div
            key={image.id}
            className={`absolute inset-0 w-full h-full transition-opacity duration-500 ${
              index === currentSlide ? 'opacity-100 z-10' : 'opacity-0 z-0'
            }`}
          >
            {/* Background Image */}
            <img
              src={image.url}
              alt={image.title || `صورة السلايدر ${index + 1}`}
              className="w-full h-full object-cover"
              onError={(e) => {
                console.error(`فشل تحميل صورة السلايدر ${index + 1}: ${image.url}`);
                setHasError(true);
                setImageLoadStates(prev => ({ ...prev, [image.id]: false }));
                // استخدم صورة افتراضية
                e.currentTarget.src = 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80';
              }}
              onLoad={() => {
                console.log(`تم تحميل صورة السلايدر ${index + 1} بنجاح: ${image.url}`);
                setImageLoadStates(prev => ({ ...prev, [image.id]: true }));
              }}
            />

            {/* Loading State */}
            {!imageLoadStates[image.id] && (
              <div className="absolute inset-0 bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center">
                <div className="text-white text-center">
                  <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-500 border-t-transparent mx-auto mb-4"></div>
                  <p className="text-lg font-semibold">جاري تحميل الصورة {index + 1}...</p>
                  <p className="text-sm opacity-75 mt-2">{image.title}</p>
                </div>
              </div>
            )}

            {/* Overlay */}
            <div className="absolute inset-0 bg-black bg-opacity-40" />

            {/* Content */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-center text-white px-4 max-w-4xl">
                <h2 className="text-3xl md:text-5xl font-bold mb-4 drop-shadow-lg">
                  {image.title}
                </h2>
                <p className="text-lg md:text-xl mb-6 drop-shadow-lg opacity-90">
                  {image.description}
                </p>
                {image.link && (
                  <Link
                    href={image.link}
                    className="inline-block px-8 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 duration-200"
                  >
                    اكتشف المزيد
                  </Link>
                )}
              </div>
            </div>
          </div>
        ))}

        {/* Navigation Arrows - Always visible when enabled */}
        {sliderSettings.showArrows && (
          <>
            <button
              onClick={prevSlide}
              className="slider-arrow absolute left-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-30 hover:bg-opacity-50 text-white p-3 rounded-full transition-all duration-300 backdrop-blur-sm hover:scale-110 shadow-lg force-visible"
              aria-label="الصورة السابقة"
              title="الصورة السابقة"
              style={{
                display: 'block',
                visibility: 'visible',
                opacity: 1,
                zIndex: 15,
                pointerEvents: 'auto'
              } as React.CSSProperties}
            >
              <ChevronLeft className="w-6 h-6" />
            </button>
            <button
              onClick={nextSlide}
              className="slider-arrow absolute right-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-30 hover:bg-opacity-50 text-white p-3 rounded-full transition-all duration-300 backdrop-blur-sm hover:scale-110 shadow-lg force-visible"
              aria-label="الصورة التالية"
              title="الصورة التالية"
              style={{
                display: 'block',
                visibility: 'visible',
                opacity: 1,
                zIndex: 15,
                pointerEvents: 'auto'
              } as React.CSSProperties}
            >
              <ChevronRight className="w-6 h-6" />
            </button>
          </>
        )}

        {/* Dots Indicator - Always visible when enabled */}
        {sliderSettings.showDots && (
          <div
            className="slider-dots absolute bottom-6 left-1/2 transform -translate-x-1/2 flex space-x-3 space-x-reverse bg-black bg-opacity-30 px-4 py-2 rounded-full backdrop-blur-sm force-visible"
            style={{
              display: 'flex',
              visibility: 'visible',
              opacity: 1,
              zIndex: 15,
              pointerEvents: 'auto'
            } as React.CSSProperties}
          >
            {images && images.length > 0 ? (
              Array.from({ length: images.length }, (_, index) => (
                <button
                  key={`dot-${index}`}
                  onClick={() => goToSlide(index)}
                  className={`w-4 h-4 rounded-full transition-all duration-200 hover:scale-110 ${
                    index === currentSlide
                      ? 'bg-white scale-110 shadow-lg'
                      : 'bg-white bg-opacity-60 hover:bg-opacity-90'
                  }`}
                  aria-label={`الذهاب للصورة ${index + 1}`}
                  title={`الصورة ${index + 1}`}
                  style={{
                    display: 'block',
                    visibility: 'visible',
                    opacity: 1,
                    zIndex: 16,
                    pointerEvents: 'auto'
                  } as React.CSSProperties}
                />
              ))
            ) : (
              // عرض نقاط افتراضية إذا لم تحمل الصور بعد
              Array.from({ length: 4 }, (_, index) => (
                <button
                  key={`default-dot-${index}`}
                  onClick={() => goToSlide(index)}
                  className={`w-4 h-4 rounded-full transition-all duration-200 hover:scale-110 ${
                    index === currentSlide
                      ? 'bg-white scale-110 shadow-lg'
                      : 'bg-white bg-opacity-60 hover:bg-opacity-90'
                  }`}
                  aria-label={`الذهاب للصورة ${index + 1}`}
                  title={`الصورة ${index + 1}`}
                  style={{
                    display: 'block',
                    visibility: 'visible',
                    opacity: 1,
                    zIndex: 16,
                    pointerEvents: 'auto'
                  } as React.CSSProperties}
                />
              ))
            )}
          </div>
        )}

        {/* Pause Indicator */}
        {isPaused && sliderSettings.autoplay && (
          <div className="absolute top-4 right-4 bg-black bg-opacity-50 text-white px-3 py-1 rounded-full text-sm backdrop-blur-sm">
            ⏸️ متوقف
          </div>
        )}
      </div>

      {/* Loading Indicator */}
      {sliderSettings.autoplay && images.length > 1 && (
        <div className="absolute bottom-0 left-0 h-1 bg-primary-600 transition-all duration-100 ease-linear"
          style={{
            width: `${((Date.now() % sliderSettings.autoplaySpeed) / sliderSettings.autoplaySpeed) * 100}%`
          }}
        />
      )}
    </div>
    </>
  );
};

export default ImageSlider;
