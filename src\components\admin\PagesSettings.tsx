'use client';

import React, { useState } from 'react';
import { useAdmin } from '@/contexts/AdminContext';
import { useToast } from '@/components/ToastContainer';
import { 
  FileText, 
  HelpCircle, 
  Truck, 
  RotateCcw, 
  Shield, 
  ScrollText,
  Palette,
  Type,
  Save,
  Eye,
  EyeOff,
  Settings
} from 'lucide-react';

const PagesSettings: React.FC = () => {
  const { settings, updateSettings } = useAdmin();
  const { showSuccess, showError } = useToast();
  const [activeTab, setActiveTab] = useState('customerService');
  const [showPreview, setShowPreview] = useState(false);

  const defaultPagesSettings = {
    customerService: {
      enabled: true,
      title: 'خدمة العملاء',
      content: 'نحن في مكتبة أنوار دارس نسعى لتقديم أفضل خدمة عملاء. يمكنكم التواصل معنا عبر الهاتف أو البريد الإلكتروني أو زيارة المكتبة مباشرة.',
      fontSize: '16px',
      fontWeight: '400',
      textColor: '#374151',
      backgroundColor: '#ffffff'
    },
    faq: {
      enabled: true,
      title: 'الأسئلة الشائعة',
      content: 'س: ما هي أوقات عمل المكتبة؟\nج: نعمل من السبت إلى الخميس من 8 صباحاً حتى 8 مساءً.\n\nس: هل يوجد توصيل؟\nج: نعم، نوفر خدمة التوصيل المجاني للطلبات أكثر من 200 ريال.',
      fontSize: '16px',
      fontWeight: '400',
      textColor: '#374151',
      backgroundColor: '#ffffff'
    },
    shippingPolicy: {
      enabled: true,
      title: 'سياسة الشحن',
      content: 'نوفر خدمة التوصيل لجميع أنحاء صنعاء. التوصيل مجاني للطلبات أكثر من 200 ريال. رسوم التوصيل للطلبات الأقل هي 100 ريال.',
      fontSize: '16px',
      fontWeight: '400',
      textColor: '#374151',
      backgroundColor: '#ffffff'
    },
    returnPolicy: {
      enabled: true,
      title: 'سياسة الإرجاع',
      content: 'يمكن إرجاع المنتجات خلال 7 أيام من تاريخ الشراء بشرط أن تكون في حالتها الأصلية. لا يمكن إرجاع الكتب المستعملة أو التالفة.',
      fontSize: '16px',
      fontWeight: '400',
      textColor: '#374151',
      backgroundColor: '#ffffff'
    },
    privacyPolicy: {
      enabled: true,
      title: 'سياسة الخصوصية',
      content: 'نحن نحترم خصوصية عملائنا ونحافظ على سرية معلوماتهم الشخصية. لا نشارك بياناتكم مع أطراف ثالثة دون موافقتكم.',
      fontSize: '16px',
      fontWeight: '400',
      textColor: '#374151',
      backgroundColor: '#ffffff'
    },
    termsConditions: {
      enabled: true,
      title: 'الشروط والأحكام',
      content: 'باستخدام موقعنا، فإنك توافق على الشروط والأحكام التالية. جميع الأسعار شاملة الضريبة. نحتفظ بالحق في تعديل الأسعار دون إشعار مسبق.',
      fontSize: '16px',
      fontWeight: '400',
      textColor: '#374151',
      backgroundColor: '#ffffff'
    }
  };

  const pagesSettings = settings.pagesSettings || defaultPagesSettings;

  const updatePageSetting = (pageKey: string, settingKey: string, value: any) => {
    const updatedSettings = {
      ...settings,
      pagesSettings: {
        ...pagesSettings,
        [pageKey]: {
          ...pagesSettings[pageKey as keyof typeof pagesSettings],
          [settingKey]: value
        }
      }
    };
    updateSettings(updatedSettings);
  };

  const handleSave = () => {
    try {
      if (typeof window !== 'undefined') {
        localStorage.setItem('admin-settings', JSON.stringify(settings));
      }
      showSuccess('تم الحفظ!', 'تم حفظ إعدادات الصفحات بنجاح');
    } catch (error) {
      showError('خطأ', 'حدث خطأ أثناء حفظ الإعدادات');
    }
  };

  const resetToDefault = () => {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع إعدادات الصفحات للقيم الافتراضية؟')) {
      updateSettings({
        ...settings,
        pagesSettings: defaultPagesSettings
      });
      showSuccess('تم الإعادة!', 'تم إعادة تعيين جميع الإعدادات الافتراضية');
    }
  };

  const pages = [
    { key: 'customerService', name: 'خدمة العملاء', icon: FileText },
    { key: 'faq', name: 'الأسئلة الشائعة', icon: HelpCircle },
    { key: 'shippingPolicy', name: 'سياسة الشحن', icon: Truck },
    { key: 'returnPolicy', name: 'سياسة الإرجاع', icon: RotateCcw },
    { key: 'privacyPolicy', name: 'سياسة الخصوصية', icon: Shield },
    { key: 'termsConditions', name: 'الشروط والأحكام', icon: ScrollText }
  ];

  const currentPage = pagesSettings[activeTab as keyof typeof pagesSettings];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Settings className="w-6 h-6 text-blue-600" />
          <h3 className="text-xl font-bold text-gray-800">إعدادات الصفحات والسياسات</h3>
        </div>
        
        <div className="flex items-center gap-3">
          <button
            onClick={() => setShowPreview(!showPreview)}
            className="flex items-center gap-2 px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
          >
            {showPreview ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            <span className="text-sm">{showPreview ? 'إخفاء المعاينة' : 'عرض المعاينة'}</span>
          </button>
          
          <button
            onClick={handleSave}
            className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
          >
            <Save className="w-4 h-4" />
            <span className="text-sm">حفظ التغييرات</span>
          </button>
        </div>
      </div>

      {/* تبويبات الصفحات */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 space-x-reverse">
          {pages.map((page) => {
            const Icon = page.icon;
            return (
              <button
                key={page.key}
                onClick={() => setActiveTab(page.key)}
                className={`flex items-center gap-2 py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === page.key
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="w-4 h-4" />
                {page.name}
              </button>
            );
          })}
        </nav>
      </div>

      {/* معاينة الصفحة */}
      {showPreview && currentPage && (
        <div className="border rounded-lg overflow-hidden">
          <div className="bg-gray-100 px-4 py-2 text-sm font-medium text-gray-700">
            معاينة: {pages.find(p => p.key === activeTab)?.name}
          </div>
          <div 
            className="p-6"
            style={{
              backgroundColor: currentPage.backgroundColor,
              color: currentPage.textColor,
              fontSize: currentPage.fontSize,
              fontWeight: currentPage.fontWeight
            }}
          >
            <h1 className="text-2xl font-bold mb-4">{currentPage.title}</h1>
            <div className="whitespace-pre-line">{currentPage.content}</div>
          </div>
        </div>
      )}

      {/* إعدادات الصفحة المحددة */}
      {currentPage && (
        <div className="bg-white p-6 rounded-lg border">
          <h4 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
            {React.createElement(pages.find(p => p.key === activeTab)?.icon || FileText, { className: "w-5 h-5" })}
            إعدادات {pages.find(p => p.key === activeTab)?.name}
          </h4>
          
          {/* تفعيل/إلغاء تفعيل الصفحة */}
          <div className="mb-6 p-4 bg-blue-50 rounded-lg">
            <label className="flex items-center gap-3">
              <input
                type="checkbox"
                checked={currentPage.enabled}
                onChange={(e) => updatePageSetting(activeTab, 'enabled', e.target.checked)}
                className="w-5 h-5 text-blue-600 rounded focus:ring-blue-500"
              />
              <span className="text-lg font-medium text-gray-700">
                تفعيل صفحة {pages.find(p => p.key === activeTab)?.name}
              </span>
            </label>
          </div>

          {currentPage.enabled && (
            <div className="space-y-6">
              {/* عنوان الصفحة */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  عنوان الصفحة
                </label>
                <input
                  type="text"
                  value={currentPage.title}
                  onChange={(e) => updatePageSetting(activeTab, 'title', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="عنوان الصفحة"
                />
              </div>

              {/* محتوى الصفحة */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  محتوى الصفحة
                </label>
                <textarea
                  value={currentPage.content}
                  onChange={(e) => updatePageSetting(activeTab, 'content', e.target.value)}
                  rows={8}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="محتوى الصفحة..."
                />
                <p className="text-xs text-gray-500 mt-1">
                  يمكنك استخدام أسطر جديدة لتنسيق النص
                </p>
              </div>

              {/* إعدادات التصميم */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {/* حجم الخط */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    حجم الخط
                  </label>
                  <select
                    value={currentPage.fontSize}
                    onChange={(e) => updatePageSetting(activeTab, 'fontSize', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="14px">صغير (14px)</option>
                    <option value="16px">متوسط (16px)</option>
                    <option value="18px">كبير (18px)</option>
                    <option value="20px">كبير جداً (20px)</option>
                    <option value="24px">عملاق (24px)</option>
                  </select>
                </div>

                {/* وزن الخط */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    وزن الخط
                  </label>
                  <select
                    value={currentPage.fontWeight}
                    onChange={(e) => updatePageSetting(activeTab, 'fontWeight', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="300">خفيف (300)</option>
                    <option value="400">عادي (400)</option>
                    <option value="500">متوسط (500)</option>
                    <option value="600">سميك (600)</option>
                    <option value="700">سميك جداً (700)</option>
                  </select>
                </div>

                {/* لون النص */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    لون النص
                  </label>
                  <div className="flex gap-2">
                    <input
                      type="color"
                      value={currentPage.textColor}
                      onChange={(e) => updatePageSetting(activeTab, 'textColor', e.target.value)}
                      className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                    />
                    <input
                      type="text"
                      value={currentPage.textColor}
                      onChange={(e) => updatePageSetting(activeTab, 'textColor', e.target.value)}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="#374151"
                    />
                  </div>
                </div>

                {/* لون الخلفية */}
                <div className="md:col-span-2 lg:col-span-3">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    لون الخلفية
                  </label>
                  <div className="flex gap-2">
                    <input
                      type="color"
                      value={currentPage.backgroundColor}
                      onChange={(e) => updatePageSetting(activeTab, 'backgroundColor', e.target.value)}
                      className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                    />
                    <input
                      type="text"
                      value={currentPage.backgroundColor}
                      onChange={(e) => updatePageSetting(activeTab, 'backgroundColor', e.target.value)}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="#ffffff"
                    />
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* أزرار الإجراءات */}
      <div className="flex justify-between items-center pt-4 border-t">
        <button
          onClick={resetToDefault}
          className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
        >
          إعادة تعيين افتراضي
        </button>
        
        <button
          onClick={handleSave}
          className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          حفظ جميع التغييرات
        </button>
      </div>
    </div>
  );
};

export default PagesSettings;
