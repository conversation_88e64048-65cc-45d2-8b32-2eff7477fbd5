/* إصلاح مشكلة اختفاء النقاط والأسهم في السلايدر */

/* ضمان ظهور الأسهم دائماً عندما تكون مفعلة */
.slider-arrow {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  position: absolute !important;
  z-index: 10 !important;
  pointer-events: auto !important;
}

/* ضمان ظهور النقاط دائماً عندما تكون مفعلة */
.slider-dots {
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
  position: absolute !important;
  z-index: 10 !important;
  pointer-events: auto !important;
}

.slider-dot {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  pointer-events: auto !important;
}

/* منع أي تأثيرات قد تخفي العناصر */
.slider-container * {
  transition-property: transform, background-color, box-shadow, scale !important;
  /* استبعاد opacity و visibility من الانتقالات */
}

/* ضمان أن السلايدر نفسه مرئي */
.slider-container {
  position: relative !important;
  overflow: hidden !important;
}

/* إصلاح أي مشاكل في z-index */
.slider-container .absolute {
  z-index: 5 !important;
}

.slider-container .absolute:last-child {
  z-index: 10 !important;
}

/* ضمان أن النقاط والأسهم فوق كل شيء */
.slider-dots,
.slider-arrow {
  z-index: 15 !important;
}

/* منع اختفاء العناصر عند التحميل */
.slider-container [class*="opacity-0"] {
  opacity: 1 !important;
}

.slider-container [class*="invisible"] {
  visibility: visible !important;
}

.slider-container [class*="hidden"] {
  display: block !important;
}

/* تأكيد إضافي للنقاط */
.slider-dots button {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  width: 16px !important;
  height: 16px !important;
  border-radius: 50% !important;
  background-color: rgba(255, 255, 255, 0.6) !important;
  border: none !important;
  cursor: pointer !important;
  transition: transform 0.2s ease, background-color 0.2s ease !important;
}

.slider-dots button:hover {
  transform: scale(1.1) !important;
  background-color: rgba(255, 255, 255, 0.9) !important;
}

.slider-dots button.active {
  background-color: white !important;
  transform: scale(1.1) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
}

/* تأكيد إضافي للأسهم */
.slider-arrow {
  width: 48px !important;
  height: 48px !important;
  border-radius: 50% !important;
  background-color: rgba(0, 0, 0, 0.3) !important;
  color: white !important;
  border: none !important;
  cursor: pointer !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: background-color 0.3s ease, transform 0.3s ease !important;
}

.slider-arrow:hover {
  background-color: rgba(0, 0, 0, 0.5) !important;
  transform: translateY(-50%) scale(1.1) !important;
}

.slider-arrow svg {
  width: 24px !important;
  height: 24px !important;
  color: white !important;
}

/* إصلاح مشاكل الموضع */
.slider-arrow.left {
  left: 16px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
}

.slider-arrow.right {
  right: 16px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
}

.slider-dots {
  bottom: 24px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  gap: 12px !important;
  padding: 8px 16px !important;
  background-color: rgba(0, 0, 0, 0.2) !important;
  border-radius: 24px !important;
  backdrop-filter: blur(4px) !important;
}

/* منع أي تداخل مع عناصر أخرى */
.slider-container {
  isolation: isolate !important;
}

/* ضمان عدم تأثر العناصر بحالة التحميل */
.slider-container.loading .slider-dots,
.slider-container.loading .slider-arrow {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* إصلاح مشاكل الـ responsive */
@media (max-width: 768px) {
  .slider-arrow {
    width: 40px !important;
    height: 40px !important;
  }
  
  .slider-arrow svg {
    width: 20px !important;
    height: 20px !important;
  }
  
  .slider-dots button {
    width: 12px !important;
    height: 12px !important;
  }
  
  .slider-dots {
    gap: 8px !important;
    padding: 6px 12px !important;
  }
}

/* تأكيد نهائي - فرض الظهور */
.force-visible {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  pointer-events: auto !important;
}

/* منع أي CSS خارجي من إخفاء العناصر */
.slider-container .slider-dots,
.slider-container .slider-arrow {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.slider-container .slider-dots {
  display: flex !important;
}
