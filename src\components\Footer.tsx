'use client';

import React from 'react';
import Link from 'next/link';
import { useAdmin } from '@/contexts/AdminContext';
import {
  Phone,
  Mail,
  MapPin,
  Facebook,
  Instagram,
  MessageCircle,
  CreditCard,
  Truck,
  Shield,
  Clock
} from 'lucide-react';
import XIcon from './icons/XIcon';
import TelegramIcon from './icons/TelegramIcon';

const Footer: React.FC = () => {
  const { settings, categories } = useAdmin();

  return (
    <footer className="bg-gray-900 text-white">
      {/* Features Section */}
      <div className="bg-gray-800 py-8">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="flex items-center justify-center md:justify-start">
              <div className="bg-primary-600 p-3 rounded-full ml-4">
                <Truck className="w-6 h-6" />
              </div>
              <div>
                <h3 className="font-semibold">توصيل سريع</h3>
                <p className="text-sm text-gray-300">توصيل في نفس اليوم</p>
              </div>
            </div>
            <div className="flex items-center justify-center md:justify-start">
              <div className="bg-green-600 p-3 rounded-full ml-4">
                <Shield className="w-6 h-6" />
              </div>
              <div>
                <h3 className="font-semibold">ضمان الجودة</h3>
                <p className="text-sm text-gray-300">منتجات أصلية 100%</p>
              </div>
            </div>
            <div className="flex items-center justify-center md:justify-start">
              <div className="bg-yellow-600 p-3 rounded-full ml-4">
                <CreditCard className="w-6 h-6" />
              </div>
              <div>
                <h3 className="font-semibold">دفع آمن</h3>
                <p className="text-sm text-gray-300">طرق دفع متعددة</p>
              </div>
            </div>
            <div className="flex items-center justify-center md:justify-start">
              <div className="bg-purple-600 p-3 rounded-full ml-4">
                <Clock className="w-6 h-6" />
              </div>
              <div>
                <h3 className="font-semibold">خدمة 24/7</h3>
                <p className="text-sm text-gray-300">دعم على مدار الساعة</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Footer */}
      <div className="py-12">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {/* Company Info */}
            <div>
              <div className="flex items-center mb-4">
                <div className="bg-primary-600 text-white p-2 rounded-lg ml-3">
                  <span className="text-xl font-bold">🛒</span>
                </div>
                <div>
                  <h3 className="text-xl font-bold">{settings.siteName}</h3>
                </div>
              </div>
              <p className="text-gray-300 mb-4">
                {settings.siteDescription}
              </p>
              <p className="text-gray-300 text-sm">
                متجرك الإلكتروني الموثوق في اليمن لجميع احتياجاتك من الكتب والقرطاسية والإلكترونيات.
              </p>
            </div>

            {/* Quick Links */}
            <div>
              <h3 className="text-lg font-semibold mb-4">روابط سريعة</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="/" className="text-gray-300 hover:text-white transition-colors">
                    الرئيسية
                  </Link>
                </li>
                {categories.slice(0, 4).map((category) => (
                  <li key={category.id}>
                    <Link 
                      href={`/category/${category.id}`} 
                      className="text-gray-300 hover:text-white transition-colors"
                    >
                      {category.name}
                    </Link>
                  </li>
                ))}
                <li>
                  <Link href="/about" className="text-gray-300 hover:text-white transition-colors">
                    من نحن
                  </Link>
                </li>
                <li>
                  <Link href="/contact" className="text-gray-300 hover:text-white transition-colors">
                    اتصل بنا
                  </Link>
                </li>
              </ul>
            </div>

            {/* Customer Service */}
            <div>
              <h3 className="text-lg font-semibold mb-4">خدمة العملاء</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="/faq" className="text-gray-300 hover:text-white transition-colors">
                    الأسئلة الشائعة
                  </Link>
                </li>
                <li>
                  <Link href="/shipping" className="text-gray-300 hover:text-white transition-colors">
                    سياسة الشحن
                  </Link>
                </li>
                <li>
                  <Link href="/returns" className="text-gray-300 hover:text-white transition-colors">
                    سياسة الإرجاع
                  </Link>
                </li>
                <li>
                  <Link href="/privacy" className="text-gray-300 hover:text-white transition-colors">
                    سياسة الخصوصية
                  </Link>
                </li>
                <li>
                  <Link href="/terms" className="text-gray-300 hover:text-white transition-colors">
                    الشروط والأحكام
                  </Link>
                </li>
              </ul>
            </div>

            {/* Contact Info */}
            <div>
              <h3 className="text-lg font-semibold mb-4">تواصل معنا</h3>
              <div className="space-y-3">
                <div className="flex items-center">
                  <Phone className="w-5 h-5 ml-3 text-primary-400" />
                  <span className="text-gray-300">{settings.contactInfo.phone}</span>
                </div>
                <div className="flex items-center">
                  <Mail className="w-5 h-5 ml-3 text-primary-400" />
                  <span className="text-gray-300">{settings.contactInfo.email}</span>
                </div>
                <div className="flex items-center">
                  <MapPin className="w-5 h-5 ml-3 text-primary-400" />
                  <span className="text-gray-300">{settings.contactInfo.address}</span>
                </div>
              </div>

              {/* Social Media */}
              <div className="mt-6">
                <h4 className="font-semibold mb-3">تابعنا على</h4>
                <div className="flex space-x-3 space-x-reverse">
                  {settings.contactInfo.socialMedia.facebook && (
                    <a
                      href={settings.contactInfo.socialMedia.facebook}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="bg-blue-600 p-2 rounded-full hover:bg-blue-700 transition-colors"
                    >
                      <Facebook className="w-5 h-5" />
                    </a>
                  )}
                  {settings.contactInfo.socialMedia.instagram && (
                    <a
                      href={settings.contactInfo.socialMedia.instagram}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="bg-pink-600 p-2 rounded-full hover:bg-pink-700 transition-colors"
                    >
                      <Instagram className="w-5 h-5" />
                    </a>
                  )}
                  {settings.contactInfo.socialMedia.twitter && (
                    <a
                      href={settings.contactInfo.socialMedia.twitter}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="bg-black p-2 rounded-full hover:bg-gray-800 transition-colors"
                      title="تابعنا على X (تويتر)"
                    >
                      <XIcon size={20} color="white" />
                    </a>
                  )}
                  {settings.contactInfo.socialMedia.telegram && (
                    <a
                      href={settings.contactInfo.socialMedia.telegram}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="bg-blue-500 p-2 rounded-full hover:bg-blue-600 transition-colors"
                      title="تابعنا على تلجرام"
                    >
                      <TelegramIcon size={20} color="white" />
                    </a>
                  )}
                  {settings.contactInfo.socialMedia.whatsapp && (
                    <a
                      href={`https://wa.me/${settings.contactInfo.socialMedia.whatsapp.replace(/[^0-9]/g, '')}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="bg-green-600 p-2 rounded-full hover:bg-green-700 transition-colors"
                    >
                      <MessageCircle className="w-5 h-5" />
                    </a>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="bg-gray-800 py-4">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-300 text-sm">
              © 2024 {settings.siteName}. جميع الحقوق محفوظة.
            </p>
            <div className="flex items-center space-x-4 space-x-reverse mt-2 md:mt-0">
              <span className="text-gray-300 text-sm">طرق الدفع المقبولة:</span>
              <div className="flex space-x-2 space-x-reverse">
                <div className="bg-white text-gray-800 px-2 py-1 rounded text-xs font-semibold">
                  تحويل بنكي
                </div>
                <div className="bg-white text-gray-800 px-2 py-1 rounded text-xs font-semibold">
                  نقداً عند الاستلام
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
