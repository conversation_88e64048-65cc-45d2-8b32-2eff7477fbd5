import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import { 
  extractFontFamily, 
  extractFontWeight, 
  extractFontStyle, 
  getFontType,
  detectFontCategory 
} from '@/utils/fontUtils';

export async function GET() {
  try {
    const fontsDir = path.join(process.cwd(), 'public', 'fonts');
    
    // التحقق من وجود مجلد الخطوط
    if (!fs.existsSync(fontsDir)) {
      return NextResponse.json({ fonts: [] });
    }

    // قراءة جميع الملفات في مجلد الخطوط
    const files = fs.readdirSync(fontsDir);
    
    // تصفية ملفات الخطوط فقط
    const fontExtensions = ['.woff2', '.woff', '.ttf', '.otf', '.eot'];
    const fontFiles = files.filter(file => 
      fontExtensions.some(ext => file.toLowerCase().endsWith(ext))
    );

    // معلومات تفصيلية عن كل خط
    const fonts = fontFiles.map(file => {
      const filePath = path.join(fontsDir, file);
      const stats = fs.statSync(filePath);
      
      return {
        name: file,
        path: `/fonts/${file}`,
        size: stats.size,
        type: getFontType(file),
        family: extractFontFamily(file),
        weight: extractFontWeight(file),
        style: extractFontStyle(file),
        category: detectFontCategory(file),
        lastModified: stats.mtime.toISOString()
      };
    });

    return NextResponse.json({ fonts });
  } catch (error) {
    console.error('Error reading fonts directory:', error);
    return NextResponse.json(
      { error: 'Failed to read fonts directory' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const files = formData.getAll('fonts') as File[];
    
    if (!files || files.length === 0) {
      return NextResponse.json(
        { error: 'No files provided' },
        { status: 400 }
      );
    }

    const fontsDir = path.join(process.cwd(), 'public', 'fonts');
    
    // إنشاء مجلد الخطوط إذا لم يكن موجوداً
    if (!fs.existsSync(fontsDir)) {
      fs.mkdirSync(fontsDir, { recursive: true });
    }

    const uploadedFonts = [];
    const errors = [];

    for (const file of files) {
      try {
        // التحقق من صحة الملف
        const validExtensions = ['.woff2', '.woff', '.ttf', '.otf', '.eot'];
        const fileExtension = path.extname(file.name).toLowerCase();
        
        if (!validExtensions.includes(fileExtension)) {
          errors.push(`${file.name}: نوع الملف غير مدعوم`);
          continue;
        }

        // التحقق من حجم الملف (5MB max)
        if (file.size > 5 * 1024 * 1024) {
          errors.push(`${file.name}: حجم الملف كبير جداً (أكثر من 5MB)`);
          continue;
        }

        // حفظ الملف
        const buffer = Buffer.from(await file.arrayBuffer());
        const filePath = path.join(fontsDir, file.name);
        
        // التحقق من عدم وجود ملف بنفس الاسم
        if (fs.existsSync(filePath)) {
          errors.push(`${file.name}: ملف بنفس الاسم موجود بالفعل`);
          continue;
        }

        fs.writeFileSync(filePath, buffer);
        
        uploadedFonts.push({
          name: file.name,
          path: `/fonts/${file.name}`,
          size: file.size,
          type: getFontType(file.name),
          family: extractFontFamily(file.name),
          weight: extractFontWeight(file.name),
          style: extractFontStyle(file.name),
          category: detectFontCategory(file.name)
        });
      } catch (error) {
        errors.push(`${file.name}: خطأ في الرفع - ${error}`);
      }
    }

    return NextResponse.json({
      success: true,
      uploaded: uploadedFonts,
      errors: errors
    });
  } catch (error) {
    console.error('Error uploading fonts:', error);
    return NextResponse.json(
      { error: 'Failed to upload fonts' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const fontName = searchParams.get('name');
    
    if (!fontName) {
      return NextResponse.json(
        { error: 'Font name is required' },
        { status: 400 }
      );
    }

    const fontsDir = path.join(process.cwd(), 'public', 'fonts');
    const filePath = path.join(fontsDir, fontName);
    
    // التحقق من وجود الملف
    if (!fs.existsSync(filePath)) {
      return NextResponse.json(
        { error: 'Font file not found' },
        { status: 404 }
      );
    }

    // حذف الملف
    fs.unlinkSync(filePath);
    
    return NextResponse.json({
      success: true,
      message: `تم حذف الخط ${fontName} بنجاح`
    });
  } catch (error) {
    console.error('Error deleting font:', error);
    return NextResponse.json(
      { error: 'Failed to delete font' },
      { status: 500 }
    );
  }
}
