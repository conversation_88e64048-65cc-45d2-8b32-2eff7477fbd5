<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح موقع المتجر على الخريطة</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        
        h1 {
            color: #333;
            margin-bottom: 20px;
            font-size: 2.5rem;
            text-align: center;
        }
        
        .section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.2);
        }
        
        .button.success {
            background: linear-gradient(45deg, #00b894, #00cec9);
        }
        
        .button.danger {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
        }
        
        .status {
            margin-top: 15px;
            padding: 12px;
            border-radius: 8px;
            font-weight: bold;
            text-align: center;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .coordinates {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
        }
        
        .map-preview {
            width: 100%;
            height: 300px;
            border: 2px solid #ddd;
            border-radius: 10px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 إصلاح موقع المتجر على الخريطة</h1>
        
        <div class="section">
            <h3>🎯 المشكلة:</h3>
            <p>العملاء لا يرون دبوس المتجر أو يرون موقع خاطئ على الخريطة</p>
        </div>
        
        <div class="section">
            <h3>📍 الحالة الحالية:</h3>
            <div id="currentStatus">جاري التحقق...</div>
        </div>
        
        <div class="section">
            <h3>🗺️ معاينة الخريطة الحالية:</h3>
            <iframe id="mapPreview" class="map-preview" src="" allowfullscreen loading="lazy"></iframe>
        </div>
        
        <div class="section">
            <h3>⚙️ إصلاح سريع:</h3>
            
            <div class="coordinates">
                <h4>📊 إحداثيات مكتبة أنوار دارس:</h4>
                <p><strong>الموقع الحقيقي:</strong> 15.434276, 44.2152547</p>
                <p><strong>شارع الزبيري:</strong> 15.3542, 44.2065</p>
                <p><strong>منطقة التحرير:</strong> 15.3656, 44.2081</p>
            </div>
            
            <button class="button success" onclick="fixStoreLocation()">
                🔧 إصلاح موقع المتجر
            </button>
            
            <button class="button" onclick="setCustomLocation()">
                📍 تحديد موقع مخصص
            </button>
            
            <button class="button danger" onclick="resetToDefault()">
                🔄 إعادة تعيين افتراضي
            </button>
        </div>
        
        <div class="section">
            <h3>🧪 اختبار النتيجة:</h3>
            
            <button class="button" onclick="testAsCustomer()">
                👤 اختبار كعميل
            </button>
            
            <button class="button" onclick="testAsAdmin()">
                👨‍💼 اختبار كمدير
            </button>
        </div>
        
        <div id="status" class="status" style="display: none;"></div>
    </div>

    <script>
        function showStatus(message, type = 'success') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
            statusDiv.style.display = 'block';
            
            setTimeout(() => {
                statusDiv.style.display = 'none';
            }, 5000);
        }
        
        function checkCurrentStatus() {
            const settings = localStorage.getItem('admin-settings');
            const statusDiv = document.getElementById('currentStatus');
            
            if (settings) {
                try {
                    const parsed = JSON.parse(settings);
                    const storeLocation = parsed.storeLocation;
                    
                    if (storeLocation) {
                        statusDiv.innerHTML = `
                            <div style="text-align: right;">
                                <p><strong>✅ يوجد موقع محفوظ:</strong></p>
                                <p>📍 خط العرض: ${storeLocation.lat}</p>
                                <p>📍 خط الطول: ${storeLocation.lng}</p>
                                <p>📅 آخر تحديث: ${storeLocation.lastUpdated ? new Date(storeLocation.lastUpdated).toLocaleString('ar') : 'غير محدد'}</p>
                            </div>
                        `;
                        
                        // تحديث معاينة الخريطة
                        updateMapPreview(storeLocation.lat, storeLocation.lng);
                    } else {
                        statusDiv.innerHTML = '<p style="color: red;">❌ لا يوجد موقع محفوظ للمتجر</p>';
                        updateMapPreview('15.434276', '44.2152547');
                    }
                } catch (e) {
                    statusDiv.innerHTML = '<p style="color: red;">❌ خطأ في قراءة البيانات</p>';
                }
            } else {
                statusDiv.innerHTML = '<p style="color: orange;">⚠️ لا توجد إعدادات محفوظة</p>';
                updateMapPreview('15.434276', '44.2152547');
            }
        }
        
        function updateMapPreview(lat, lng) {
            const mapPreview = document.getElementById('mapPreview');
            // استخدام الموقع الحقيقي لمكتبة أنوار دارس
            const mapSrc = `https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d1920!2d${lng}!3d${lat}!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x1603d9e4b0964ecf:0x3927f400397d0531!2z2YXZg9iq2KjYqSDYp9mG2YjYp9ixINiv2KfYsdizINmF2YPYqtio2Kkg2KfZhtmI2KfYsSDYr9in2LHYsw!5e0!3m2!1sar!2sye!4v${Date.now()}!5m2!1sar!2sye`;
            mapPreview.src = mapSrc;
        }
        
        function fixStoreLocation() {
            // إعداد موقع مكتبة أنوار دارس الحقيقي
            const defaultLocation = {
                lat: '15.434276',
                lng: '44.2152547',
                lastUpdated: new Date().toISOString()
            };
            
            const settings = localStorage.getItem('admin-settings');
            let parsedSettings = {};
            
            if (settings) {
                try {
                    parsedSettings = JSON.parse(settings);
                } catch (e) {
                    console.error('خطأ في قراءة الإعدادات:', e);
                }
            }
            
            // إضافة موقع المتجر
            parsedSettings.storeLocation = defaultLocation;
            
            // حفظ الإعدادات
            localStorage.setItem('admin-settings', JSON.stringify(parsedSettings));
            
            showStatus('✅ تم إصلاح موقع المتجر! الموقع الآن في وسط صنعاء.', 'success');
            
            // تحديث العرض
            setTimeout(() => {
                checkCurrentStatus();
            }, 1000);
        }
        
        function setCustomLocation() {
            const lat = prompt('أدخل خط العرض (Latitude):', '15.434276');
            const lng = prompt('أدخل خط الطول (Longitude):', '44.2152547');
            
            if (lat && lng) {
                const customLocation = {
                    lat: lat,
                    lng: lng,
                    lastUpdated: new Date().toISOString()
                };
                
                const settings = localStorage.getItem('admin-settings');
                let parsedSettings = {};
                
                if (settings) {
                    try {
                        parsedSettings = JSON.parse(settings);
                    } catch (e) {
                        console.error('خطأ في قراءة الإعدادات:', e);
                    }
                }
                
                parsedSettings.storeLocation = customLocation;
                localStorage.setItem('admin-settings', JSON.stringify(parsedSettings));
                
                showStatus(`✅ تم تحديد موقع مخصص: ${lat}, ${lng}`, 'success');
                
                setTimeout(() => {
                    checkCurrentStatus();
                }, 1000);
            }
        }
        
        function resetToDefault() {
            if (confirm('هل أنت متأكد من إعادة تعيين موقع المتجر للإعدادات الافتراضية؟')) {
                const settings = localStorage.getItem('admin-settings');
                let parsedSettings = {};
                
                if (settings) {
                    try {
                        parsedSettings = JSON.parse(settings);
                    } catch (e) {
                        console.error('خطأ في قراءة الإعدادات:', e);
                    }
                }
                
                // حذف موقع المتجر
                delete parsedSettings.storeLocation;
                localStorage.setItem('admin-settings', JSON.stringify(parsedSettings));
                
                showStatus('✅ تم إعادة تعيين موقع المتجر للإعدادات الافتراضية', 'success');
                
                setTimeout(() => {
                    checkCurrentStatus();
                }, 1000);
            }
        }
        
        function testAsCustomer() {
            window.open('/contact', '_blank');
            showStatus('تم فتح صفحة الاتصال كعميل. تحقق من ظهور الدبوس في الموقع الصحيح.', 'success');
        }
        
        function testAsAdmin() {
            window.open('/admin', '_blank');
            showStatus('تم فتح لوحة التحكم. ادخل ثم اذهب لصفحة الاتصال لاختبار أزرار التحكم.', 'success');
        }
        
        // فحص تلقائي عند تحميل الصفحة
        window.onload = function() {
            console.log('🚀 أداة إصلاح موقع المتجر جاهزة');
            checkCurrentStatus();
        };
    </script>
</body>
</html>
