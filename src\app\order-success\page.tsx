'use client';

import React from 'react';
import Link from 'next/link';
import { useAdmin } from '@/contexts/AdminContext';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { CheckCircle, Phone, MessageCircle, Home, ShoppingBag } from 'lucide-react';

export default function OrderSuccessPage() {
  const { settings } = useAdmin();

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="container mx-auto px-4 py-16">
        <div className="max-w-2xl mx-auto text-center">
          {/* Success Icon */}
          <div className="bg-green-100 w-24 h-24 rounded-full flex items-center justify-center mx-auto mb-8">
            <CheckCircle className="w-12 h-12 text-green-600" />
          </div>

          {/* Success Message */}
          <h1 className="text-3xl font-bold text-gray-800 mb-4">
            تم إرسال طلبك بنجاح! 🎉
          </h1>
          
          <p className="text-lg text-gray-600 mb-8">
            شكراً لك على ثقتك بنا. سنتواصل معك قريباً لتأكيد الطلب وترتيب التوصيل.
          </p>

          {/* Order Details */}
          <div className="bg-white rounded-lg shadow-md p-6 mb-8 text-right">
            <h2 className="text-xl font-semibold mb-4">تفاصيل الطلب</h2>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">رقم الطلب:</span>
                <span className="font-semibold">#{Date.now().toString().slice(-6)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">تاريخ الطلب:</span>
                <span className="font-semibold">{new Date().toLocaleDateString('ar-YE')}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">حالة الطلب:</span>
                <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-sm font-semibold">
                  قيد المراجعة
                </span>
              </div>
            </div>
          </div>

          {/* Next Steps */}
          <div className="bg-blue-50 rounded-lg p-6 mb-8 text-right">
            <h3 className="text-lg font-semibold text-blue-800 mb-4">الخطوات التالية:</h3>
            <div className="space-y-3 text-blue-700">
              <div className="flex items-start">
                <div className="bg-blue-200 w-6 h-6 rounded-full flex items-center justify-center text-sm font-bold ml-3 mt-0.5">
                  1
                </div>
                <p>سنراجع طلبك ونتأكد من توفر جميع المنتجات</p>
              </div>
              <div className="flex items-start">
                <div className="bg-blue-200 w-6 h-6 rounded-full flex items-center justify-center text-sm font-bold ml-3 mt-0.5">
                  2
                </div>
                <p>سنتصل بك خلال 24 ساعة لتأكيد الطلب وتفاصيل التوصيل</p>
              </div>
              <div className="flex items-start">
                <div className="bg-blue-200 w-6 h-6 rounded-full flex items-center justify-center text-sm font-bold ml-3 mt-0.5">
                  3
                </div>
                <p>سنقوم بتحضير طلبك وإرساله إليك في الموعد المحدد</p>
              </div>
            </div>
          </div>

          {/* Contact Information */}
          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <h3 className="text-lg font-semibold mb-4">هل تحتاج مساعدة؟</h3>
            <p className="text-gray-600 mb-4">
              يمكنك التواصل معنا في أي وقت للاستفسار عن طلبك
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href={`tel:${settings.contactInfo.phone}`}
                className="flex items-center justify-center bg-primary-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-primary-700 transition-colors"
              >
                <Phone className="w-5 h-5 ml-2" />
                اتصل بنا
              </a>
              {settings.contactInfo.socialMedia.whatsapp && (
                <a
                  href={`https://wa.me/${settings.contactInfo.socialMedia.whatsapp.replace(/[^0-9]/g, '')}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center justify-center bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors"
                >
                  <MessageCircle className="w-5 h-5 ml-2" />
                  واتساب
                </a>
              )}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/"
              className="flex items-center justify-center bg-primary-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-primary-700 transition-colors"
            >
              <Home className="w-5 h-5 ml-2" />
              العودة للرئيسية
            </Link>
            <Link
              href="/products"
              className="flex items-center justify-center border border-primary-600 text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-primary-50 transition-colors"
            >
              <ShoppingBag className="w-5 h-5 ml-2" />
              متابعة التسوق
            </Link>
          </div>

          {/* Additional Information */}
          <div className="mt-12 text-sm text-gray-500">
            <p className="mb-2">
              ستصلك رسالة تأكيد على رقم الهاتف المسجل عند تأكيد الطلب
            </p>
            <p>
              في حالة عدم التواصل معك خلال 48 ساعة، يرجى الاتصال بنا على {settings.contactInfo.phone}
            </p>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
