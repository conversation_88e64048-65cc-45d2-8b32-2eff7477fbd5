/* ملف الخطوط المخصصة للموقع */
/* Custom Fonts File for Website */

/* 
  تعليمات الاستخدام:
  1. ضع ملفات الخطوط في مجلد public/fonts/
  2. أضف تعريف الخط هنا باستخدام @font-face
  3. استخدم الخط في ملف globals.css أو في أي مكون
*/

/* مثال على إضافة خط عربي مخصص */
/*
@font-face {
  font-family: 'CustomArabicFont';
  src: url('./CustomArabicFont-Regular.woff2') format('woff2'),
       url('./CustomArabicFont-Regular.woff') format('woff'),
       url('./CustomArabicFont-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'CustomArabicFont';
  src: url('./CustomArabicFont-Bold.woff2') format('woff2'),
       url('./CustomArabicFont-Bold.woff') format('woff'),
       url('./CustomArabicFont-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}
*/

/* مثال على إضافة خط إنجليزي مخصص */
/*
@font-face {
  font-family: 'CustomEnglishFont';
  src: url('./CustomEnglishFont-Regular.woff2') format('woff2'),
       url('./CustomEnglishFont-Regular.woff') format('woff'),
       url('./CustomEnglishFont-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'CustomEnglishFont';
  src: url('./CustomEnglishFont-Bold.woff2') format('woff2'),
       url('./CustomEnglishFont-Bold.woff') format('woff'),
       url('./CustomEnglishFont-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}
*/

/* خطوط Google Fonts المحلية (إذا كنت تريد تحميلها محلياً) */
/*
@font-face {
  font-family: 'Cairo';
  src: url('./Cairo-Regular.woff2') format('woff2'),
       url('./Cairo-Regular.woff') format('woff');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Cairo';
  src: url('./Cairo-Bold.woff2') format('woff2'),
       url('./Cairo-Bold.woff') format('woff');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}
*/

/* إضافة خطوط متعددة الأوزان */
/*
@font-face {
  font-family: 'MyCustomFont';
  src: url('./MyCustomFont-Light.woff2') format('woff2');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'MyCustomFont';
  src: url('./MyCustomFont-Regular.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'MyCustomFont';
  src: url('./MyCustomFont-Medium.woff2') format('woff2');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'MyCustomFont';
  src: url('./MyCustomFont-SemiBold.woff2') format('woff2');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'MyCustomFont';
  src: url('./MyCustomFont-Bold.woff2') format('woff2');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'MyCustomFont';
  src: url('./MyCustomFont-ExtraBold.woff2') format('woff2');
  font-weight: 800;
  font-style: normal;
  font-display: swap;
}
*/

/* 
  ملاحظات مهمة:
  
  1. أفضل تنسيقات الخطوط (بالترتيب):
     - WOFF2 (الأحدث والأصغر حجماً)
     - WOFF (متوافق مع المتصفحات القديمة)
     - TTF (احتياطي)
  
  2. font-display: swap يحسن الأداء
  
  3. تأكد من رفع ملفات الخطوط في نفس المجلد
  
  4. استخدم أسماء واضحة للخطوط
  
  5. لاستخدام الخط في CSS:
     font-family: 'اسم الخط المخصص', 'خط احتياطي', sans-serif;
*/

/* مثال على خط عربي شائع - Amiri */
/*
@font-face {
  font-family: 'Amiri';
  src: url('./Amiri-Regular.woff2') format('woff2'),
       url('./Amiri-Regular.woff') format('woff');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Amiri';
  src: url('./Amiri-Bold.woff2') format('woff2'),
       url('./Amiri-Bold.woff') format('woff');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}
*/

/* مثال على خط عربي حديث - Tajawal */
/*
@font-face {
  font-family: 'Tajawal';
  src: url('./Tajawal-Regular.woff2') format('woff2'),
       url('./Tajawal-Regular.woff') format('woff');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Tajawal';
  src: url('./Tajawal-Bold.woff2') format('woff2'),
       url('./Tajawal-Bold.woff') format('woff');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}
*/

/* ضع تعريفات خطوطك المخصصة هنا */
/* Add your custom font definitions here */
