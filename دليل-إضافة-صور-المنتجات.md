# دليل إضافة صور المنتجات 📸

## 🎯 طرق إضافة المنتجات:

### 1. **من لوحة التحكم (الطريقة الأساسية):**
1. **ادخل لوحة التحكم:** `http://localhost:3000/admin`
2. **سجل دخول:** `admin` / `admin77111`
3. **اختر تبويب "المنتجات"**
4. **اضغط "إضافة منتج جديد"**
5. **املأ البيانات المطلوبة**

### 2. **رفع الصور الجديد (محسن):**
- ✅ **سحب وإفلات:** اسحب الصورة مباشرة إلى المنطقة المخصصة
- ✅ **اختيار ملف:** اضغط "اختيار صورة" لتصفح الملفات
- ✅ **رابط يدوي:** أدخل رابط الصورة يدوياً
- ✅ **معاينة فورية:** شاهد الصورة قبل الحفظ
- ✅ **التحقق التلقائي:** فحص نوع وحجم الملف تلقائياً

### 2. **الحقول المطلوبة:**
- ✅ **اسم المنتج** (مطلوب)
- ✅ **وصف المنتج** (مطلوب)
- ✅ **السعر** (مطلوب - أكبر من صفر)
- ✅ **الفئة** (مطلوبة)
- ✅ **الكمية** (مطلوبة - لا تقل عن صفر)

### 3. **الحقول الاختيارية:**
- 📷 **صورة المنتج**
- 💰 **السعر الأصلي** (للخصومات)
- 🏷️ **الفئة الفرعية**
- 🏢 **العلامة التجارية**
- 🔖 **الكلمات المفتاحية** (مفصولة بفواصل)
- ⭐ **منتج مميز** (يظهر في القسم المميز)
- 📋 **المواصفات التقنية**

---

## 📸 إضافة صور المنتجات:

### **الطريقة الأولى: رفع الصور محلياً**

#### 1. **إنشاء مجلد الصور:**
```
public/
└── images/
    └── products/
        ├── books/
        ├── stationery/
        ├── electronics/
        └── printing/
```

#### 2. **رفع الصور:**
1. **انسخ صور المنتجات** إلى المجلد المناسب
2. **استخدم أسماء واضحة** مثل: `math-book-grade12.jpg`
3. **في نموذج إضافة المنتج** أدخل المسار:
   ```
   /images/products/books/math-book-grade12.jpg
   ```

### **الطريقة الثانية: استخدام روابط خارجية**

#### 1. **رفع على خدمة سحابية:**
- **Google Drive** (مع جعل الرابط عام)
- **Dropbox** (مع رابط مباشر)
- **Imgur** (مجاني للصور)
- **Cloudinary** (متخصص للصور)

#### 2. **في نموذج المنتج:**
```
https://example.com/image.jpg
```

---

## 📏 مقاسات وشروط الصور:

### **المقاسات المثلى:**

#### **للمنتجات العادية:**
- ✅ **العرض:** 800-1200 بكسل
- ✅ **الارتفاع:** 800-1200 بكسل
- ✅ **النسبة:** 1:1 (مربع) أو 4:3
- ✅ **الدقة:** 72-150 DPI

#### **للمنتجات المميزة:**
- ✅ **العرض:** 1200-1600 بكسل
- ✅ **الارتفاع:** 1200-1600 بكسل
- ✅ **النسبة:** 1:1 (مربع)
- ✅ **الدقة:** 150-300 DPI

### **أنواع الملفات المدعومة:**
- ✅ **JPG/JPEG** (الأفضل للصور الفوتوغرافية)
- ✅ **PNG** (الأفضل للصور بخلفية شفافة)
- ✅ **WebP** (حجم أصغر، جودة عالية)
- ✅ **SVG** (للرسوم البيانية والأيقونات)

### **حجم الملفات:**
- ✅ **الحد الأدنى:** 50 KB
- ✅ **الحد الأقصى:** 2 MB
- ✅ **المثالي:** 200-500 KB

### **جودة الصور:**
- ✅ **واضحة وحادة** (غير مشوشة)
- ✅ **إضاءة جيدة** (ليست مظلمة أو ساطعة جداً)
- ✅ **خلفية نظيفة** (أبيض أو شفاف مفضل)
- ✅ **تركيز على المنتج** (المنتج في المقدمة)

---

## 🛠️ أدوات تحسين الصور:

### **أدوات مجانية:**
1. **GIMP** (تحرير متقدم)
2. **Paint.NET** (بسيط وسهل)
3. **Canva** (تصميم أونلاين)
4. **Photopea** (فوتوشوب أونلاين)

### **أدوات ضغط الصور:**
1. **TinyPNG** (ضغط PNG/JPG)
2. **Squoosh** (من Google)
3. **ImageOptim** (للماك)
4. **RIOT** (للويندوز)

### **أدوات تحويل الصيغ:**
1. **CloudConvert** (أونلاين)
2. **XnConvert** (برنامج مجاني)
3. **IrfanView** (عارض ومحول)

---

## 📋 خطوات إضافة منتج بالصور:

### **الخطوة 1: تحضير الصورة**
1. **اختر صورة واضحة** للمنتج
2. **اقتصها** لتكون مربعة (1:1)
3. **اضبط الحجم** ليكون 800x800 بكسل
4. **احفظها** بصيغة JPG أو PNG
5. **تأكد من الحجم** أقل من 1 MB

### **الخطوة 2: رفع الصورة**
```bash
# انسخ الصورة إلى:
public/images/products/[الفئة]/[اسم-المنتج].jpg

# مثال:
public/images/products/books/math-grade12.jpg
```

### **الخطوة 3: إضافة المنتج**
1. **ادخل لوحة التحكم**
2. **اضغط "إضافة منتج جديد"**
3. **املأ البيانات:**
   - **الاسم:** كتاب الرياضيات - الصف الثاني عشر
   - **الوصف:** كتاب منهجي شامل...
   - **السعر:** 2500
   - **الصورة:** `/images/products/books/math-grade12.jpg`
   - **الفئة:** الكتب والملازم
   - **الكمية:** 50

### **الخطوة 4: حفظ ومراجعة**
1. **اضغط "حفظ"**
2. **تحقق من ظهور المنتج** في قائمة المنتجات
3. **اذهب للصفحة الرئيسية** وتأكد من ظهور الصورة

---

## 🎨 نصائح للحصول على أفضل النتائج:

### **للكتب والملازم:**
- 📚 **صور الغلاف** واضحة ومستقيمة
- 📖 **خلفية بيضاء** أو شفافة
- 📝 **النص مقروء** في الصورة
- 📐 **زوايا مستقيمة** غير مائلة

### **للقرطاسية:**
- ✏️ **ترتيب جميل** للأدوات
- 🎨 **ألوان واضحة** ومشرقة
- 📏 **تفاصيل واضحة** للمنتج
- 🖍️ **خلفية متباينة** مع لون المنتج

### **للإلكترونيات:**
- 💻 **زوايا متعددة** إن أمكن
- 🔌 **تفاصيل المنافذ** واضحة
- 📱 **الشاشة مضاءة** إن وجدت
- ⚡ **الملحقات ظاهرة** إن وجدت

### **لخدمات الطباعة:**
- 🖨️ **عينات من الأعمال** المطبوعة
- 📄 **جودة الطباعة** واضحة
- 🎯 **قبل وبعد** إن أمكن
- 📊 **أنواع الورق** المختلفة

---

## 🔍 استكشاف الأخطاء:

### **إذا لم تظهر الصورة:**
1. **تحقق من المسار** صحيح
2. **تأكد من وجود الملف** في المجلد
3. **تحقق من أذونات الملف**
4. **جرب رابط خارجي** للاختبار

### **إذا كانت الصورة بطيئة:**
1. **قلل حجم الملف** (أقل من 500 KB)
2. **استخدم صيغة WebP** إن أمكن
3. **اضغط الصورة** بأدوات الضغط
4. **قلل الدقة** إلى 72 DPI

### **إذا كانت الصورة مشوهة:**
1. **تأكد من النسبة** 1:1 أو 4:3
2. **لا تمدد الصورة** بقوة
3. **استخدم اقتصاص ذكي**
4. **حافظ على جودة الأصل**

---

## 📞 للمساعدة:

### **مشاكل تقنية:**
- تحقق من console المتصفح (F12)
- تأكد من إعدادات next.config.js
- راجع أذونات المجلدات

### **مشاكل التصميم:**
- استخدم أدوات تحرير الصور
- اطلب المساعدة من مصمم
- ابحث عن قوالب جاهزة

### **مشاكل الأداء:**
- اضغط الصور قبل الرفع
- استخدم CDN للصور
- فعل lazy loading

---

## ✅ قائمة مراجعة سريعة:

- [ ] **الصورة واضحة** وعالية الجودة
- [ ] **المقاس مناسب** (800x800 بكسل)
- [ ] **الحجم معقول** (أقل من 1 MB)
- [ ] **الصيغة مدعومة** (JPG/PNG)
- [ ] **المسار صحيح** في النموذج
- [ ] **الملف موجود** في المجلد
- [ ] **البيانات مكتملة** (اسم، وصف، سعر، فئة)
- [ ] **تم الحفظ** بنجاح
- [ ] **الصورة تظهر** في الموقع

الآن يمكنك إضافة منتجات بصور جميلة واحترافية! 🎉
