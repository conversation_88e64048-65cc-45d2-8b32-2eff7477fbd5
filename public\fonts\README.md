# دليل إدارة الخطوط المخصصة
# Custom Fonts Management Guide

## 📁 هيكل المجلد
```
public/fonts/
├── fonts.css          # ملف تعريف الخطوط
├── README.md          # هذا الملف
└── [ملفات الخطوط]    # ضع ملفات الخطوط هنا
```

## 🚀 كيفية إضافة خط جديد:

### الخطوة 1: رفع ملفات الخط
ضع ملفات الخط في هذا المجلد (`public/fonts/`)

**أفضل التنسيقات:**
- `.woff2` (الأحدث والأصغر حجماً)
- `.woff` (متوافق مع المتصفحات القديمة)
- `.ttf` (احتياطي)

### الخطوة 2: تعريف الخط في fonts.css
افتح ملف `fonts.css` وأضف تعريف الخط:

```css
@font-face {
  font-family: 'اسم الخط';
  src: url('./اسم-الملف.woff2') format('woff2'),
       url('./اسم-الملف.woff') format('woff'),
       url('./اسم-الملف.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}
```

### الخطوة 3: ربط ملف الخطوط بالموقع
أضف هذا السطر في ملف `src/app/layout.tsx`:

```jsx
import '../styles/globals.css';
import '../../public/fonts/fonts.css'; // أضف هذا السطر
```

أو في ملف `src/styles/globals.css`:

```css
@import url('/fonts/fonts.css');
```

### الخطوة 4: استخدام الخط
في أي ملف CSS أو Tailwind:

```css
/* في CSS عادي */
.my-text {
  font-family: 'اسم الخط', 'Cairo', sans-serif;
}

/* في Tailwind (بعد إضافته في tailwind.config.js) */
<div className="font-custom">النص</div>
```

## 📝 أمثلة عملية:

### مثال 1: خط عربي (Cairo)
```css
@font-face {
  font-family: 'Cairo';
  src: url('./Cairo-Regular.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Cairo';
  src: url('./Cairo-Bold.woff2') format('woff2');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}
```

### مثال 2: خط إنجليزي (Roboto)
```css
@font-face {
  font-family: 'Roboto';
  src: url('./Roboto-Regular.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}
```

## ⚙️ إعداد Tailwind للخطوط المخصصة:

في ملف `tailwind.config.js`:

```javascript
module.exports = {
  theme: {
    extend: {
      fontFamily: {
        'custom': ['اسم الخط المخصص', 'Cairo', 'sans-serif'],
        'arabic': ['Cairo', 'Amiri', 'sans-serif'],
        'english': ['Roboto', 'Inter', 'sans-serif'],
      }
    }
  }
}
```

ثم استخدمه:
```jsx
<h1 className="font-custom">عنوان بالخط المخصص</h1>
<p className="font-arabic">نص عربي</p>
<p className="font-english">English text</p>
```

## 🎯 نصائح مهمة:

### 1. تحسين الأداء:
- استخدم `font-display: swap` دائماً
- فضل WOFF2 على التنسيقات الأخرى
- ضغط ملفات الخطوط قبل الرفع

### 2. التوافق:
- اختبر الخطوط على متصفحات مختلفة
- ضع خطوط احتياطية دائماً
- تأكد من دعم الخط للغة العربية

### 3. التنظيم:
- استخدم أسماء واضحة للملفات
- نظم الخطوط في مجلدات فرعية إذا لزم الأمر
- احتفظ بنسخة احتياطية من الخطوط

## 🔧 استكشاف الأخطاء:

### المشكلة: الخط لا يظهر
**الحلول:**
1. تأكد من مسار الملف صحيح
2. تحقق من تنسيق الملف
3. امسح cache المتصفح
4. تأكد من ربط ملف fonts.css

### المشكلة: الخط يظهر متأخراً
**الحلول:**
1. استخدم `font-display: swap`
2. قم بتحميل الخط مسبقاً في HTML head
3. ضغط ملف الخط

### المشكلة: الخط لا يدعم العربية
**الحلول:**
1. تأكد من أن الخط يدعم Unicode العربي
2. استخدم خط احتياطي عربي
3. اختبر الخط في محرر نصوص أولاً

## 📚 مصادر الخطوط العربية:

### مجانية:
- Google Fonts (Cairo, Amiri, Tajawal)
- Adobe Fonts
- Font Squirrel

### مدفوعة:
- MyFonts
- Fonts.com
- Arabic Typography

## 🎨 خطوط عربية مُوصى بها:

### للعناوين:
- Amiri (تقليدي)
- Cairo (حديث)
- Tajawal (عصري)

### للنصوص:
- Noto Sans Arabic
- IBM Plex Sans Arabic
- Almarai

### للشعارات:
- Kufam
- Lalezar
- Reem Kufi
