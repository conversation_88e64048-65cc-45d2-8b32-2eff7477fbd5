'use client';

import React from 'react';
import { useAdmin } from '@/contexts/AdminContext';
import { useToast } from '@/components/ToastContainer';
import { Database, RefreshCw, Trash2, Save } from 'lucide-react';

const DataTestPanel: React.FC = () => {
  const { 
    products, 
    categories, 
    orders, 
    settings,
    addProduct,
    addCategory,
    resetAdminData
  } = useAdmin();
  
  const { showSuccess, showInfo } = useToast();

  const testAddProduct = () => {
    const testProduct = {
      name: `منتج تجريبي ${Date.now()}`,
      description: 'هذا منتج للاختبار',
      price: Math.floor(Math.random() * 1000) + 100,
      image: '/images/products/placeholder-default.svg',
      category: categories[0]?.id || '1',
      quantity: 10,
      inStock: true,
      featured: false,
      brand: 'علامة تجريبية'
    };
    
    addProduct(testProduct);
    showSuccess('تم إضافة منتج تجريبي', `تم إضافة "${testProduct.name}"`);
  };

  const testAddCategory = () => {
    const icons = ['🧪', '🎯', '⚡', '🌟', '🔥', '💎', '🚀', '🎨'];
    const randomIcon = icons[Math.floor(Math.random() * icons.length)];

    const testCategory = {
      name: `فئة تجريبية ${Date.now()}`,
      description: 'هذه فئة للاختبار مع فئات فرعية',
      icon: randomIcon,
      subcategories: [
        {
          id: `sub-${Date.now()}-1`,
          name: 'فئة فرعية أولى',
          description: 'وصف الفئة الفرعية الأولى',
          categoryId: `${Date.now()}`
        },
        {
          id: `sub-${Date.now()}-2`,
          name: 'فئة فرعية ثانية',
          description: 'وصف الفئة الفرعية الثانية',
          categoryId: `${Date.now()}`
        }
      ]
    };

    addCategory(testCategory);
    showSuccess('تم إضافة فئة تجريبية', `تم إضافة "${testCategory.name}" مع ${testCategory.subcategories.length} فئة فرعية`);
  };

  const checkLocalStorage = () => {
    const data = {
      products: localStorage.getItem('admin_products'),
      categories: localStorage.getItem('admin_categories'),
      orders: localStorage.getItem('admin_orders'),
      settings: localStorage.getItem('admin_settings')
    };
    
    console.log('📊 LocalStorage Data:', data);
    showInfo('تم فحص البيانات', 'تحقق من console للتفاصيل');
  };

  const clearLocalStorage = () => {
    localStorage.removeItem('admin_products');
    localStorage.removeItem('admin_categories');
    localStorage.removeItem('admin_orders');
    localStorage.removeItem('admin_settings');
    showInfo('تم مسح البيانات', 'سيتم إعادة تحميل الصفحة');
    setTimeout(() => window.location.reload(), 1000);
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex items-center mb-6">
        <Database className="w-6 h-6 text-blue-600 ml-3" />
        <h2 className="text-xl font-semibold">لوحة اختبار البيانات</h2>
      </div>

      {/* Current Data Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-blue-50 p-4 rounded-lg text-center">
          <div className="text-2xl font-bold text-blue-600">{products.length}</div>
          <div className="text-sm text-gray-600">المنتجات</div>
        </div>
        <div className="bg-green-50 p-4 rounded-lg text-center">
          <div className="text-2xl font-bold text-green-600">{categories.length}</div>
          <div className="text-sm text-gray-600">الفئات</div>
        </div>
        <div className="bg-yellow-50 p-4 rounded-lg text-center">
          <div className="text-2xl font-bold text-yellow-600">{orders.length}</div>
          <div className="text-sm text-gray-600">الطلبات</div>
        </div>
        <div className="bg-purple-50 p-4 rounded-lg text-center">
          <div className="text-2xl font-bold text-purple-600">
            {settings ? '✓' : '✗'}
          </div>
          <div className="text-sm text-gray-600">الإعدادات</div>
        </div>
      </div>

      {/* Test Actions */}
      <div className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <button
            onClick={testAddProduct}
            className="flex items-center justify-center px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Save className="w-5 h-5 ml-2" />
            إضافة منتج تجريبي
          </button>
          
          <button
            onClick={testAddCategory}
            className="flex items-center justify-center px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
          >
            <Save className="w-5 h-5 ml-2" />
            إضافة فئة تجريبية
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <button
            onClick={checkLocalStorage}
            className="flex items-center justify-center px-4 py-3 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors"
          >
            <Database className="w-5 h-5 ml-2" />
            فحص localStorage
          </button>
          
          <button
            onClick={() => window.location.reload()}
            className="flex items-center justify-center px-4 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
          >
            <RefreshCw className="w-5 h-5 ml-2" />
            إعادة تحميل الصفحة
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <button
            onClick={resetAdminData}
            className="flex items-center justify-center px-4 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
          >
            <RefreshCw className="w-5 h-5 ml-2" />
            إعادة تعيين البيانات
          </button>
          
          <button
            onClick={clearLocalStorage}
            className="flex items-center justify-center px-4 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            <Trash2 className="w-5 h-5 ml-2" />
            مسح localStorage
          </button>
        </div>
      </div>

      {/* Instructions */}
      <div className="mt-6 p-4 bg-gray-50 rounded-lg">
        <h3 className="font-semibold mb-2">تعليمات الاختبار:</h3>
        <ol className="text-sm text-gray-600 space-y-1">
          <li>1. اضغط "إضافة منتج تجريبي" أو "إضافة فئة تجريبية"</li>
          <li>2. تحقق من زيادة العدد في الإحصائيات أعلاه</li>
          <li>3. اضغط "إعادة تحميل الصفحة" للتأكد من حفظ البيانات</li>
          <li>4. إذا لم تُحفظ البيانات، تحقق من console المتصفح</li>
          <li>5. استخدم "فحص localStorage" لرؤية البيانات المحفوظة</li>
        </ol>
      </div>
    </div>
  );
};

export default DataTestPanel;
