import type { Metadata, Viewport } from 'next'
import './globals.css'
import { CartProvider } from '@/contexts/CartContext'
import { AdminProvider } from '@/contexts/AdminContext'
import ThemeProvider from '@/components/ThemeProvider'
import { ToastProvider } from '@/components/ToastContainer'
import ThemeApplier from '@/components/ThemeApplier'
import CustomFontLoader from '@/components/CustomFontLoader'

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
}

export const metadata: Metadata = {
  title: 'مكتبة أنوار دارس - مكتبتك الشاملة للكتب والقرطاسية',
  description: 'مكتبة إلكترونية يمنية شاملة للكتب والقرطاسية والأدوات التعليمية وخدمات الطباعة والتصوير',
  keywords: 'مكتبة، اليمن، كتب، قرطاسية، أدوات تعليمية، طباعة، تصوير، أنوار دارس',
  authors: [{ name: 'مكتبة أنوار دارس' }],
  robots: 'index, follow',
  openGraph: {
    title: 'مكتبة أنوار دارس',
    description: 'مكتبتك الشاملة للكتب والقرطاسية',
    type: 'website',
    locale: 'ar_YE',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="ar" dir="rtl">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link rel="stylesheet" href="/fonts/fonts.css" />
      </head>
      <body className="font-arabic bg-gray-50 min-h-screen">
        <AdminProvider>
          <CartProvider>
            <ToastProvider>
              <ThemeProvider>
                <ThemeApplier>
                  <CustomFontLoader />
                  {children}
                </ThemeApplier>
              </ThemeProvider>
            </ToastProvider>
          </CartProvider>
        </AdminProvider>
      </body>
    </html>
  )
}
