@echo off
echo ========================================
echo    فحص متطلبات تشغيل المشروع
echo ========================================
echo.

echo 1. فحص Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت!
    echo 📥 يرجى تحميله من: https://nodejs.org
    echo ⚠️  اختر النسخة LTS
    goto :end
) else (
    echo ✅ Node.js مثبت
    node --version
)

echo.
echo 2. فحص npm...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm غير متاح!
    echo 🔧 يُثبت عادة مع Node.js
    goto :end
) else (
    echo ✅ npm متاح
    npm --version
)

echo.
echo 3. فحص مساحة القرص...
for /f "tokens=3" %%a in ('dir /-c ^| find "bytes free"') do set free=%%a
if %free% LSS 2000000000 (
    echo ⚠️  مساحة القرص قليلة - يُفضل 2 جيجابايت على الأقل
) else (
    echo ✅ مساحة القرص كافية
)

echo.
echo 4. فحص الاتصال بالإنترنت...
ping -n 1 google.com >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  لا يوجد اتصال إنترنت - مطلوب لتحميل المكتبات
) else (
    echo ✅ الاتصال بالإنترنت متاح
)

echo.
echo ========================================
echo          ملخص الفحص
echo ========================================
echo ✅ جميع المتطلبات متوفرة!
echo 🚀 يمكنك الآن تشغيل المشروع
echo.
echo للتشغيل:
echo 1. npm install
echo 2. npm run dev
echo.

:end
pause
