// أدوات إدارة الخطوط
// Font Management Utilities

export interface FontFile {
  name: string;
  path: string;
  size: number;
  type: string;
  family?: string;
  weight?: string;
  style?: string;
}

export interface FontFamily {
  name: string;
  files: FontFile[];
  category: 'arabic' | 'english' | 'mixed';
  preview: string;
}

// قائمة الخطوط النظام المتاحة
export const systemFonts: FontFamily[] = [
  {
    name: 'Cairo',
    files: [],
    category: 'arabic',
    preview: 'خط القاهرة الحديث والواضح للنصوص العربية'
  },
  {
    name: '<PERSON><PERSON>',
    files: [],
    category: 'arabic',
    preview: 'خط أميري التقليدي والأنيق للنصوص الكلاسيكية'
  },
  {
    name: 'Tajawal',
    files: [],
    category: 'arabic',
    preview: 'خط تجوال العصري والجميل للعناوين والنصوص'
  },
  {
    name: '<PERSON><PERSON>',
    files: [],
    category: 'arabic',
    preview: 'خط المرعي البسيط والواضح للنصوص الصغيرة'
  },
  {
    name: 'Noto Sans Arabic',
    files: [],
    category: 'arabic',
    preview: 'خط نوتو العربي المتوازن والشامل'
  },
  {
    name: 'IBM Plex Sans Arabic',
    files: [],
    category: 'arabic',
    preview: 'خط IBM العربي الاحترافي والحديث'
  },
  {
    name: 'Inter',
    files: [],
    category: 'english',
    preview: 'Modern and clean English font for interfaces'
  },
  {
    name: 'Roboto',
    files: [],
    category: 'english',
    preview: 'Classic and readable Google font'
  },
  {
    name: 'Poppins',
    files: [],
    category: 'english',
    preview: 'Elegant and modern geometric font'
  },
  {
    name: 'Open Sans',
    files: [],
    category: 'english',
    preview: 'Friendly and readable humanist font'
  },
  {
    name: 'Lato',
    files: [],
    category: 'english',
    preview: 'Semi-rounded details of the letters'
  },
  {
    name: 'Montserrat',
    files: [],
    category: 'english',
    preview: 'Urban typography inspired by old posters'
  }
];

// دالة لتحديد نوع الخط من الامتداد
export const getFontType = (filename: string): string => {
  const extension = filename.split('.').pop()?.toLowerCase();
  switch (extension) {
    case 'woff2':
      return 'woff2';
    case 'woff':
      return 'woff';
    case 'ttf':
      return 'truetype';
    case 'otf':
      return 'opentype';
    case 'eot':
      return 'embedded-opentype';
    default:
      return 'unknown';
  }
};

// دالة لاستخراج اسم عائلة الخط من اسم الملف
export const extractFontFamily = (filename: string): string => {
  // إزالة الامتداد
  const nameWithoutExt = filename.replace(/\.(woff2?|ttf|otf|eot)$/i, '');
  
  // إزالة أوزان الخط الشائعة
  const cleanName = nameWithoutExt
    .replace(/-(Regular|Bold|Light|Medium|SemiBold|ExtraBold|Black|Thin|Italic|Oblique)$/i, '')
    .replace(/_(Regular|Bold|Light|Medium|SemiBold|ExtraBold|Black|Thin|Italic|Oblique)$/i, '')
    .replace(/(Regular|Bold|Light|Medium|SemiBold|ExtraBold|Black|Thin|Italic|Oblique)$/i, '');
  
  return cleanName.trim();
};

// دالة لاستخراج وزن الخط من اسم الملف
export const extractFontWeight = (filename: string): string => {
  const lowerName = filename.toLowerCase();
  
  if (lowerName.includes('thin')) return '100';
  if (lowerName.includes('extralight') || lowerName.includes('ultralight')) return '200';
  if (lowerName.includes('light')) return '300';
  if (lowerName.includes('regular') || lowerName.includes('normal')) return '400';
  if (lowerName.includes('medium')) return '500';
  if (lowerName.includes('semibold') || lowerName.includes('demibold')) return '600';
  if (lowerName.includes('bold')) return '700';
  if (lowerName.includes('extrabold') || lowerName.includes('ultrabold')) return '800';
  if (lowerName.includes('black') || lowerName.includes('heavy')) return '900';
  
  return '400'; // افتراضي
};

// دالة لاستخراج نمط الخط من اسم الملف
export const extractFontStyle = (filename: string): string => {
  const lowerName = filename.toLowerCase();
  
  if (lowerName.includes('italic') || lowerName.includes('oblique')) {
    return 'italic';
  }
  
  return 'normal';
};

// دالة لتحديد فئة الخط (عربي/إنجليزي/مختلط)
export const detectFontCategory = (fontName: string): 'arabic' | 'english' | 'mixed' => {
  const arabicKeywords = [
    'arabic', 'amiri', 'cairo', 'tajawal', 'almarai', 'noto.*arabic', 
    'ibm.*arabic', 'dubai', 'droid.*arabic', 'scheherazade', 'lateef',
    'markazi', 'harmattan', 'reem', 'kufi', 'naskh', 'thuluth'
  ];
  
  const englishKeywords = [
    'inter', 'roboto', 'poppins', 'open.*sans', 'lato', 'montserrat',
    'helvetica', 'arial', 'times', 'georgia', 'verdana', 'trebuchet'
  ];
  
  const lowerName = fontName.toLowerCase();
  
  // فحص الكلمات المفتاحية العربية
  for (const keyword of arabicKeywords) {
    if (new RegExp(keyword).test(lowerName)) {
      return 'arabic';
    }
  }
  
  // فحص الكلمات المفتاحية الإنجليزية
  for (const keyword of englishKeywords) {
    if (new RegExp(keyword).test(lowerName)) {
      return 'english';
    }
  }
  
  return 'mixed';
};

// دالة لتنسيق حجم الملف
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// دالة لإنشاء CSS للخط
export const generateFontFaceCSS = (fontFile: FontFile): string => {
  const family = fontFile.family || extractFontFamily(fontFile.name);
  const weight = fontFile.weight || extractFontWeight(fontFile.name);
  const style = fontFile.style || extractFontStyle(fontFile.name);
  const format = getFontType(fontFile.name);
  
  return `@font-face {
  font-family: '${family}';
  src: url('${fontFile.path}') format('${format}');
  font-weight: ${weight};
  font-style: ${style};
  font-display: swap;
}`;
};

// دالة لتجميع الخطوط حسب العائلة
export const groupFontsByFamily = (fonts: FontFile[]): FontFamily[] => {
  const families: { [key: string]: FontFamily } = {};
  
  fonts.forEach(font => {
    const familyName = font.family || extractFontFamily(font.name);
    
    if (!families[familyName]) {
      families[familyName] = {
        name: familyName,
        files: [],
        category: detectFontCategory(familyName),
        preview: `نص تجريبي بخط ${familyName}`
      };
    }
    
    families[familyName].files.push(font);
  });
  
  return Object.values(families);
};

// دالة للتحقق من صحة ملف الخط
export const validateFontFile = (file: File): { valid: boolean; error?: string } => {
  const validExtensions = ['woff2', 'woff', 'ttf', 'otf', 'eot'];
  const maxSize = 5 * 1024 * 1024; // 5MB
  
  const extension = file.name.split('.').pop()?.toLowerCase();
  
  if (!extension || !validExtensions.includes(extension)) {
    return {
      valid: false,
      error: 'نوع الملف غير مدعوم. الأنواع المدعومة: ' + validExtensions.join(', ')
    };
  }
  
  if (file.size > maxSize) {
    return {
      valid: false,
      error: 'حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت'
    };
  }
  
  return { valid: true };
};

// دالة لإنشاء معاينة للخط
export const generateFontPreview = (fontFamily: string, text: string = 'مرحباً بكم'): string => {
  return `
    <div style="
      font-family: '${fontFamily}', sans-serif;
      font-size: 1.2rem;
      padding: 12px;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      background-color: #f9fafb;
      margin-top: 8px;
    ">
      ${text}
    </div>
  `;
};

// إعدادات الخطوط الافتراضية
export const defaultFontSettings = {
  headings: {
    primary: 'Cairo',
    secondary: 'Tajawal', 
    tertiary: 'Almarai'
  },
  body: {
    main: 'Cairo',
    small: 'Almarai',
    caption: 'Inter'
  },
  special: {
    logo: 'Cairo',
    numbers: 'Roboto',
    buttons: 'Cairo'
  }
};

// دالة لتطبيق إعدادات الخطوط على CSS
export const applyFontSettings = (settings: typeof defaultFontSettings): string => {
  return `
    :root {
      --font-heading-primary: '${settings.headings.primary}', sans-serif;
      --font-heading-secondary: '${settings.headings.secondary}', sans-serif;
      --font-heading-tertiary: '${settings.headings.tertiary}', sans-serif;
      --font-body-main: '${settings.body.main}', sans-serif;
      --font-body-small: '${settings.body.small}', sans-serif;
      --font-body-caption: '${settings.body.caption}', sans-serif;
      --font-special-logo: '${settings.special.logo}', sans-serif;
      --font-special-numbers: '${settings.special.numbers}', monospace;
      --font-special-buttons: '${settings.special.buttons}', sans-serif;
    }
    
    h1, .heading-primary { font-family: var(--font-heading-primary); }
    h2, .heading-secondary { font-family: var(--font-heading-secondary); }
    h3, h4, h5, h6, .heading-tertiary { font-family: var(--font-heading-tertiary); }
    p, .body-main { font-family: var(--font-body-main); }
    small, .body-small { font-family: var(--font-body-small); }
    .caption { font-family: var(--font-body-caption); }
    .logo { font-family: var(--font-special-logo); }
    .numbers { font-family: var(--font-special-numbers); }
    button, .button { font-family: var(--font-special-buttons); }
  `;
};
