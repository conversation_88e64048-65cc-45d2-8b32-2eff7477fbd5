<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار السلايدر</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f0f0f0;
        }
        
        .slider-container {
            position: relative;
            width: 100%;
            height: 400px;
            overflow: hidden;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            background: #333;
        }
        
        .slide {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            transition: opacity 0.5s ease-in-out;
        }
        
        .slide.active {
            opacity: 1;
        }
        
        .slide img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .slide-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: white;
            background: rgba(0,0,0,0.5);
            padding: 20px;
            border-radius: 10px;
        }
        
        .controls {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 10px;
        }
        
        .dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(255,255,255,0.5);
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .dot.active {
            background: white;
        }
        
        .nav-btn {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(0,0,0,0.5);
            color: white;
            border: none;
            padding: 15px 20px;
            cursor: pointer;
            font-size: 18px;
            border-radius: 5px;
        }
        
        .prev {
            left: 20px;
        }
        
        .next {
            right: 20px;
        }
        
        .debug {
            margin-top: 20px;
            padding: 15px;
            background: white;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <h1>اختبار السلايدر - تشخيص المشكلة</h1>
    
    <div class="slider-container" id="slider">
        <div class="slide active">
            <img src="/images/products/slider/55555.jpg" alt="صورة 1" onload="logImageLoad(1)" onerror="logImageError(1)">
            <div class="slide-content">
                <h2>مرحباً بكم في متجرنا</h2>
                <p>اكتشف أفضل المنتجات بأسعار تنافسية</p>
            </div>
        </div>
        
        <div class="slide">
            <img src="/images/products/slider/6666.jpg" alt="صورة 2" onload="logImageLoad(2)" onerror="logImageError(2)">
            <div class="slide-content">
                <h2>عروض خاصة</h2>
                <p>خصومات تصل إلى 50% على منتجات مختارة</p>
            </div>
        </div>
        
        <div class="slide">
            <img src="/images/products/slider/777.jpg" alt="صورة 3" onload="logImageLoad(3)" onerror="logImageError(3)">
            <div class="slide-content">
                <h2>شحن مجاني</h2>
                <p>شحن مجاني للطلبات أكثر من 200 ريال</p>
            </div>
        </div>
        
        <button class="nav-btn prev" onclick="prevSlide()">‹</button>
        <button class="nav-btn next" onclick="nextSlide()">›</button>
        
        <div class="controls">
            <div class="dot active" onclick="goToSlide(0)"></div>
            <div class="dot" onclick="goToSlide(1)"></div>
            <div class="dot" onclick="goToSlide(2)"></div>
        </div>
    </div>
    
    <div class="debug">
        <h3>معلومات التشخيص:</h3>
        <div id="debug-info">
            <p>الصورة الحالية: <span id="current-slide">1</span></p>
            <p>حالة تحميل الصور:</p>
            <ul id="image-status"></ul>
        </div>
    </div>

    <script>
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');
        const dots = document.querySelectorAll('.dot');
        const totalSlides = slides.length;
        
        function updateSlider() {
            slides.forEach((slide, index) => {
                slide.classList.toggle('active', index === currentSlide);
            });
            
            dots.forEach((dot, index) => {
                dot.classList.toggle('active', index === currentSlide);
            });
            
            document.getElementById('current-slide').textContent = currentSlide + 1;
            console.log(`🔄 تم التبديل للصورة رقم: ${currentSlide + 1}`);
        }
        
        function nextSlide() {
            currentSlide = (currentSlide + 1) % totalSlides;
            updateSlider();
        }
        
        function prevSlide() {
            currentSlide = (currentSlide - 1 + totalSlides) % totalSlides;
            updateSlider();
        }
        
        function goToSlide(index) {
            currentSlide = index;
            updateSlider();
        }
        
        function logImageLoad(imageNum) {
            console.log(`✅ تم تحميل الصورة ${imageNum} بنجاح`);
            updateImageStatus(imageNum, 'تم التحميل ✅');
        }
        
        function logImageError(imageNum) {
            console.error(`❌ فشل تحميل الصورة ${imageNum}`);
            updateImageStatus(imageNum, 'فشل التحميل ❌');
        }
        
        function updateImageStatus(imageNum, status) {
            const statusList = document.getElementById('image-status');
            const existingItem = document.getElementById(`status-${imageNum}`);
            
            if (existingItem) {
                existingItem.textContent = `الصورة ${imageNum}: ${status}`;
            } else {
                const listItem = document.createElement('li');
                listItem.id = `status-${imageNum}`;
                listItem.textContent = `الصورة ${imageNum}: ${status}`;
                statusList.appendChild(listItem);
            }
        }
        
        // تشغيل تلقائي
        setInterval(nextSlide, 3000);
        
        console.log('🚀 تم تحميل اختبار السلايدر');
        console.log(`📊 عدد الصور: ${totalSlides}`);
    </script>
</body>
</html>
