'use client';

import React from 'react';
import Link from 'next/link';
import { useAdmin } from '@/contexts/AdminContext';
import { Category } from '@/types';

interface CategoryGridProps {
  title?: string;
  showAll?: boolean;
  maxItems?: number;
  className?: string;
}

const CategoryGrid: React.FC<CategoryGridProps> = ({ 
  title = "تصفح حسب الفئة",
  showAll = false,
  maxItems = 8,
  className = ""
}) => {
  const { categories } = useAdmin();
  
  const displayCategories = showAll ? categories : categories.slice(0, maxItems);

  if (categories.length === 0) {
    return null;
  }

  return (
    <section className={`py-12 ${className}`}>
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center mb-8">
          <h2 className="text-3xl font-bold text-gray-800">{title}</h2>
          {!showAll && categories.length > maxItems && (
            <Link 
              href="/categories" 
              className="text-primary-600 hover:text-primary-700 font-medium"
            >
              عرض الكل
            </Link>
          )}
        </div>

        <div className="flex justify-center items-center">
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-8 max-w-6xl mx-auto justify-items-center">
            {displayCategories.map((category) => (
              <CategoryCard key={category.id} category={category} />
            ))}
          </div>
        </div>

        {showAll && (
          <div className="mt-8 text-center">
            <p className="text-gray-600">
              إجمالي الفئات: {categories.length}
            </p>
          </div>
        )}
      </div>
    </section>
  );
};

interface CategoryCardProps {
  category: Category;
}

const CategoryCard: React.FC<CategoryCardProps> = ({ category }) => {
  const subcategoriesCount = category.subcategories?.length || 0;

  return (
    <Link
      href={`/category/${category.id}`}
      className="group block"
    >
      <div className="relative bg-gradient-to-br from-white via-gray-50 to-gray-100 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 p-6 text-center border border-gray-200/50 hover:border-primary-300/50 transform hover:-translate-y-2 hover:scale-105 backdrop-blur-sm">
        {/* Windows 11 Style Background Glow */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary-100/20 via-transparent to-accent-100/20 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

        {/* Icon Container with Windows 11 Style */}
        <div className="relative z-10 mb-4">
          <div className="w-16 h-16 mx-auto bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl shadow-lg flex items-center justify-center group-hover:shadow-xl transition-all duration-500 group-hover:rotate-3 group-hover:scale-110">
            {/* Icon Background Glow */}
            <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-2xl"></div>

            {/* Icon */}
            <div className="relative text-3xl text-white drop-shadow-lg">
              {category.icon || '📁'}
            </div>

            {/* Shine Effect */}
            <div className="absolute top-1 left-1 w-6 h-6 bg-white/30 rounded-full blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
          </div>
        </div>

        {/* Category Name */}
        <h3 className="relative z-10 font-semibold text-gray-800 mb-2 text-sm leading-tight group-hover:text-primary-700 transition-colors duration-300">
          {category.name}
        </h3>

        {/* Subcategories Count */}
        {subcategoriesCount > 0 && (
          <div className="relative z-10 inline-flex items-center px-2 py-1 bg-primary-100 text-primary-700 rounded-full text-xs font-medium">
            <span className="w-1.5 h-1.5 bg-primary-500 rounded-full mr-1"></span>
            {subcategoriesCount} قسم
          </div>
        )}

        {/* Description (on hover) */}
        {category.description && (
          <div className="relative z-10 opacity-0 group-hover:opacity-100 transition-all duration-500 mt-3 transform translate-y-2 group-hover:translate-y-0">
            <p className="text-xs text-gray-600 line-clamp-2 bg-white/80 backdrop-blur-sm rounded-lg p-2 border border-gray-200/50">
              {category.description}
            </p>
          </div>
        )}

        {/* Floating Particles Effect */}
        <div className="absolute top-2 right-2 w-2 h-2 bg-primary-400 rounded-full opacity-0 group-hover:opacity-60 transition-all duration-700 group-hover:animate-pulse"></div>
        <div className="absolute bottom-3 left-3 w-1.5 h-1.5 bg-accent-400 rounded-full opacity-0 group-hover:opacity-40 transition-all duration-1000 group-hover:animate-bounce"></div>

        {/* Border Glow Effect */}
        <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-primary-500/20 via-transparent to-accent-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-sm"></div>
      </div>
    </Link>
  );
};

export default CategoryGrid;
