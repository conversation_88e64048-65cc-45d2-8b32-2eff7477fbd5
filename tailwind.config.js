/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: 'var(--color-primary-50, #eff6ff)',
          100: 'var(--color-primary-100, #dbeafe)',
          200: 'var(--color-primary-200, #bfdbfe)',
          300: 'var(--color-primary-300, #93c5fd)',
          400: 'var(--color-primary-400, #60a5fa)',
          500: 'var(--color-primary-500, #3b82f6)',
          600: 'var(--color-primary-600, #2563eb)',
          700: 'var(--color-primary-700, #1d4ed8)',
          800: 'var(--color-primary-800, #1e40af)',
          900: 'var(--color-primary-900, #1e3a8a)',
        },
        secondary: {
          50: 'var(--color-secondary-50, #f8fafc)',
          100: 'var(--color-secondary-100, #f1f5f9)',
          200: 'var(--color-secondary-200, #e2e8f0)',
          300: 'var(--color-secondary-300, #cbd5e1)',
          400: 'var(--color-secondary-400, #94a3b8)',
          500: 'var(--color-secondary-500, #64748b)',
          600: 'var(--color-secondary-600, #475569)',
          700: 'var(--color-secondary-700, #334155)',
          800: 'var(--color-secondary-800, #1e293b)',
          900: 'var(--color-secondary-900, #0f172a)',
        },
        accent: {
          50: 'var(--color-accent-50, #fef7ff)',
          100: 'var(--color-accent-100, #fce7ff)',
          200: 'var(--color-accent-200, #f8d4fe)',
          300: 'var(--color-accent-300, #f2b2fc)',
          400: 'var(--color-accent-400, #e879f9)',
          500: 'var(--color-accent-500, #d946ef)',
          600: 'var(--color-accent-600, #c026d3)',
          700: 'var(--color-accent-700, #a21caf)',
          800: 'var(--color-accent-800, #86198f)',
          900: 'var(--color-accent-900, #701a75)',
        }
      },
      fontFamily: {
        // الخطوط الافتراضية
        arabic: ['Noto Sans Arabic', 'Arial', 'sans-serif'],
        english: ['Inter', 'system-ui', 'sans-serif'],

        // الخطوط العربية المخصصة
        'amiri': ['Amiri', 'serif'],
        'cairo': ['Cairo', 'sans-serif'],
        'tajawal': ['Tajawal', 'sans-serif'],
        'almarai': ['Almarai', 'sans-serif'],
        'custom-arabic': ['CustomArabicFont', 'Cairo', 'sans-serif'],

        // الخطوط الإنجليزية المخصصة
        'inter': ['Inter', 'sans-serif'],
        'roboto': ['Roboto', 'sans-serif'],
        'poppins': ['Poppins', 'sans-serif'],
        'custom-english': ['CustomEnglishFont', 'Inter', 'sans-serif'],

        // خطوط للعناوين
        'main-heading': ['Cairo', 'Amiri', 'serif'],
        'sub-heading': ['Tajawal', 'Cairo', 'sans-serif'],
        'custom-heading': ['CustomHeadingFont', 'Cairo', 'sans-serif'],

        // خطوط للنصوص
        'main-text': ['Cairo', 'sans-serif'],
        'small-text': ['Almarai', 'Cairo', 'sans-serif'],
        'custom-body': ['CustomBodyFont', 'Cairo', 'sans-serif'],

        // خطوط خاصة
        'logo': ['CustomLogoFont', 'Cairo', 'sans-serif'],
        'numbers': ['Roboto', 'monospace'],
        'code': ['Fira Code', 'Consolas', 'monospace'],
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'bounce-gentle': 'bounceGentle 2s infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        bounceGentle: {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-5px)' },
        },
      },
    },
  },
  plugins: [],
}
