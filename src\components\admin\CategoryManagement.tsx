'use client';

import React, { useState } from 'react';
import { useAdmin } from '@/contexts/AdminContext';
import { useToast } from '@/components/ToastContainer';
import { Category, Subcategory } from '@/types';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Save, 
  X, 
  FolderPlus,
  Folder,
  ChevronDown,
  ChevronRight
} from 'lucide-react';

const CategoryManagement: React.FC = () => {
  const { categories, addCategory, updateCategory, deleteCategory } = useAdmin();
  const { showSuccess, showError } = useToast();
  
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingCategory, setEditingCategory] = useState<string | null>(null);
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set());
  const [showSubcategoryForm, setShowSubcategoryForm] = useState<string | null>(null);
  
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    icon: '📁'
  });

  const [subcategoryFormData, setSubcategoryFormData] = useState({
    name: '',
    description: ''
  });

  const resetForm = () => {
    setFormData({ name: '', description: '', icon: '📁' });
    setEditingCategory(null);
    setShowAddForm(false);
  };

  const resetSubcategoryForm = () => {
    setSubcategoryFormData({ name: '', description: '' });
    setShowSubcategoryForm(null);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      showError('خطأ', 'اسم الفئة مطلوب');
      return;
    }

    try {
      if (editingCategory) {
        updateCategory(editingCategory, formData);
        showSuccess('تم التحديث!', 'تم تحديث الفئة بنجاح');
      } else {
        addCategory(formData);
        showSuccess('تمت الإضافة!', 'تم إضافة الفئة بنجاح');
      }
      resetForm();
    } catch (error) {
      showError('خطأ', 'حدث خطأ أثناء حفظ الفئة');
    }
  };

  const handleEdit = (category: Category) => {
    setFormData({
      name: category.name,
      description: category.description || '',
      icon: category.icon || '📁'
    });
    setEditingCategory(category.id);
    setShowAddForm(true);
  };

  const handleDelete = (categoryId: string) => {
    if (confirm('هل أنت متأكد من حذف هذه الفئة؟ سيتم حذف جميع الفئات الفرعية أيضاً.')) {
      try {
        deleteCategory(categoryId);
        showSuccess('تم الحذف!', 'تم حذف الفئة بنجاح');
      } catch (error) {
        showError('خطأ', 'حدث خطأ أثناء حذف الفئة');
      }
    }
  };

  const handleAddSubcategory = (categoryId: string) => {
    console.log('🔄 بدء إضافة فئة فرعية:', { categoryId, formData: subcategoryFormData });

    if (!subcategoryFormData.name.trim()) {
      showError('خطأ', 'اسم الفئة الفرعية مطلوب');
      return;
    }

    try {
      const category = categories.find(c => c.id === categoryId);
      console.log('📁 الفئة الموجودة:', category);

      if (!category) {
        showError('خطأ', 'لم يتم العثور على الفئة الأساسية');
        return;
      }

      const newSubcategory: Subcategory = {
        id: `sub_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        name: subcategoryFormData.name.trim(),
        description: subcategoryFormData.description.trim(),
        categoryId: categoryId
      };

      console.log('✨ الفئة الفرعية الجديدة:', newSubcategory);

      const currentSubcategories = category.subcategories || [];
      const updatedSubcategories = [...currentSubcategories, newSubcategory];

      console.log('📊 الفئات الفرعية المحدثة:', updatedSubcategories);

      updateCategory(categoryId, { subcategories: updatedSubcategories });

      showSuccess('تمت الإضافة!', `تم إضافة الفئة الفرعية "${newSubcategory.name}" بنجاح`);
      resetSubcategoryForm();

      // التأكد من توسيع الفئة لإظهار الفئة الفرعية الجديدة
      setExpandedCategories(prev => {
        const newSet = new Set(prev);
        newSet.add(categoryId);
        return newSet;
      });

      console.log('✅ تم إضافة الفئة الفرعية بنجاح');
    } catch (error) {
      console.error('❌ خطأ في إضافة الفئة الفرعية:', error);
      showError('خطأ', 'حدث خطأ أثناء إضافة الفئة الفرعية');
    }
  };

  const handleDeleteSubcategory = (categoryId: string, subcategoryId: string) => {
    const category = categories.find(c => c.id === categoryId);
    const subcategory = category?.subcategories?.find(sub => sub.id === subcategoryId);

    if (!subcategory) {
      showError('خطأ', 'لم يتم العثور على الفئة الفرعية');
      return;
    }

    if (confirm(`هل أنت متأكد من حذف الفئة الفرعية "${subcategory.name}"؟`)) {
      try {
        console.log('🗑️ حذف فئة فرعية:', { categoryId, subcategoryId, subcategoryName: subcategory.name });

        if (category) {
          const updatedSubcategories = category.subcategories?.filter(sub => sub.id !== subcategoryId) || [];
          console.log('📊 الفئات الفرعية بعد الحذف:', updatedSubcategories);

          updateCategory(categoryId, { subcategories: updatedSubcategories });
          showSuccess('تم الحذف!', `تم حذف الفئة الفرعية "${subcategory.name}" بنجاح`);

          console.log('✅ تم حذف الفئة الفرعية بنجاح');
        }
      } catch (error) {
        console.error('❌ خطأ في حذف الفئة الفرعية:', error);
        showError('خطأ', 'حدث خطأ أثناء حذف الفئة الفرعية');
      }
    }
  };

  const toggleCategory = (categoryId: string) => {
    const newExpanded = new Set(expandedCategories);
    if (newExpanded.has(categoryId)) {
      newExpanded.delete(categoryId);
    } else {
      newExpanded.add(categoryId);
    }
    setExpandedCategories(newExpanded);
  };

  const commonIcons = ['📁', '📱', '👕', '🏠', '📚', '⚽', '🚗', '🎮', '💄', '🍔', '🎵', '🎨', '🔧', '💊', '🌱'];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-semibold">إدارة الفئات والتصنيفات</h2>
        <button
          onClick={() => setShowAddForm(true)}
          className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors flex items-center"
        >
          <Plus className="w-5 h-5 ml-2" />
          إضافة فئة جديدة
        </button>
      </div>

      {/* Add/Edit Category Form */}
      {showAddForm && (
        <div className="bg-white rounded-lg shadow-md p-6 border-l-4 border-primary-500">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold">
              {editingCategory ? 'تعديل الفئة' : 'إضافة فئة جديدة'}
            </h3>
            <button
              onClick={resetForm}
              className="text-gray-500 hover:text-gray-700"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  اسم الفئة *
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  className="input-field"
                  placeholder="أدخل اسم الفئة"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  الأيقونة
                </label>
                <div className="flex items-center space-x-2 space-x-reverse">
                  <input
                    type="text"
                    value={formData.icon}
                    onChange={(e) => setFormData(prev => ({ ...prev, icon: e.target.value }))}
                    className="input-field flex-1"
                    placeholder="🔗"
                  />
                  <div className="flex space-x-1 space-x-reverse">
                    {commonIcons.slice(0, 5).map(icon => (
                      <button
                        key={icon}
                        type="button"
                        onClick={() => setFormData(prev => ({ ...prev, icon }))}
                        className="p-2 border rounded hover:bg-gray-50 text-lg"
                      >
                        {icon}
                      </button>
                    ))}
                  </div>
                </div>
                <div className="mt-2 flex flex-wrap gap-1">
                  {commonIcons.slice(5).map(icon => (
                    <button
                      key={icon}
                      type="button"
                      onClick={() => setFormData(prev => ({ ...prev, icon }))}
                      className="p-1 border rounded hover:bg-gray-50 text-sm"
                    >
                      {icon}
                    </button>
                  ))}
                </div>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                وصف الفئة
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                rows={3}
                className="input-field"
                placeholder="أدخل وصف للفئة (اختياري)"
              />
            </div>

            <div className="flex justify-end space-x-4 space-x-reverse">
              <button
                type="button"
                onClick={resetForm}
                className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
              >
                إلغاء
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors flex items-center"
              >
                <Save className="w-4 h-4 ml-2" />
                {editingCategory ? 'تحديث' : 'إضافة'}
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Categories List */}
      <div className="bg-white rounded-lg shadow-md">
        <div className="p-6 border-b">
          <h3 className="text-lg font-semibold">الفئات الحالية ({categories.length})</h3>
        </div>

        <div className="divide-y">
          {categories.map((category) => (
            <div key={category.id} className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4 space-x-reverse">
                  <button
                    onClick={() => toggleCategory(category.id)}
                    className="text-gray-500 hover:text-gray-700"
                  >
                    {expandedCategories.has(category.id) ? (
                      <ChevronDown className="w-5 h-5" />
                    ) : (
                      <ChevronRight className="w-5 h-5" />
                    )}
                  </button>
                  
                  <div className="text-2xl">{category.icon}</div>
                  
                  <div>
                    <h4 className="font-semibold text-lg">{category.name}</h4>
                    {category.description && (
                      <p className="text-gray-600 text-sm">{category.description}</p>
                    )}
                    <p className="text-xs text-gray-500">
                      {category.subcategories?.length || 0} فئة فرعية
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-2 space-x-reverse">
                  <button
                    onClick={() => setShowSubcategoryForm(category.id)}
                    className="text-green-600 hover:text-green-800 p-2"
                    title="إضافة فئة فرعية"
                  >
                    <FolderPlus className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => handleEdit(category)}
                    className="text-blue-600 hover:text-blue-800 p-2"
                    title="تعديل"
                  >
                    <Edit className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => handleDelete(category.id)}
                    className="text-red-600 hover:text-red-800 p-2"
                    title="حذف"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>

              {/* Subcategories */}
              {expandedCategories.has(category.id) && (
                <div className="mt-4 mr-8 space-y-2">
                  {/* Add Subcategory Form */}
                  {showSubcategoryForm === category.id && (
                    <div className="bg-blue-50 p-4 rounded-lg border-2 border-blue-200 shadow-sm">
                      <div className="flex items-center gap-2 mb-3">
                        <FolderPlus className="w-5 h-5 text-blue-600" />
                        <h5 className="font-semibold text-blue-800">إضافة فئة فرعية جديدة لـ "{category.name}"</h5>
                      </div>

                      <div className="space-y-3">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            اسم الفئة الفرعية <span className="text-red-500">*</span>
                          </label>
                          <input
                            type="text"
                            value={subcategoryFormData.name}
                            onChange={(e) => setSubcategoryFormData(prev => ({ ...prev, name: e.target.value }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="مثال: كتب الأطفال، أدوات الكتابة، إلخ..."
                            required
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            وصف الفئة الفرعية
                          </label>
                          <textarea
                            value={subcategoryFormData.description}
                            onChange={(e) => setSubcategoryFormData(prev => ({ ...prev, description: e.target.value }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="وصف مختصر للفئة الفرعية (اختياري)"
                            rows={2}
                          />
                        </div>
                      </div>

                      <div className="flex justify-end space-x-3 space-x-reverse mt-4 pt-3 border-t border-blue-200">
                        <button
                          onClick={resetSubcategoryForm}
                          className="px-4 py-2 text-sm border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
                        >
                          <X className="w-4 h-4 inline ml-1" />
                          إلغاء
                        </button>
                        <button
                          onClick={() => handleAddSubcategory(category.id)}
                          disabled={!subcategoryFormData.name.trim()}
                          className="px-4 py-2 text-sm bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center gap-1"
                        >
                          <Plus className="w-4 h-4" />
                          إضافة الفئة الفرعية
                        </button>
                      </div>
                    </div>
                  )}

                  {/* Subcategories List */}
                  {category.subcategories && category.subcategories.length > 0 && (
                    <div className="space-y-2">
                      <h6 className="text-sm font-medium text-gray-700 flex items-center gap-1">
                        <Folder className="w-4 h-4" />
                        الفئات الفرعية ({category.subcategories.length})
                      </h6>
                      {category.subcategories.map((subcategory, index) => (
                        <div key={subcategory.id} className="flex items-center justify-between bg-white p-3 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
                          <div className="flex items-center space-x-3 space-x-reverse">
                            <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                              <span className="text-xs font-semibold text-blue-600">{index + 1}</span>
                            </div>
                            <div>
                              <span className="font-medium text-gray-800">{subcategory.name}</span>
                              {subcategory.description && (
                                <p className="text-sm text-gray-600 mt-1">{subcategory.description}</p>
                              )}
                              <p className="text-xs text-gray-400 mt-1">ID: {subcategory.id}</p>
                            </div>
                          </div>
                          <button
                            onClick={() => handleDeleteSubcategory(category.id, subcategory.id)}
                            className="text-red-600 hover:text-red-800 hover:bg-red-50 p-2 rounded-lg transition-colors"
                            title={`حذف الفئة الفرعية "${subcategory.name}"`}
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                  )}

                  {(!category.subcategories || category.subcategories.length === 0) && showSubcategoryForm !== category.id && (
                    <div className="text-center py-6 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
                      <Folder className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                      <p className="text-gray-500 text-sm">لا توجد فئات فرعية في هذه الفئة</p>
                      <p className="text-gray-400 text-xs mt-1">اضغط على زر "إضافة فئة فرعية" لإضافة فئة فرعية جديدة</p>
                    </div>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>

        {categories.length === 0 && (
          <div className="p-12 text-center text-gray-500">
            <Folder className="w-12 h-12 mx-auto mb-4 text-gray-300" />
            <p>لا توجد فئات بعد</p>
            <p className="text-sm">اضغط "إضافة فئة جديدة" لإنشاء أول فئة</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default CategoryManagement;
