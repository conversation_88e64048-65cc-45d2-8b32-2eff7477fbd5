import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('image') as File;
    
    if (!file) {
      return NextResponse.json({ error: 'لم يتم العثور على ملف' }, { status: 400 });
    }

    // التحقق من نوع الملف
    const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/svg+xml'];
    if (!validTypes.includes(file.type)) {
      return NextResponse.json({ 
        error: 'نوع الملف غير مدعوم. الأنواع المدعومة: JPG, PNG, WebP, SVG' 
      }, { status: 400 });
    }

    // التحقق من حجم الملف (2MB)
    const maxSize = 2 * 1024 * 1024;
    if (file.size > maxSize) {
      return NextResponse.json({ 
        error: 'حجم الملف كبير جداً. الحد الأقصى 2 ميجابايت' 
      }, { status: 400 });
    }

    // إنشاء مجلد الصور إذا لم يكن موجوداً
    const uploadsDir = path.join(process.cwd(), 'public', 'images', 'products', 'uploads');
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir, { recursive: true });
    }

    // إنشاء اسم ملف فريد
    const timestamp = Date.now();
    const fileExtension = path.extname(file.name);
    const fileName = `product-${timestamp}${fileExtension}`;
    const filePath = path.join(uploadsDir, fileName);

    // حفظ الملف
    const buffer = Buffer.from(await file.arrayBuffer());
    fs.writeFileSync(filePath, buffer);

    // إرجاع رابط الصورة
    const imageUrl = `/images/products/uploads/${fileName}`;
    
    return NextResponse.json({ 
      success: true, 
      url: imageUrl,
      message: 'تم رفع الصورة بنجاح'
    });

  } catch (error) {
    console.error('خطأ في رفع الصورة:', error);
    return NextResponse.json({ 
      error: 'حدث خطأ أثناء رفع الصورة' 
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({ 
    message: 'API لرفع الصور - استخدم POST لرفع الصور' 
  });
}
