Console.log("Console.log("Console.log("Console.log("Console.log("Console.log("Console.log("Console.log("Console.log("");");");");");");");");");export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  originalPrice?: number;
  image: string;
  images?: string[];
  category: string;
  subcategory?: string;
  inStock: boolean;
  quantity: number;
  featured?: boolean;
  rating?: number;
  reviews?: number;
  brand?: string;
  specifications?: Record<string, string>;
  tags?: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface CartItem {
  id: string;
  product: Product;
  quantity: number;
  selectedOptions?: Record<string, string>;
}

export interface Category {
  id: string;
  name: string;
  description?: string;
  image?: string;
  subcategories?: Subcategory[];
  icon?: string;
}

export interface Subcategory {
  id: string;
  name: string;
  description?: string;
  categoryId: string;
}

export interface Order {
  id: string;
  items: CartItem[];
  customerInfo: CustomerInfo;
  totalAmount: number;
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  paymentMethod: 'bank_transfer' | 'cash_on_delivery' | 'deposit';
  paymentStatus: 'pending' | 'paid' | 'failed';
  shippingAddress: string;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface CustomerInfo {
  name: string;
  phone: string;
  email?: string;
  address: string;
  city: string;
  notes?: string;
}

export interface PrintService {
  id: string;
  name: string;
  description: string;
  pricePerPage: number;
  options: PrintOption[];
}

export interface PrintOption {
  id: string;
  name: string;
  type: 'select' | 'number' | 'boolean';
  options?: string[];
  priceModifier?: number;
  required?: boolean;
}

export interface PrintOrder {
  id: string;
  serviceId: string;
  fileName: string;
  fileUrl: string;
  pages: number;
  copies: number;
  selectedOptions: Record<string, any>;
  totalPrice: number;
  customerInfo: CustomerInfo;
  status: 'pending' | 'processing' | 'ready' | 'completed';
  createdAt: Date;
  estimatedCompletion: Date;
}

export interface User {
  id: string;
  name: string;
  email?: string;
  phone: string;
  role: 'customer' | 'admin';
  orders: Order[];
  wishlist: string[];
  addresses: Address[];
  createdAt: Date;
}

export interface Permission {
  id: string;
  name: string;
  description: string;
  category: 'products' | 'orders' | 'users' | 'settings' | 'reports';
}

export interface AdminUser {
  id: string;
  username: string;
  email: string;
  name: string;
  password: string;
  role: 'super_admin' | 'admin' | 'manager' | 'editor';
  permissions: string[]; // Permission IDs
  isActive: boolean;
  lastLogin?: Date;
  createdAt: Date;
  createdBy: string; // Admin ID who created this user
}

export interface AdminCredentials {
  username: string;
  password: string;
  lastUpdated: Date;
  isDefault: boolean;
}

export interface SalesReport {
  date: string;
  totalSales: number;
  totalOrders: number;
  totalRevenue: number;
  topProducts: {
    productId: string;
    productName: string;
    quantity: number;
    revenue: number;
  }[];
  paymentMethods: {
    bankTransfer: number;
    cashOnDelivery: number;
  };
}

export interface Address {
  id: string;
  name: string;
  phone: string;
  address: string;
  city: string;
  isDefault: boolean;
}

export interface BankAccount {
  id: string;
  bankName: string;
  accountNumber: string;
  accountName: string;
  enabled: boolean;
  logo?: string;
  description?: string;
}

export interface AdminSettings {
  siteName: string;
  siteDescription: string;
  logo?: string;
  contactInfo: {
    phone: string;
    email: string;
    address: string;
    whatsapp?: string;
    socialMedia: {
      facebook?: string;
      twitter?: string;
      instagram?: string;
      telegram?: string;
      whatsapp?: string;
    };
  };
  workingHours?: {
    enabled: boolean;
    schedule: {
      saturday: { enabled: boolean; open: string; close: string; };
      sunday: { enabled: boolean; open: string; close: string; };
      monday: { enabled: boolean; open: string; close: string; };
      tuesday: { enabled: boolean; open: string; close: string; };
      wednesday: { enabled: boolean; open: string; close: string; };
      thursday: { enabled: boolean; open: string; close: string; };
      friday: { enabled: boolean; open: string; close: string; };
    };
    timezone: string;
    displayFormat: string;
    closedMessage: string;
    openMessage: string;
  };
  storeLocation?: {
    lat: string;
    lng: string;
    lastUpdated?: string;
  };
  topBarSettings?: {
    enabled: boolean;
    backgroundColor: string;
    textColor: string;
    fontSize: string;
    fontWeight: string;
    padding: string;
    showPhone: boolean;
    showEmail: boolean;
    showShipping: boolean;
    customShippingText?: string;
  };
  pagesSettings?: {
    customerService: {
      enabled: boolean;
      title: string;
      content: string;
      fontSize: string;
      fontWeight: string;
      textColor: string;
      backgroundColor: string;
    };
    faq: {
      enabled: boolean;
      title: string;
      content: string;
      fontSize: string;
      fontWeight: string;
      textColor: string;
      backgroundColor: string;
    };
    shippingPolicy: {
      enabled: boolean;
      title: string;
      content: string;
      fontSize: string;
      fontWeight: string;
      textColor: string;
      backgroundColor: string;
    };
    returnPolicy: {
      enabled: boolean;
      title: string;
      content: string;
      fontSize: string;
      fontWeight: string;
      textColor: string;
      backgroundColor: string;
    };
    privacyPolicy: {
      enabled: boolean;
      title: string;
      content: string;
      fontSize: string;
      fontWeight: string;
      textColor: string;
      backgroundColor: string;
    };
    termsConditions: {
      enabled: boolean;
      title: string;
      content: string;
      fontSize: string;
      fontWeight: string;
      textColor: string;
      backgroundColor: string;
    };
  };

  paymentMethods: {
    bankTransfer: {
      enabled: boolean;
      banks: BankAccount[];
    };
    cashOnDelivery: {
      enabled: boolean;
    };
  };
  shippingSettings: {
    freeShippingThreshold: number;
    shippingCost: number;
    deliveryAreas: string[];
  };
  theme: {
    primaryColor: string;
    secondaryColor: string;
    accentColor: string;
  };
  designSettings: {
    typography: {
      headings: {
        fontFamily: string;
        fontSize: string;
        fontWeight: string;
        lineHeight: string;
        letterSpacing: string;
      };
      subheadings: {
        fontFamily: string;
        fontSize: string;
        fontWeight: string;
        lineHeight: string;
        letterSpacing: string;
      };
      paragraphs: {
        fontFamily: string;
        fontSize: string;
        fontWeight: string;
        lineHeight: string;
        letterSpacing: string;
      };
      notes: {
        fontFamily: string;
        fontSize: string;
        fontWeight: string;
        lineHeight: string;
        letterSpacing: string;
      };
    };
    slider: {
      enabled: boolean;
      height: string;
      autoplay: boolean;
      autoplaySpeed: number;
      showDots: boolean;
      showArrows: boolean;
      pauseOnHover: boolean;
      transition: string;
      images: Array<{
        id: string;
        url: string;
        title: string;
        description: string;
        link?: string;
      }>;
    };
    layout: {
      borderRadius: string;
      spacing: string;
      containerMaxWidth: string;
      cardShadow: string;
      borderWidth: string;
    };
    colors: {
      primary: string;
      secondary: string;
      accent: string;
      success: string;
      warning: string;
      error: string;
      info: string;
      background: string;
      surface: string;
      text: {
        primary: string;
        secondary: string;
        muted: string;
        inverse: string;
      };
      border: string;
    };
    animations: {
      duration: string;
      easing: string;
      hoverScale: string;
    };
  };
}
