'use client';

import React, { useState, useEffect } from 'react';
import { useAdmin } from '@/contexts/AdminContext';
import { useToast } from '@/components/ToastContainer';
import { Product } from '@/types';
import { X, Save } from 'lucide-react';
import ImageUploader from './ImageUploader';

interface AddProductFormProps {
  isOpen: boolean;
  onClose: () => void;
  editingProduct?: Product | null;
}

const AddProductForm: React.FC<AddProductFormProps> = ({ isOpen, onClose, editingProduct }) => {
  const { addProduct, updateProduct, categories } = useAdmin();
  const { showSuccess, showError } = useToast();
  
  const [formData, setFormData] = useState({
    name: editingProduct?.name || '',
    description: editingProduct?.description || '',
    price: editingProduct?.price || 0,
    originalPrice: editingProduct?.originalPrice || 0,
    image: editingProduct?.image || '/images/products/placeholder-default.svg',
    category: editingProduct?.category || '',
    subcategory: editingProduct?.subcategory || '',
    quantity: editingProduct?.quantity || 0,
    brand: editingProduct?.brand || '',
    inStock: editingProduct?.inStock ?? true,
    featured: editingProduct?.featured || false,
    tags: editingProduct?.tags?.join(', ') || '',
    specifications: editingProduct?.specifications || {}
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // تحديث البيانات عندما يتغير editingProduct
  useEffect(() => {
    if (editingProduct) {
      setFormData({
        name: editingProduct.name || '',
        description: editingProduct.description || '',
        price: editingProduct.price || 0,
        originalPrice: editingProduct.originalPrice || 0,
        image: editingProduct.image || '/images/products/placeholder-default.svg',
        category: editingProduct.category || '',
        subcategory: editingProduct.subcategory || '',
        quantity: editingProduct.quantity || 0,
        brand: editingProduct.brand || '',
        inStock: editingProduct.inStock ?? true,
        featured: editingProduct.featured || false,
        tags: editingProduct.tags?.join(', ') || '',
        specifications: editingProduct.specifications || {}
      });
    } else {
      // إعادة تعيين النموذج للإضافة الجديدة
      setFormData({
        name: '',
        description: '',
        price: 0,
        originalPrice: 0,
        image: '/images/products/placeholder-default.svg',
        category: '',
        subcategory: '',
        quantity: 0,
        brand: '',
        inStock: true,
        featured: false,
        tags: '',
        specifications: {}
      });
    }
    setErrors({});
  }, [editingProduct]);

  const selectedCategory = categories.find(cat => cat.id === formData.category);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) newErrors.name = 'اسم المنتج مطلوب';
    if (!formData.description.trim()) newErrors.description = 'وصف المنتج مطلوب';
    if (formData.price <= 0) newErrors.price = 'السعر يجب أن يكون أكبر من صفر';
    if (!formData.category) newErrors.category = 'الفئة مطلوبة';
    if (formData.quantity < 0) newErrors.quantity = 'الكمية لا يمكن أن تكون سالبة';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsSubmitting(true);

    try {
      const productData = {
        ...formData,
        tags: formData.tags ? formData.tags.split(',').map(tag => tag.trim()).filter(Boolean) : [],
        originalPrice: formData.originalPrice || undefined,
      };

      if (editingProduct) {
        updateProduct(editingProduct.id, productData);
        showSuccess('تم التحديث!', 'تم تحديث المنتج بنجاح');
      } else {
        addProduct(productData);
        showSuccess('تمت الإضافة!', 'تم إضافة المنتج بنجاح');
      }

      onClose();
      // Reset form
      setFormData({
        name: '',
        description: '',
        price: 0,
        originalPrice: 0,
        image: '/images/products/placeholder-default.svg',
        category: '',
        subcategory: '',
        quantity: 0,
        brand: '',
        inStock: true,
        featured: false,
        tags: '',
        specifications: {}
      });
    } catch (error) {
      console.error('Error saving product:', error);
      showError('خطأ', 'حدث خطأ أثناء حفظ المنتج');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b">
          <h2 className="text-2xl font-bold text-gray-800">
            {editingProduct ? 'تعديل المنتج' : 'إضافة منتج جديد'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Product Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                اسم المنتج *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className={`input-field ${errors.name ? 'border-red-500' : ''}`}
                placeholder="أدخل اسم المنتج"
              />
              {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name}</p>}
            </div>

            {/* Brand */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                العلامة التجارية
              </label>
              <input
                type="text"
                value={formData.brand}
                onChange={(e) => handleInputChange('brand', e.target.value)}
                className="input-field"
                placeholder="أدخل العلامة التجارية"
              />
            </div>
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              وصف المنتج *
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              rows={4}
              className={`input-field ${errors.description ? 'border-red-500' : ''}`}
              placeholder="أدخل وصف مفصل للمنتج"
            />
            {errors.description && <p className="text-red-500 text-sm mt-1">{errors.description}</p>}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Price */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                السعر (ريال) *
              </label>
              <input
                type="number"
                value={formData.price}
                onChange={(e) => handleInputChange('price', Number(e.target.value))}
                className={`input-field ${errors.price ? 'border-red-500' : ''}`}
                placeholder="0"
                min="0"
                step="0.01"
              />
              {errors.price && <p className="text-red-500 text-sm mt-1">{errors.price}</p>}
            </div>

            {/* Original Price */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                السعر الأصلي (اختياري)
              </label>
              <input
                type="number"
                value={formData.originalPrice}
                onChange={(e) => handleInputChange('originalPrice', Number(e.target.value))}
                className="input-field"
                placeholder="0"
                min="0"
                step="0.01"
              />
            </div>

            {/* Quantity */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                الكمية المتوفرة *
              </label>
              <input
                type="number"
                value={formData.quantity}
                onChange={(e) => handleInputChange('quantity', Number(e.target.value))}
                className={`input-field ${errors.quantity ? 'border-red-500' : ''}`}
                placeholder="0"
                min="0"
              />
              {errors.quantity && <p className="text-red-500 text-sm mt-1">{errors.quantity}</p>}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Category */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                الفئة *
              </label>
              <select
                value={formData.category}
                onChange={(e) => {
                  handleInputChange('category', e.target.value);
                  handleInputChange('subcategory', ''); // Reset subcategory
                }}
                className={`input-field ${errors.category ? 'border-red-500' : ''}`}
              >
                <option value="">اختر الفئة</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
              {errors.category && <p className="text-red-500 text-sm mt-1">{errors.category}</p>}
            </div>

            {/* Subcategory */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                الفئة الفرعية
              </label>
              <select
                value={formData.subcategory}
                onChange={(e) => handleInputChange('subcategory', e.target.value)}
                className="input-field"
                disabled={!selectedCategory?.subcategories?.length}
              >
                <option value="">اختر الفئة الفرعية</option>
                {selectedCategory?.subcategories?.map((subcategory) => (
                  <option key={subcategory.id} value={subcategory.id}>
                    {subcategory.name}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Image Upload */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              صورة المنتج
            </label>
            <ImageUploader
              value={formData.image}
              onChange={(url) => handleInputChange('image', url)}
            />
          </div>

          {/* Tags */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              الكلمات المفتاحية
            </label>
            <input
              type="text"
              value={formData.tags}
              onChange={(e) => handleInputChange('tags', e.target.value)}
              className="input-field"
              placeholder="كلمة1, كلمة2, كلمة3"
            />
            <p className="text-sm text-gray-500 mt-1">
              افصل الكلمات بفاصلة
            </p>
          </div>

          {/* Checkboxes */}
          <div className="flex space-x-6 space-x-reverse">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.inStock}
                onChange={(e) => handleInputChange('inStock', e.target.checked)}
                className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
              />
              <span className="mr-2 text-sm text-gray-700">متوفر في المخزن</span>
            </label>

            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.featured}
                onChange={(e) => handleInputChange('featured', e.target.checked)}
                className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
              />
              <span className="mr-2 text-sm text-gray-700">منتج مميز</span>
            </label>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-4 space-x-reverse pt-6 border-t">
            <button
              type="button"
              onClick={onClose}
              className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
            >
              إلغاء
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="px-6 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors disabled:opacity-50 flex items-center"
            >
              <Save className="w-5 h-5 ml-2" />
              {isSubmitting ? 'جاري الحفظ...' : (editingProduct ? 'تحديث' : 'إضافة')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddProductForm;
