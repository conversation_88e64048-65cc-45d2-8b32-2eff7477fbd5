<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد أوقات العمل - مكتبة أنوار دارس</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 800px;
            width: 100%;
            text-align: center;
        }
        
        h1 {
            color: #333;
            margin-bottom: 20px;
            font-size: 2.5rem;
        }
        
        .button {
            background: linear-gradient(45deg, #4fac<PERSON>, #00f2fe);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 1.1rem;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
            min-width: 200px;
        }
        
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        
        .button.success {
            background: linear-gradient(45deg, #00b894, #00cec9);
        }
        
        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 10px;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .current-status {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: right;
        }
        
        .schedule-preview {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: right;
        }
        
        .day-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .day-item:last-child {
            border-bottom: none;
        }
        
        .day-name {
            font-weight: bold;
            color: #333;
        }
        
        .day-time {
            color: #666;
        }
        
        .day-closed {
            color: #f44336;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🕒 إعداد أوقات العمل</h1>
        
        <div class="description">
            <p>هذه الأداة ستقوم بإعداد أوقات العمل لمكتبة أنوار دارس</p>
            <p>وتفعيل عرضها في صفحة "اتصل بنا"</p>
        </div>
        
        <div class="current-status">
            <h3>📋 الحالة الحالية:</h3>
            <div id="currentStatus">جاري التحقق...</div>
        </div>
        
        <div class="schedule-preview">
            <h3>📅 جدول أوقات العمل المقترح:</h3>
            <div class="day-item">
                <span class="day-name">السبت</span>
                <span class="day-time">08:00 ص - 08:00 م</span>
            </div>
            <div class="day-item">
                <span class="day-name">الأحد</span>
                <span class="day-time">08:00 ص - 08:00 م</span>
            </div>
            <div class="day-item">
                <span class="day-name">الاثنين</span>
                <span class="day-time">08:00 ص - 08:00 م</span>
            </div>
            <div class="day-item">
                <span class="day-name">الثلاثاء</span>
                <span class="day-time">08:00 ص - 08:00 م</span>
            </div>
            <div class="day-item">
                <span class="day-name">الأربعاء</span>
                <span class="day-time">08:00 ص - 08:00 م</span>
            </div>
            <div class="day-item">
                <span class="day-name">الخميس</span>
                <span class="day-time">08:00 ص - 08:00 م</span>
            </div>
            <div class="day-item">
                <span class="day-name">الجمعة</span>
                <span class="day-closed">مغلق</span>
            </div>
        </div>
        
        <div class="buttons">
            <button class="button" onclick="checkCurrentStatus()">
                🔍 فحص الحالة الحالية
            </button>
            
            <button class="button success" onclick="setupWorkingHours()">
                ⚙️ إعداد أوقات العمل
            </button>
            
            <button class="button" onclick="viewContactPage()">
                👁️ عرض صفحة اتصل بنا
            </button>
        </div>
        
        <div id="status" class="status" style="display: none;"></div>
    </div>

    <script>
        function showStatus(message, type = 'success') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
            statusDiv.style.display = 'block';
        }
        
        function checkCurrentStatus() {
            console.log('🔍 فحص حالة أوقات العمل...');
            
            const settings = localStorage.getItem('admin-settings');
            const statusDiv = document.getElementById('currentStatus');
            
            if (settings) {
                try {
                    const parsedSettings = JSON.parse(settings);
                    const workingHours = parsedSettings.workingHours;
                    
                    let statusHtml = '<div style="text-align: right;">';
                    
                    if (workingHours) {
                        statusHtml += `<p><strong>حالة التفعيل:</strong> ${workingHours.enabled ? '✅ مفعل' : '❌ معطل'}</p>`;
                        statusHtml += `<p><strong>المنطقة الزمنية:</strong> ${workingHours.timezone || 'غير محدد'}</p>`;
                        statusHtml += `<p><strong>تنسيق الوقت:</strong> ${workingHours.displayFormat || 'غير محدد'}</p>`;
                        
                        if (workingHours.schedule) {
                            statusHtml += '<p><strong>الأيام المفعلة:</strong></p>';
                            const days = ['saturday', 'sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday'];
                            const dayNames = ['السبت', 'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة'];
                            
                            days.forEach((day, index) => {
                                const daySchedule = workingHours.schedule[day];
                                if (daySchedule && daySchedule.enabled) {
                                    statusHtml += `<p style="margin-right: 20px;">• ${dayNames[index]}: ${daySchedule.open} - ${daySchedule.close}</p>`;
                                } else {
                                    statusHtml += `<p style="margin-right: 20px; color: red;">• ${dayNames[index]}: مغلق</p>`;
                                }
                            });
                        }
                    } else {
                        statusHtml += '<p style="color: red;">❌ لا توجد إعدادات لأوقات العمل</p>';
                    }
                    
                    statusHtml += '</div>';
                    statusDiv.innerHTML = statusHtml;
                } catch (e) {
                    statusDiv.innerHTML = '<p style="color: red;">❌ خطأ في قراءة البيانات</p>';
                }
            } else {
                statusDiv.innerHTML = '<p style="color: orange;">⚠️ لا توجد إعدادات محفوظة</p>';
            }
        }
        
        function setupWorkingHours() {
            console.log('⚙️ إعداد أوقات العمل...');
            
            const settings = localStorage.getItem('admin-settings');
            let parsedSettings = {};
            
            if (settings) {
                try {
                    parsedSettings = JSON.parse(settings);
                } catch (e) {
                    console.error('خطأ في قراءة الإعدادات:', e);
                }
            }
            
            // إعداد أوقات العمل الافتراضية
            const workingHoursConfig = {
                enabled: true,
                schedule: {
                    saturday: { enabled: true, open: '08:00', close: '20:00' },
                    sunday: { enabled: true, open: '08:00', close: '20:00' },
                    monday: { enabled: true, open: '08:00', close: '20:00' },
                    tuesday: { enabled: true, open: '08:00', close: '20:00' },
                    wednesday: { enabled: true, open: '08:00', close: '20:00' },
                    thursday: { enabled: true, open: '08:00', close: '20:00' },
                    friday: { enabled: false, open: '14:00', close: '18:00' }
                },
                timezone: 'Asia/Aden',
                displayFormat: '12',
                closedMessage: 'نعتذر، المكتبة مغلقة حالياً',
                openMessage: 'المكتبة مفتوحة الآن'
            };
            
            // دمج الإعدادات الجديدة مع الموجودة
            parsedSettings.workingHours = workingHoursConfig;
            
            // التأكد من وجود البيانات الأساسية
            if (!parsedSettings.siteName) {
                parsedSettings.siteName = 'مكتبة أنوار دارس';
                parsedSettings.siteDescription = 'مكتبتك الشاملة للكتب والقرطاسية';
            }
            
            if (!parsedSettings.contactInfo) {
                parsedSettings.contactInfo = {
                    phone: '+967-777-123456',
                    email: '<EMAIL>',
                    address: 'صنعاء، اليمن - شارع الزبيري',
                    whatsapp: '+967-777-123456',
                    socialMedia: {
                        facebook: 'https://facebook.com/anwardares',
                        instagram: 'https://instagram.com/anwardares',
                        twitter: 'https://twitter.com/anwardares',
                        telegram: 'https://t.me/anwardares',
                        whatsapp: '+967-777-123456',
                    }
                };
            }
            
            // حفظ الإعدادات
            localStorage.setItem('admin-settings', JSON.stringify(parsedSettings));
            
            console.log('✅ تم إعداد أوقات العمل');
            console.log('📊 الإعدادات المحفوظة:', parsedSettings.workingHours);
            
            showStatus('✅ تم إعداد أوقات العمل بنجاح! يمكنك الآن مشاهدتها في صفحة "اتصل بنا"', 'success');
            
            // تحديث العرض
            setTimeout(() => {
                checkCurrentStatus();
            }, 1000);
        }
        
        function viewContactPage() {
            console.log('👁️ فتح صفحة اتصل بنا...');
            window.open('/contact', '_blank');
        }
        
        // فحص تلقائي عند تحميل الصفحة
        window.onload = function() {
            console.log('🚀 أداة إعداد أوقات العمل جاهزة');
            checkCurrentStatus();
        };
    </script>
</body>
</html>
