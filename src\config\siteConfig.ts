// ملف تكوين الموقع - يمكنك تعديل جميع النصوص والإعدادات من هنا

export const siteConfig = {
  // معلومات الموقع الأساسية
  siteName: 'مكتبة أنوار دارس',
  siteDescription: 'مكتبتك الشاملة للكتب والقرطاسية',
  siteSlogan: 'تعلم بثقة وأمان',
  
  // النصوص الرئيسية
  texts: {
    // الصفحة الرئيسية
    hero: {
      title: 'مرحباً بك في مكتبة أنوار دارس',
      subtitle: 'اكتشف مجموعة واسعة من الكتب والقرطاسية عالية الجودة بأفضل الأسعار',
      ctaButton: 'تسوق الآن',
    },
    
    // أقسام الصفحة الرئيسية
    sections: {
      featuredProducts: 'المنتجات المميزة',
      categories: 'تسوق حسب الفئة',
      whyChooseUs: 'لماذا تختارنا؟',
      testimonials: 'آراء العملاء',
    },
    
    // مميزات الموقع
    features: [
      {
        title: 'شحن مجاني',
        description: 'شحن مجاني للطلبات أكثر من 10,000 ريال',
        icon: 'truck'
      },
      {
        title: 'دعم 24/7',
        description: 'فريق دعم متاح على مدار الساعة لمساعدتك',
        icon: 'headphones'
      },
      {
        title: 'ضمان الجودة',
        description: 'جميع منتجاتنا مضمونة الجودة',
        icon: 'shield'
      },
      {
        title: 'دفع آمن',
        description: 'طرق دفع متعددة وآمنة',
        icon: 'credit-card'
      }
    ],
    
    // نصوص السلة والدفع
    cart: {
      emptyCart: 'سلة التسوق فارغة',
      emptyCartDescription: 'لم تقم بإضافة أي منتجات إلى سلة التسوق بعد',
      backToShopping: 'العودة للتسوق',
      checkout: 'إتمام الطلب',
      continueShopping: 'متابعة التسوق',
      clearCart: 'إفراغ السلة',
    },
    
    // نصوص صفحة الدفع
    checkout: {
      title: 'إتمام الطلب',
      subtitle: 'أكمل بياناتك لإتمام عملية الشراء',
      personalInfo: 'المعلومات الشخصية',
      shippingAddress: 'عنوان التوصيل',
      paymentMethod: 'طريقة الدفع',
      orderSummary: 'ملخص الطلب',
      confirmOrder: 'تأكيد الطلب',
    },
    
    // رسائل النجاح والخطأ
    messages: {
      addedToCart: 'تم إضافة المنتج إلى السلة',
      orderSuccess: 'تم إرسال طلبك بنجاح!',
      orderError: 'حدث خطأ أثناء إرسال الطلب',
      fillRequiredFields: 'يرجى ملء جميع الحقول المطلوبة',
    }
  },
  
  // الفئات الافتراضية
  defaultCategories: [
    {
      id: 'electronics',
      name: 'إلكترونيات',
      description: 'أجهزة إلكترونية وتقنية حديثة',
      icon: 'smartphone'
    },
    {
      id: 'fashion',
      name: 'أزياء',
      description: 'ملابس وإكسسوارات عصرية',
      icon: 'shirt'
    },
    {
      id: 'home',
      name: 'منزل ومطبخ',
      description: 'أدوات منزلية ومطبخية',
      icon: 'home'
    },
    {
      id: 'books',
      name: 'كتب',
      description: 'كتب ومراجع متنوعة',
      icon: 'book'
    },
    {
      id: 'sports',
      name: 'رياضة',
      description: 'معدات وأدوات رياضية',
      icon: 'dumbbell'
    }
  ],
  
  // إعدادات الاتصال
  contact: {
    phone: '+967-777-123456',
    email: '<EMAIL>',
    address: 'صنعاء، اليمن - شارع الزبيري',
    workingHours: 'السبت - الخميس: 8:00 ص - 8:00 م',
    socialMedia: {
      facebook: 'https://facebook.com/anwardares',
      instagram: 'https://instagram.com/anwardares',
      twitter: 'https://twitter.com/anwardares',
      whatsapp: '+967-777-123456',
    }
  },
  
  // إعدادات الدفع
  payment: {
    bankTransfer: {
      enabled: true,
      bankName: 'البنك الأهلي اليمني',
      accountNumber: '*********',
      accountName: 'مكتبة أنوار دارس',
      instructions: 'يرجى إرسال صورة من إيصال التحويل عبر الواتساب'
    },
    cashOnDelivery: {
      enabled: true,
      description: 'ادفع عند استلام الطلب'
    }
  },
  
  // إعدادات الشحن
  shipping: {
    freeShippingThreshold: 10000,
    shippingCost: 500,
    deliveryAreas: ['صنعاء', 'عدن', 'تعز', 'الحديدة', 'إب', 'ذمار', 'المكلا'],
    estimatedDelivery: '1-3 أيام عمل داخل المدن الرئيسية'
  }
};

// نصوص إضافية للصفحات
export const pageTexts = {
  // صفحة من نحن
  about: {
    title: 'من نحن',
    description: 'نحن متجر إلكتروني يمني يهدف إلى توفير أفضل المنتجات بأسعار منافسة',
    mission: 'مهمتنا هي جعل التسوق الإلكتروني سهلاً وآمناً للجميع',
    vision: 'رؤيتنا أن نكون المتجر الإلكتروني الأول في اليمن'
  },
  
  // صفحة الاتصال
  contact: {
    title: 'تواصل معنا',
    description: 'نحن هنا لمساعدتك. تواصل معنا في أي وقت',
    formTitle: 'أرسل لنا رسالة',
    faqTitle: 'الأسئلة الشائعة'
  },
  
  // Footer
  footer: {
    description: 'متجرك الإلكتروني الموثوق في اليمن',
    quickLinks: 'روابط سريعة',
    customerService: 'خدمة العملاء',
    followUs: 'تابعنا على',
    copyright: 'جميع الحقوق محفوظة'
  }
};
