'use client';

import React from 'react';
import { useAdmin } from '@/contexts/AdminContext';
import { Clock, Calendar, Globe } from 'lucide-react';

const WorkingHoursSettings: React.FC = () => {
  const { settings, updateSettings } = useAdmin();

  const days = [
    { key: 'saturday', name: 'السبت' },
    { key: 'sunday', name: 'الأحد' },
    { key: 'monday', name: 'الاثنين' },
    { key: 'tuesday', name: 'الثلاثاء' },
    { key: 'wednesday', name: 'الأربعاء' },
    { key: 'thursday', name: 'الخميس' },
    { key: 'friday', name: 'الجمعة' }
  ];

  const handleWorkingHoursToggle = (enabled: boolean) => {
    updateSettings({
      ...settings,
      workingHours: {
        ...settings.workingHours,
        enabled
      }
    });
  };

  const handleDayToggle = (dayKey: string, enabled: boolean) => {
    updateSettings({
      ...settings,
      workingHours: {
        ...settings.workingHours,
        schedule: {
          ...settings.workingHours?.schedule,
          [dayKey]: {
            ...settings.workingHours?.schedule?.[dayKey as keyof typeof settings.workingHours.schedule],
            enabled
          }
        }
      }
    });
  };

  const handleTimeChange = (dayKey: string, timeType: 'open' | 'close', value: string) => {
    updateSettings({
      ...settings,
      workingHours: {
        ...settings.workingHours,
        schedule: {
          ...settings.workingHours?.schedule,
          [dayKey]: {
            ...settings.workingHours?.schedule?.[dayKey as keyof typeof settings.workingHours.schedule],
            [timeType]: value
          }
        }
      }
    });
  };

  const handleMessageChange = (messageType: 'openMessage' | 'closedMessage', value: string) => {
    updateSettings({
      ...settings,
      workingHours: {
        ...settings.workingHours,
        [messageType]: value
      }
    });
  };

  const handleFormatChange = (format: string) => {
    updateSettings({
      ...settings,
      workingHours: {
        ...settings.workingHours,
        displayFormat: format
      }
    });
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex items-center gap-3 mb-6">
        <Clock className="w-6 h-6 text-blue-600" />
        <h2 className="text-xl font-bold text-gray-800">إعدادات أوقات العمل</h2>
      </div>

      {/* تفعيل/إلغاء تفعيل أوقات العمل */}
      <div className="mb-6 p-4 bg-blue-50 rounded-lg">
        <label className="flex items-center gap-3">
          <input
            type="checkbox"
            checked={settings.workingHours?.enabled || false}
            onChange={(e) => handleWorkingHoursToggle(e.target.checked)}
            className="w-5 h-5 text-blue-600 rounded focus:ring-blue-500"
          />
          <span className="text-lg font-medium text-gray-700">
            عرض أوقات العمل للعملاء
          </span>
        </label>
        <p className="text-sm text-gray-600 mt-2 mr-8">
          عند التفعيل، سيتم عرض حالة المكتبة (مفتوحة/مغلقة) للعملاء
        </p>
      </div>

      {settings.workingHours?.enabled && (
        <>
          {/* جدول أيام العمل */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
              <Calendar className="w-5 h-5" />
              جدول أيام العمل
            </h3>
            
            <div className="space-y-3">
              {days.map((day) => {
                const daySchedule = settings.workingHours?.schedule?.[day.key as keyof typeof settings.workingHours.schedule];
                
                return (
                  <div key={day.key} className="flex items-center gap-4 p-3 bg-gray-50 rounded-lg">
                    <div className="w-20">
                      <label className="flex items-center gap-2">
                        <input
                          type="checkbox"
                          checked={daySchedule?.enabled || false}
                          onChange={(e) => handleDayToggle(day.key, e.target.checked)}
                          className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
                        />
                        <span className="font-medium text-gray-700">{day.name}</span>
                      </label>
                    </div>
                    
                    {daySchedule?.enabled && (
                      <div className="flex items-center gap-3">
                        <div className="flex items-center gap-2">
                          <label className="text-sm text-gray-600">من:</label>
                          <input
                            type="time"
                            value={daySchedule.open || '08:00'}
                            onChange={(e) => handleTimeChange(day.key, 'open', e.target.value)}
                            className="px-3 py-1 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          />
                        </div>
                        
                        <div className="flex items-center gap-2">
                          <label className="text-sm text-gray-600">إلى:</label>
                          <input
                            type="time"
                            value={daySchedule.close || '20:00'}
                            onChange={(e) => handleTimeChange(day.key, 'close', e.target.value)}
                            className="px-3 py-1 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          />
                        </div>
                      </div>
                    )}
                    
                    {!daySchedule?.enabled && (
                      <span className="text-red-500 text-sm font-medium">مغلق</span>
                    )}
                  </div>
                );
              })}
            </div>
          </div>

          {/* إعدادات العرض */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
              <Globe className="w-5 h-5" />
              إعدادات العرض
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  تنسيق الوقت
                </label>
                <select
                  value={settings.workingHours?.displayFormat || '12'}
                  onChange={(e) => handleFormatChange(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="12">12 ساعة (AM/PM)</option>
                  <option value="24">24 ساعة</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  المنطقة الزمنية
                </label>
                <select
                  value={settings.workingHours?.timezone || 'Asia/Aden'}
                  onChange={(e) => updateSettings({
                    ...settings,
                    workingHours: {
                      ...settings.workingHours,
                      timezone: e.target.value
                    }
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="Asia/Aden">اليمن (Asia/Aden)</option>
                  <option value="Asia/Riyadh">السعودية (Asia/Riyadh)</option>
                  <option value="Asia/Dubai">الإمارات (Asia/Dubai)</option>
                </select>
              </div>
            </div>
          </div>

          {/* رسائل الحالة */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">
              رسائل الحالة
            </h3>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                رسالة عند فتح المكتبة
              </label>
              <input
                type="text"
                value={settings.workingHours?.openMessage || ''}
                onChange={(e) => handleMessageChange('openMessage', e.target.value)}
                placeholder="المكتبة مفتوحة الآن"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                رسالة عند إغلاق المكتبة
              </label>
              <input
                type="text"
                value={settings.workingHours?.closedMessage || ''}
                onChange={(e) => handleMessageChange('closedMessage', e.target.value)}
                placeholder="نعتذر، المكتبة مغلقة حالياً"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default WorkingHoursSettings;
