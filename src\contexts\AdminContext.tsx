'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import { Product, Category, Order, AdminSettings, BankAccount, AdminUser, AdminCredentials } from '@/types';
import { ROLE_PERMISSIONS } from '@/utils/permissions';

interface AdminContextType {
  // Products
  products: Product[];
  addProduct: (product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateProduct: (id: string, product: Partial<Product>) => void;
  deleteProduct: (id: string) => void;

  // Categories
  categories: Category[];
  addCategory: (category: Omit<Category, 'id'>) => void;
  updateCategory: (id: string, category: Partial<Category>) => void;
  deleteCategory: (id: string) => void;

  // Orders
  orders: Order[];
  updateOrderStatus: (id: string, status: Order['status']) => void;

  // Settings
  settings: AdminSettings;
  updateSettings: (settings: Partial<AdminSettings>) => void;

  // Banks
  addBank: (bank: Omit<BankAccount, 'id'>) => void;
  updateBank: (id: string, bank: Partial<BankAccount>) => void;
  deleteBank: (id: string) => void;

  // Admin Users
  adminUsers: AdminUser[];
  currentUser: AdminUser | null;
  addAdminUser: (user: Omit<AdminUser, 'id' | 'createdAt' | 'createdBy'>) => void;
  updateAdminUser: (id: string, user: Partial<AdminUser>) => void;
  deleteAdminUser: (id: string) => void;

  // Admin Credentials
  adminCredentials: AdminCredentials;
  updateAdminCredentials: (credentials: Partial<AdminCredentials>) => void;

  // Admin authentication
  isAdmin: boolean;
  adminLogin: (username: string, password: string) => boolean;
  adminLogout: () => void;
  resetAdminData: () => void;
}

const AdminContext = createContext<AdminContextType | undefined>(undefined);

// Default data
const defaultCategories: Category[] = [
  {
    id: '1',
    name: 'الكتب والملازم',
    icon: '📚',
    subcategories: [
      { id: '1-1', name: 'كتب دراسية', categoryId: '1' },
      { id: '1-2', name: 'ملازم تعليمية', categoryId: '1' },
      { id: '1-3', name: 'كتب خارج المنهج', categoryId: '1' },
    ]
  },
  {
    id: '2',
    name: 'القرطاسية',
    icon: '✏️',
    subcategories: [
      { id: '2-1', name: 'أدوات كتابية', categoryId: '2' },
      { id: '2-2', name: 'أدوات مدرسية', categoryId: '2' },
      { id: '2-3', name: 'أدوات فنية', categoryId: '2' },
    ]
  },
  {
    id: '3',
    name: 'الإلكترونيات',
    icon: '🔌',
    subcategories: [
      { id: '3-1', name: 'سماعات', categoryId: '3' },
      { id: '3-2', name: 'شواحن', categoryId: '3' },
      { id: '3-3', name: 'أجهزة ذكية', categoryId: '3' },
    ]
  },
  {
    id: '4',
    name: 'الطباعة والتصوير',
    icon: '🖨️',
    subcategories: [
      { id: '4-1', name: 'طباعة المستندات', categoryId: '4' },
      { id: '4-2', name: 'تصوير الملازم', categoryId: '4' },
      { id: '4-3', name: 'طباعة الصور', categoryId: '4' },
    ]
  }
];

const defaultProducts: Product[] = [
  {
    id: '1',
    name: 'كتاب الرياضيات - الصف الثالث الثانوي',
    description: 'كتاب الرياضيات المنهجي للصف الثالث الثانوي - طبعة حديثة',
    price: 2500,
    originalPrice: 3000,
    image: '/images/products/placeholder-book.svg',
    category: '1',
    subcategory: '1-1',
    inStock: true,
    quantity: 50,
    featured: true,
    rating: 4.5,
    reviews: 23,
    brand: 'دار المناهج اليمنية',
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: '2',
    name: 'مجموعة أقلام ملونة - 24 لون',
    description: 'مجموعة أقلام ملونة عالية الجودة مناسبة للطلاب والفنانين',
    price: 1200,
    image: '/images/products/placeholder-pens.svg',
    category: '2',
    subcategory: '2-3',
    inStock: true,
    quantity: 100,
    featured: true,
    rating: 4.8,
    reviews: 45,
    brand: 'فابر كاستل',
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: '3',
    name: 'سماعات بلوتوث لاسلكية',
    description: 'سماعات بلوتوث عالية الجودة مع إلغاء الضوضاء',
    price: 8500,
    originalPrice: 10000,
    image: '/images/products/placeholder-headphones.svg',
    category: '3',
    subcategory: '3-1',
    inStock: true,
    quantity: 25,
    featured: true,
    rating: 4.7,
    reviews: 67,
    brand: 'سوني',
    createdAt: new Date(),
    updatedAt: new Date(),
  }
];

const defaultSettings: AdminSettings = {
  siteName: 'مكتبة أنوار دارس',
  siteDescription: 'مكتبتك الشاملة للكتب والقرطاسية',
  contactInfo: {
    phone: '+967-777-123456',
    email: '<EMAIL>',
    address: 'صنعاء، اليمن - شارع الزبيري',
    whatsapp: '+967-777-123456',
    socialMedia: {
      facebook: 'https://facebook.com/anwardares',
      instagram: 'https://instagram.com/anwardares',
      twitter: 'https://twitter.com/anwardares',
      telegram: 'https://t.me/anwardares',
      whatsapp: '+967-777-123456',
    }
  },
  workingHours: {
    enabled: true,
    schedule: {
      saturday: { enabled: true, open: '08:00', close: '20:00' },
      sunday: { enabled: true, open: '08:00', close: '20:00' },
      monday: { enabled: true, open: '08:00', close: '20:00' },
      tuesday: { enabled: true, open: '08:00', close: '20:00' },
      wednesday: { enabled: true, open: '08:00', close: '20:00' },
      thursday: { enabled: true, open: '08:00', close: '20:00' },
      friday: { enabled: false, open: '14:00', close: '18:00' }
    },
    timezone: 'Asia/Aden',
    displayFormat: '12', // 12 or 24 hour format
    closedMessage: 'نعتذر، المكتبة مغلقة حالياً',
    openMessage: 'المكتبة مفتوحة الآن'
  },

  paymentMethods: {
    bankTransfer: {
      enabled: true,
      banks: [
        {
          id: '1',
          bankName: 'البنك الأهلي اليمني',
          accountNumber: '*********',
          accountName: 'مكتبة أنوار دارس',
          enabled: true,
          description: 'الحساب الرئيسي للمكتبة'
        }
      ]
    },
    cashOnDelivery: {
      enabled: true,
    }
  },
  shippingSettings: {
    freeShippingThreshold: 10000,
    shippingCost: 500,
    deliveryAreas: ['صنعاء', 'عدن', 'تعز', 'الحديدة', 'إب'],
  },
  theme: {
    primaryColor: '#2563eb',
    secondaryColor: '#64748b',
    accentColor: '#d946ef'
  },
  designSettings: {
    typography: {
      headings: {
        fontFamily: 'Cairo, system-ui, -apple-system, sans-serif',
        fontSize: '2rem',
        fontWeight: '700',
        lineHeight: '1.2',
        letterSpacing: '0.025em'
      },
      subheadings: {
        fontFamily: 'Cairo, system-ui, -apple-system, sans-serif',
        fontSize: '1.5rem',
        fontWeight: '600',
        lineHeight: '1.3',
        letterSpacing: '0.025em'
      },
      paragraphs: {
        fontFamily: 'Cairo, system-ui, -apple-system, sans-serif',
        fontSize: '1rem',
        fontWeight: '400',
        lineHeight: '1.6',
        letterSpacing: '0.025em'
      },
      notes: {
        fontFamily: 'Cairo, system-ui, -apple-system, sans-serif',
        fontSize: '0.875rem',
        fontWeight: '400',
        lineHeight: '1.5',
        letterSpacing: '0.025em'
      }
    },
    slider: {
      enabled: true,
      height: '400px',
      autoplay: true,
      autoplaySpeed: 5000,
      showDots: true,
      showArrows: true,
      pauseOnHover: true,
      transition: 'slide',
      images: [
        {
          id: '1',
          url: '/images/products/slider/55555.jpg',
          title: 'مرحباً بكم في مكتبة أنوار دارس',
          description: 'اكتشف أفضل الكتب والقرطاسية التعليمية',
          link: '/products'
        },
        {
          id: '2',
          url: '/images/products/slider/6666.jpg',
          title: 'عروض خاصة على الكتب',
          description: 'خصومات تصل إلى 50% على الكتب المدرسية والجامعية',
          link: '/offers'
        },
        {
          id: '3',
          url: '/images/products/slider/777.jpg',
          title: 'شحن مجاني',
          description: 'شحن مجاني للطلبات أكثر من 200 ريال',
          link: '/shipping'
        },
        {
          id: '4',
          url: '/images/products/slider/8888.jpg',
          title: 'خدمة عملاء ممتازة',
          description: 'فريق دعم متاح 24/7 لخدمتكم',
          link: '/contact'
        }
      ]
    },
    layout: {
      borderRadius: '0.5rem',
      spacing: '1rem',
      containerMaxWidth: '1200px',
      cardShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
      borderWidth: '1px'
    },
    colors: {
      primary: '#2563eb',
      secondary: '#64748b',
      accent: '#d946ef',
      success: '#10b981',
      warning: '#f59e0b',
      error: '#ef4444',
      info: '#3b82f6',
      background: '#f8fafc',
      surface: '#ffffff',
      text: {
        primary: '#1f2937',
        secondary: '#6b7280',
        muted: '#9ca3af',
        inverse: '#ffffff'
      },
      border: '#e5e7eb'
    },
    animations: {
      duration: '300ms',
      easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
      hoverScale: '1.05'
    }
  },
  topBarSettings: {
    enabled: true,
    backgroundColor: '#2563eb',
    textColor: '#ffffff',
    fontSize: '14px',
    fontWeight: '400',
    padding: '8px 0',
    showPhone: true,
    showEmail: true,
    showShipping: true,
    customShippingText: ''
  },
  pagesSettings: {
    customerService: {
      enabled: true,
      title: 'خدمة العملاء',
      content: 'نحن في مكتبة أنوار دارس نسعى لتقديم أفضل خدمة عملاء. يمكنكم التواصل معنا عبر الهاتف أو البريد الإلكتروني أو زيارة المكتبة مباشرة.',
      fontSize: '16px',
      fontWeight: '400',
      textColor: '#374151',
      backgroundColor: '#ffffff'
    },
    faq: {
      enabled: true,
      title: 'الأسئلة الشائعة',
      content: 'س: ما هي أوقات عمل المكتبة؟\nج: نعمل من السبت إلى الخميس من 8 صباحاً حتى 8 مساءً.\n\nس: هل يوجد توصيل؟\nج: نعم، نوفر خدمة التوصيل المجاني للطلبات أكثر من 200 ريال.',
      fontSize: '16px',
      fontWeight: '400',
      textColor: '#374151',
      backgroundColor: '#ffffff'
    },
    shippingPolicy: {
      enabled: true,
      title: 'سياسة الشحن',
      content: 'نوفر خدمة التوصيل لجميع أنحاء صنعاء. التوصيل مجاني للطلبات أكثر من 200 ريال. رسوم التوصيل للطلبات الأقل هي 100 ريال.',
      fontSize: '16px',
      fontWeight: '400',
      textColor: '#374151',
      backgroundColor: '#ffffff'
    },
    returnPolicy: {
      enabled: true,
      title: 'سياسة الإرجاع',
      content: 'يمكن إرجاع المنتجات خلال 7 أيام من تاريخ الشراء بشرط أن تكون في حالتها الأصلية. لا يمكن إرجاع الكتب المستعملة أو التالفة.',
      fontSize: '16px',
      fontWeight: '400',
      textColor: '#374151',
      backgroundColor: '#ffffff'
    },
    privacyPolicy: {
      enabled: true,
      title: 'سياسة الخصوصية',
      content: 'نحن نحترم خصوصية عملائنا ونحافظ على سرية معلوماتهم الشخصية. لا نشارك بياناتكم مع أطراف ثالثة دون موافقتكم.',
      fontSize: '16px',
      fontWeight: '400',
      textColor: '#374151',
      backgroundColor: '#ffffff'
    },
    termsConditions: {
      enabled: true,
      title: 'الشروط والأحكام',
      content: 'باستخدام موقعنا، فإنك توافق على الشروط والأحكام التالية. جميع الأسعار شاملة الضريبة. نحتفظ بالحق في تعديل الأسعار دون إشعار مسبق.',
      fontSize: '16px',
      fontWeight: '400',
      textColor: '#374151',
      backgroundColor: '#ffffff'
    }
  }
};

export const AdminProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {


  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [orders, setOrders] = useState<Order[]>([]);
  const [settings, setSettings] = useState<AdminSettings>(defaultSettings);
  const [isAdmin, setIsAdmin] = useState(false);
  const [adminUsers, setAdminUsers] = useState<AdminUser[]>([]);
  const [currentUser, setCurrentUser] = useState<AdminUser | null>(null);
  const [isDataLoaded, setIsDataLoaded] = useState(false);
  const [adminCredentials, setAdminCredentials] = useState<AdminCredentials>({
    username: 'admin',
    password: 'admin77111',
    lastUpdated: new Date(),
    isDefault: true
  });



  // Load data from localStorage
  useEffect(() => {
    console.log('🔄 Loading data from localStorage...');

    // Load products
    const savedProducts = localStorage.getItem('admin_products');
    if (savedProducts) {
      try {
        const parsedProducts = JSON.parse(savedProducts);
        console.log('✅ Loaded products from localStorage:', parsedProducts.length, 'products');
        setProducts(parsedProducts);
      } catch (error) {
        console.error('Error parsing saved products:', error);
        setProducts(defaultProducts);
        localStorage.setItem('admin_products', JSON.stringify(defaultProducts));
      }
    } else {
      console.log('📦 No saved products found, using defaults');
      setProducts(defaultProducts);
      localStorage.setItem('admin_products', JSON.stringify(defaultProducts));
    }

    // Load categories
    const savedCategories = localStorage.getItem('admin_categories');
    if (savedCategories) {
      try {
        const parsedCategories = JSON.parse(savedCategories);
        console.log('✅ Loaded categories from localStorage:', parsedCategories.length, 'categories');
        setCategories(parsedCategories);
      } catch (error) {
        console.error('Error parsing saved categories:', error);
        setCategories(defaultCategories);
        localStorage.setItem('admin_categories', JSON.stringify(defaultCategories));
      }
    } else {
      console.log('📦 No saved categories found, using defaults');
      setCategories(defaultCategories);
      localStorage.setItem('admin_categories', JSON.stringify(defaultCategories));
    }

    // Load orders
    const savedOrders = localStorage.getItem('admin_orders');
    if (savedOrders) {
      try {
        const parsedOrders = JSON.parse(savedOrders);
        console.log('✅ Loaded orders from localStorage:', parsedOrders.length, 'orders');
        setOrders(parsedOrders);
      } catch (error) {
        console.error('Error parsing saved orders:', error);
        setOrders([]);
      }
    }

    // Load settings
    const savedSettings = localStorage.getItem('admin_settings');
    if (savedSettings) {
      try {
        const parsedSettings = JSON.parse(savedSettings);
        console.log('✅ Loaded settings from localStorage');
        setSettings(parsedSettings);
      } catch (error) {
        console.error('Error parsing saved settings:', error);
        setSettings(defaultSettings);
        localStorage.setItem('admin_settings', JSON.stringify(defaultSettings));
      }
    } else {
      console.log('📦 No saved settings found, using defaults');
      setSettings(defaultSettings);
      localStorage.setItem('admin_settings', JSON.stringify(defaultSettings));
    }

    // Load admin credentials
    const savedCredentials = localStorage.getItem('admin_credentials');
    if (savedCredentials) {
      try {
        const credentials = JSON.parse(savedCredentials);
        setAdminCredentials(credentials);
        console.log('✅ Loaded admin credentials from localStorage');
      } catch (error) {
        console.error('Error parsing admin credentials:', error);
      }
    }

    // Load admin status
    const savedAdminStatus = localStorage.getItem('admin_logged_in');
    if (savedAdminStatus) {
      try {
        setIsAdmin(JSON.parse(savedAdminStatus));
      } catch (error) {
        console.error('Error parsing admin status:', error);
        setIsAdmin(false);
      }
    }

    // Load categories - if saved data exists, use it; otherwise use defaults
    if (savedCategories) {
      try {
        const parsedCategories = JSON.parse(savedCategories);
        setCategories(parsedCategories);
      } catch (error) {
        console.error('Error parsing saved categories:', error);
        setCategories(defaultCategories);
        localStorage.setItem('admin_categories', JSON.stringify(defaultCategories));
      }
    } else {
      setCategories(defaultCategories);
      localStorage.setItem('admin_categories', JSON.stringify(defaultCategories));
    }

    // Load orders
    if (savedOrders) {
      try {
        const parsedOrders = JSON.parse(savedOrders);
        setOrders(parsedOrders);
      } catch (error) {
        console.error('Error parsing saved orders:', error);
        setOrders([]);
      }
    }

    // Load settings - if saved data exists, use it; otherwise use defaults
    if (savedSettings) {
      try {
        const parsedSettings = JSON.parse(savedSettings);
        setSettings(parsedSettings);
      } catch (error) {
        console.error('Error parsing saved settings:', error);
        setSettings(defaultSettings);
        localStorage.setItem('admin_settings', JSON.stringify(defaultSettings));
      }
    } else {
      setSettings(defaultSettings);
      localStorage.setItem('admin_settings', JSON.stringify(defaultSettings));
    }

    // Load admin status
    if (savedAdminStatus) {
      try {
        setIsAdmin(JSON.parse(savedAdminStatus));
      } catch (error) {
        console.error('Error parsing admin status:', error);
        setIsAdmin(false);
      }
    }

    // Load admin users
    const savedAdminUsers = localStorage.getItem('admin_users');
    const savedCurrentUser = localStorage.getItem('current_user');

    // Always initialize with fresh default admin user to ensure correct password
    const defaultUsers: AdminUser[] = [
      {
        id: '1',
        username: 'admin',
        email: '<EMAIL>',
        name: 'المدير العام',
        password: 'admin77111',
        role: 'super_admin',
        permissions: ROLE_PERMISSIONS.super_admin,
        isActive: true,
        createdAt: new Date(),
        createdBy: 'system'
      }
    ];

    if (savedAdminUsers) {
      try {
        const users = JSON.parse(savedAdminUsers);
        // Check if admin user exists with correct password
        const adminUser = users.find((u: AdminUser) => u.username === 'admin');
        if (!adminUser || adminUser.password !== 'admin77111') {
          // Update or add admin user with correct password
          const updatedUsers = users.filter((u: AdminUser) => u.username !== 'admin');
          updatedUsers.unshift(defaultUsers[0]);
          setAdminUsers(updatedUsers);
          localStorage.setItem('admin_users', JSON.stringify(updatedUsers));
        } else {
          setAdminUsers(users);
        }
      } catch (error) {
        // If parsing fails, use default users
        setAdminUsers(defaultUsers);
        localStorage.setItem('admin_users', JSON.stringify(defaultUsers));
      }
    } else {
      // Initialize with default admin user if no users exist
      setAdminUsers(defaultUsers);
      localStorage.setItem('admin_users', JSON.stringify(defaultUsers));
    }

    if (savedCurrentUser) {
      try {
        const user = JSON.parse(savedCurrentUser);
        // If this is the main admin, ensure they have super admin privileges
        if (user.id === 'main-admin' || user.username === adminCredentials.username) {
          const mainAdminUser: AdminUser = {
            ...user,
            id: 'main-admin',
            username: adminCredentials.username,
            password: adminCredentials.password,
            role: 'super_admin',
            permissions: ROLE_PERMISSIONS.super_admin,
            isActive: true
          };
          setCurrentUser(mainAdminUser);
        } else {
          setCurrentUser(user);
        }
      } catch (error) {
        console.error('Error parsing current user:', error);
        setCurrentUser(null);
      }
    }

    // Mark as initialized after loading all data
    setIsDataLoaded(true);
    console.log('✅ All data loaded successfully');
  }, []);

  // Save data to localStorage (with initialization check)
  useEffect(() => {
    if (isDataLoaded && products.length > 0) {
      console.log('💾 Saving products to localStorage:', products.length, 'products');
      localStorage.setItem('admin_products', JSON.stringify(products));
    }
  }, [products, isDataLoaded]);

  useEffect(() => {
    if (isDataLoaded && categories.length > 0) {
      console.log('💾 Saving categories to localStorage:', categories.length, 'categories');
      localStorage.setItem('admin_categories', JSON.stringify(categories));
    }
  }, [categories, isDataLoaded]);

  useEffect(() => {
    if (isDataLoaded) {
      console.log('💾 Saving orders to localStorage:', orders.length, 'orders');
      localStorage.setItem('admin_orders', JSON.stringify(orders));
    }
  }, [orders, isDataLoaded]);

  useEffect(() => {
    if (isDataLoaded) {
      console.log('💾 Saving settings to localStorage');
      localStorage.setItem('admin_settings', JSON.stringify(settings));
    }
  }, [settings, isDataLoaded]);

  useEffect(() => {
    localStorage.setItem('admin_logged_in', JSON.stringify(isAdmin));
  }, [isAdmin]);

  useEffect(() => {
    localStorage.setItem('admin_users', JSON.stringify(adminUsers));
  }, [adminUsers]);

  useEffect(() => {
    if (currentUser) {
      localStorage.setItem('current_user', JSON.stringify(currentUser));
    }
  }, [currentUser]);

  // Product management
  const addProduct = (productData: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newProduct: Product = {
      ...productData,
      id: Date.now().toString(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    console.log('➕ Adding new product:', newProduct.name);
    setProducts(prev => {
      const updated = [...prev, newProduct];
      console.log('📊 Total products after add:', updated.length);
      return updated;
    });
  };

  const updateProduct = (id: string, productData: Partial<Product>) => {
    setProducts(prev => prev.map(product => 
      product.id === id 
        ? { ...product, ...productData, updatedAt: new Date() }
        : product
    ));
  };

  const deleteProduct = (id: string) => {
    setProducts(prev => prev.filter(product => product.id !== id));
  };

  // Category management
  const addCategory = (categoryData: Omit<Category, 'id'>) => {
    const newCategory: Category = {
      ...categoryData,
      id: Date.now().toString(),
    };
    console.log('➕ Adding new category:', newCategory.name);
    setCategories(prev => {
      const updated = [...prev, newCategory];
      console.log('📊 Total categories after add:', updated.length);
      return updated;
    });
  };

  const updateCategory = (id: string, categoryData: Partial<Category>) => {
    console.log('✏️ Updating category:', id, categoryData);
    setCategories(prev => {
      const updated = prev.map(category =>
        category.id === id ? { ...category, ...categoryData } : category
      );
      console.log('📊 Categories after update:', updated.length);
      return updated;
    });
  };

  const deleteCategory = (id: string) => {
    console.log('🗑️ Deleting category:', id);
    setCategories(prev => {
      const updated = prev.filter(category => category.id !== id);
      console.log('📊 Categories after delete:', updated.length);
      return updated;
    });
  };

  // Order management
  const updateOrderStatus = (id: string, status: Order['status']) => {
    setOrders(prev => prev.map(order => 
      order.id === id 
        ? { ...order, status, updatedAt: new Date() }
        : order
    ));
  };

  // Settings management
  const updateSettings = (newSettings: Partial<AdminSettings>) => {
    setSettings(prev => ({ ...prev, ...newSettings }));
  };

  // Bank management
  const addBank = (bankData: Omit<BankAccount, 'id'>) => {
    const newBank: BankAccount = {
      ...bankData,
      id: Date.now().toString(),
    };

    setSettings(prev => ({
      ...prev,
      paymentMethods: {
        ...prev.paymentMethods,
        bankTransfer: {
          ...prev.paymentMethods.bankTransfer,
          banks: [...prev.paymentMethods.bankTransfer.banks, newBank]
        }
      }
    }));
  };

  const updateBank = (id: string, bankData: Partial<BankAccount>) => {
    setSettings(prev => ({
      ...prev,
      paymentMethods: {
        ...prev.paymentMethods,
        bankTransfer: {
          ...prev.paymentMethods.bankTransfer,
          banks: prev.paymentMethods.bankTransfer.banks.map(bank =>
            bank.id === id ? { ...bank, ...bankData } : bank
          )
        }
      }
    }));
  };

  const deleteBank = (id: string) => {
    setSettings(prev => ({
      ...prev,
      paymentMethods: {
        ...prev.paymentMethods,
        bankTransfer: {
          ...prev.paymentMethods.bankTransfer,
          banks: prev.paymentMethods.bankTransfer.banks.filter(bank => bank.id !== id)
        }
      }
    }));
  };

  // Admin Users management
  const addAdminUser = (userData: Omit<AdminUser, 'id' | 'createdAt' | 'createdBy'>) => {
    const newUser: AdminUser = {
      ...userData,
      id: Date.now().toString(),
      createdAt: new Date(),
      createdBy: currentUser?.id || '1'
    };

    setAdminUsers(prev => [...prev, newUser]);
  };

  const updateAdminUser = (id: string, userData: Partial<AdminUser>) => {
    setAdminUsers(prev => prev.map(user =>
      user.id === id ? { ...user, ...userData } : user
    ));
  };

  const deleteAdminUser = (id: string) => {
    setAdminUsers(prev => prev.filter(user => user.id !== id));
  };

  // Admin Credentials Management
  const updateAdminCredentials = (newCredentials: Partial<AdminCredentials>) => {
    const updatedCredentials = {
      ...adminCredentials,
      ...newCredentials,
      lastUpdated: new Date(),
      isDefault: false
    };

    console.log('🔄 Updating admin credentials:', {
      old: { username: adminCredentials.username, isDefault: adminCredentials.isDefault },
      new: { username: updatedCredentials.username, isDefault: updatedCredentials.isDefault }
    });

    setAdminCredentials(updatedCredentials);

    // Save to localStorage immediately
    if (typeof window !== 'undefined') {
      localStorage.setItem('admin_credentials', JSON.stringify(updatedCredentials));
      console.log('💾 Admin credentials saved to localStorage');
    }

    // Force logout to require new credentials
    setIsAdmin(false);
    setCurrentUser(null);
    if (typeof window !== 'undefined') {
      localStorage.setItem('admin_logged_in', 'false');
      localStorage.removeItem('current_user');
    }

    console.log('✅ Admin credentials updated successfully');
  };

  // Admin authentication
  const adminLogin = (username: string, password: string) => {
    console.log('Login attempt:', { username, password });

    // Check against current admin credentials ONLY
    if (username === adminCredentials.username && password === adminCredentials.password) {
      setIsAdmin(true);

      // Create a super admin user object for the main admin
      const mainAdminUser: AdminUser = {
        id: 'main-admin',
        username: adminCredentials.username,
        email: '<EMAIL>',
        name: 'المدير الرئيسي',
        password: adminCredentials.password,
        role: 'super_admin',
        permissions: ROLE_PERMISSIONS.super_admin,
        isActive: true,
        lastLogin: new Date(),
        createdAt: new Date(),
        createdBy: 'system'
      };

      setCurrentUser(mainAdminUser);

      if (typeof window !== 'undefined') {
        localStorage.setItem('admin_logged_in', 'true');
        localStorage.setItem('current_user', JSON.stringify(mainAdminUser));
      }
      console.log('✅ Admin login successful as super admin');
      return true;
    }

    console.log('❌ Login failed - credentials do not match');
    return false;
  };

  const adminLogout = () => {
    setIsAdmin(false);
    setCurrentUser(null);
    localStorage.removeItem('current_user');
    localStorage.removeItem('admin_logged_in');
  };

  // Reset admin data (useful for debugging)
  const resetAdminData = () => {
    localStorage.removeItem('admin_users');
    localStorage.removeItem('current_user');
    localStorage.removeItem('admin_logged_in');

    const defaultUsers: AdminUser[] = [
      {
        id: '1',
        username: 'admin',
        email: '<EMAIL>',
        name: 'المدير العام',
        password: 'admin77111',
        role: 'super_admin',
        permissions: ROLE_PERMISSIONS.super_admin,
        isActive: true,
        createdAt: new Date(),
        createdBy: 'system'
      }
    ];

    setAdminUsers(defaultUsers);
    setCurrentUser(null);
    setIsAdmin(false);
    localStorage.setItem('admin_users', JSON.stringify(defaultUsers));
  };

  return (
    <AdminContext.Provider
      value={{
        products,
        addProduct,
        updateProduct,
        deleteProduct,
        categories,
        addCategory,
        updateCategory,
        deleteCategory,
        orders,
        updateOrderStatus,
        settings,
        updateSettings,
        addBank,
        updateBank,
        deleteBank,
        adminUsers,
        currentUser,
        addAdminUser,
        updateAdminUser,
        deleteAdminUser,
        adminCredentials,
        updateAdminCredentials,
        isAdmin,
        adminLogin,
        adminLogout,
        resetAdminData,
      }}
    >
      {children}
    </AdminContext.Provider>
  );
};

export const useAdmin = () => {
  const context = useContext(AdminContext);
  if (context === undefined) {
    throw new Error('useAdmin must be used within an AdminProvider');
  }
  return context;
};
