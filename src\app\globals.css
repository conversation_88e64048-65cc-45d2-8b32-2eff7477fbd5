@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;

  /* Dynamic theme colors */
  --color-primary-50: rgb(239, 246, 255);
  --color-primary-100: rgb(219, 234, 254);
  --color-primary-200: rgb(191, 219, 254);
  --color-primary-300: rgb(147, 197, 253);
  --color-primary-400: rgb(96, 165, 250);
  --color-primary-500: rgb(59, 130, 246);
  --color-primary-600: rgb(37, 99, 235);
  --color-primary-700: rgb(29, 78, 216);
  --color-primary-800: rgb(30, 64, 175);
  --color-primary-900: rgb(30, 58, 138);
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: 'Noto Sans Arabic', 'Inter', system-ui, sans-serif;
  direction: rtl;
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      135deg,
      #f8fafc 0%,
      #e2e8f0 50%,
      #f1f5f9 100%
    );
  scroll-behavior: smooth;
}

a {
  color: inherit;
  text-decoration: none;
}

/* Enhanced scrollbar with modern design */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--color-primary-400), var(--color-primary-600));
  border-radius: 10px;
  transition: all 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, var(--color-primary-500), var(--color-primary-700));
  transform: scale(1.1);
}

/* Enhanced Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% { transform: translateY(0); }
  40%, 43% { transform: translateY(-10px); }
  70% { transform: translateY(-5px); }
  90% { transform: translateY(-2px); }
}

@keyframes shimmer {
  0% { background-position: -200px 0; }
  100% { background-position: calc(200px + 100%) 0; }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-left {
  animation: fadeInLeft 0.6s ease-out;
}

.animate-fade-in-right {
  animation: fadeInRight 0.6s ease-out;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

.animate-bounce-gentle {
  animation: bounce 1s infinite;
}

.animate-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Enhanced Custom Components */
.btn-primary {
  @apply bg-primary-600 text-white font-medium py-3 px-6 rounded-lg relative overflow-hidden;
  background: linear-gradient(135deg, var(--color-primary-600), var(--color-primary-700));
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(37, 99, 235, 0.2);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
}

.btn-primary:active {
  transform: translateY(0);
}

.btn-secondary {
  @apply bg-gray-200 text-gray-800 font-medium py-3 px-6 rounded-lg relative overflow-hidden;
  background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.card {
  @apply bg-white rounded-xl p-6 relative overflow-hidden;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
}

.input-field {
  @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent;
  background: linear-gradient(145deg, #ffffff, #f8fafc);
  transition: all 0.3s ease-in-out;
}

.input-field:focus {
  transform: translateY(-1px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* RTL support */
.rtl {
  direction: rtl;
}

.ltr {
  direction: ltr;
}

/* Product Card Special Effects */
.product-card {
  @apply card cursor-pointer;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.product-card:hover {
  transform: translateY(-8px) scale(1.02);
}

.product-card:hover .product-image {
  transform: scale(1.1);
}

.product-image {
  transition: transform 0.4s ease;
}

/* Navigation Animations */
.nav-item {
  @apply relative;
  transition: all 0.3s ease;
}

.nav-item::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--color-primary-600);
  transition: width 0.3s ease;
}

.nav-item:hover::after,
.nav-item.active::after {
  width: 100%;
}

/* Loading Skeleton */
.skeleton {
  @apply bg-gray-200 rounded animate-pulse;
}

/* Gradient Utilities */
.gradient-bg {
  background: linear-gradient(135deg, var(--color-primary-600), var(--color-primary-800));
}

.gradient-text {
  background: linear-gradient(135deg, var(--color-primary-600), var(--color-primary-800));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Floating Action Button */
.fab {
  @apply fixed bottom-6 right-6 w-14 h-14 bg-primary-600 text-white rounded-full shadow-lg flex items-center justify-center z-50;
  transition: all 0.3s ease;
}

.fab:hover {
  transform: scale(1.1);
  box-shadow: 0 15px 30px rgba(37, 99, 235, 0.4);
}

/* Modal Animations */
.modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50;
  backdrop-filter: blur(5px);
  animation: fadeInUp 0.3s ease;
}

.modal-content {
  @apply bg-white rounded-xl p-6 max-w-md w-full mx-4;
  animation: fadeInUp 0.3s ease;
}

/* Notification Styles */
.notification {
  @apply fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50;
  animation: fadeInRight 0.3s ease;
}

.notification.success {
  @apply bg-green-500 text-white;
}

.notification.error {
  @apply bg-red-500 text-white;
}

.notification.info {
  @apply bg-blue-500 text-white;
}

/* Glass Effect */
.glass {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

/* Responsive Design Helpers */
@media (max-width: 768px) {
  .card {
    @apply p-4;
  }

  .btn-primary,
  .btn-secondary {
    @apply px-4 py-2 text-sm;
  }

  .fab {
    @apply bottom-4 right-4 w-12 h-12;
  }
}

/* Toast animations */
.toast-enter {
  transform: translateX(100%);
  opacity: 0;
}

.toast-enter-active {
  transform: translateX(0);
  opacity: 1;
  transition: transform 300ms ease-out, opacity 300ms ease-out;
}

.toast-exit {
  transform: translateX(0);
  opacity: 1;
}

.toast-exit-active {
  transform: translateX(100%);
  opacity: 0;
  transition: transform 300ms ease-in, opacity 300ms ease-in;
}
