'use client';

import React, { useState, useMemo } from 'react';
import { useAdmin } from '@/contexts/AdminContext';
import { 
  BarChart3, 
  TrendingUp, 
  DollarSign, 
  ShoppingCart, 
  Calendar,
  Download,
  Filter,
  Eye
} from 'lucide-react';

const Reports: React.FC = () => {
  const { orders, products } = useAdmin();
  const [selectedPeriod, setSelectedPeriod] = useState<'daily' | 'monthly' | 'yearly'>('monthly');
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);

  // Calculate reports data
  const reportsData = useMemo(() => {
    const now = new Date();
    const selectedDateObj = new Date(selectedDate);
    
    let filteredOrders = orders.filter(order => {
      const orderDate = new Date(order.createdAt);
      
      switch (selectedPeriod) {
        case 'daily':
          return orderDate.toDateString() === selectedDateObj.toDateString();
        case 'monthly':
          return orderDate.getMonth() === selectedDateObj.getMonth() && 
                 orderDate.getFullYear() === selectedDateObj.getFullYear();
        case 'yearly':
          return orderDate.getFullYear() === selectedDateObj.getFullYear();
        default:
          return true;
      }
    });

    const totalRevenue = filteredOrders.reduce((sum, order) => sum + order.totalAmount, 0);
    const totalOrders = filteredOrders.length;
    const completedOrders = filteredOrders.filter(order => order.status === 'delivered').length;
    const pendingOrders = filteredOrders.filter(order => order.status === 'pending').length;

    // Payment methods breakdown
    const bankTransferOrders = filteredOrders.filter(order => order.paymentMethod === 'bank_transfer').length;
    const cashOnDeliveryOrders = filteredOrders.filter(order => order.paymentMethod === 'cash_on_delivery').length;

    // Top products
    const productSales: Record<string, { name: string; quantity: number; revenue: number }> = {};
    
    filteredOrders.forEach(order => {
      order.items.forEach(item => {
        if (!productSales[item.product.id]) {
          productSales[item.product.id] = {
            name: item.product.name,
            quantity: 0,
            revenue: 0
          };
        }
        productSales[item.product.id].quantity += item.quantity;
        productSales[item.product.id].revenue += item.product.price * item.quantity;
      });
    });

    const topProducts = Object.entries(productSales)
      .map(([id, data]) => ({ id, ...data }))
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 5);

    // Daily sales for charts (last 7 days for daily, last 12 months for monthly, etc.)
    const chartData = [];
    if (selectedPeriod === 'daily') {
      for (let i = 6; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        const dayOrders = orders.filter(order => {
          const orderDate = new Date(order.createdAt);
          return orderDate.toDateString() === date.toDateString();
        });
        chartData.push({
          label: date.toLocaleDateString('ar-YE', { weekday: 'short' }),
          value: dayOrders.reduce((sum, order) => sum + order.totalAmount, 0),
          orders: dayOrders.length
        });
      }
    } else if (selectedPeriod === 'monthly') {
      for (let i = 11; i >= 0; i--) {
        const date = new Date();
        date.setMonth(date.getMonth() - i);
        const monthOrders = orders.filter(order => {
          const orderDate = new Date(order.createdAt);
          return orderDate.getMonth() === date.getMonth() && 
                 orderDate.getFullYear() === date.getFullYear();
        });
        chartData.push({
          label: date.toLocaleDateString('ar-YE', { month: 'short' }),
          value: monthOrders.reduce((sum, order) => sum + order.totalAmount, 0),
          orders: monthOrders.length
        });
      }
    }

    return {
      totalRevenue,
      totalOrders,
      completedOrders,
      pendingOrders,
      bankTransferOrders,
      cashOnDeliveryOrders,
      topProducts,
      chartData,
      averageOrderValue: totalOrders > 0 ? totalRevenue / totalOrders : 0
    };
  }, [orders, selectedPeriod, selectedDate]);

  const periodLabels = {
    daily: 'يومي',
    monthly: 'شهري',
    yearly: 'سنوي'
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-semibold">التقارير والإحصائيات</h2>
        <div className="flex items-center space-x-4 space-x-reverse">
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value as any)}
            className="input-field"
          >
            <option value="daily">يومي</option>
            <option value="monthly">شهري</option>
            <option value="yearly">سنوي</option>
          </select>
          <input
            type="date"
            value={selectedDate}
            onChange={(e) => setSelectedDate(e.target.value)}
            className="input-field"
          />
          <button className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors flex items-center">
            <Download className="w-4 h-4 ml-2" />
            تصدير
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="bg-green-100 p-3 rounded-full">
              <DollarSign className="w-6 h-6 text-green-600" />
            </div>
            <div className="mr-4">
              <p className="text-sm text-gray-600">إجمالي المبيعات</p>
              <p className="text-2xl font-bold text-gray-800">{reportsData.totalRevenue.toLocaleString()} ريال</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="bg-blue-100 p-3 rounded-full">
              <ShoppingCart className="w-6 h-6 text-blue-600" />
            </div>
            <div className="mr-4">
              <p className="text-sm text-gray-600">عدد الطلبات</p>
              <p className="text-2xl font-bold text-gray-800">{reportsData.totalOrders}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="bg-purple-100 p-3 rounded-full">
              <TrendingUp className="w-6 h-6 text-purple-600" />
            </div>
            <div className="mr-4">
              <p className="text-sm text-gray-600">متوسط قيمة الطلب</p>
              <p className="text-2xl font-bold text-gray-800">{reportsData.averageOrderValue.toLocaleString()} ريال</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="bg-orange-100 p-3 rounded-full">
              <BarChart3 className="w-6 h-6 text-orange-600" />
            </div>
            <div className="mr-4">
              <p className="text-sm text-gray-600">الطلبات المكتملة</p>
              <p className="text-2xl font-bold text-gray-800">{reportsData.completedOrders}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Charts and Details */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Sales Chart */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold mb-4">مبيعات {periodLabels[selectedPeriod]}</h3>
          <div className="space-y-3">
            {reportsData.chartData.map((item, index) => (
              <div key={index} className="flex items-center justify-between">
                <span className="text-sm text-gray-600">{item.label}</span>
                <div className="flex items-center space-x-2 space-x-reverse">
                  <span className="text-sm font-medium">{item.value.toLocaleString()} ريال</span>
                  <div className="w-32 bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-primary-600 h-2 rounded-full"
                      style={{ 
                        width: `${Math.max(5, (item.value / Math.max(...reportsData.chartData.map(d => d.value))) * 100)}%` 
                      }}
                    ></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Top Products */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold mb-4">أفضل المنتجات مبيعاً</h3>
          <div className="space-y-3">
            {reportsData.topProducts.map((product, index) => (
              <div key={product.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center">
                  <span className="bg-primary-600 text-white text-xs w-6 h-6 rounded-full flex items-center justify-center ml-3">
                    {index + 1}
                  </span>
                  <div>
                    <p className="font-medium">{product.name}</p>
                    <p className="text-sm text-gray-600">الكمية: {product.quantity}</p>
                  </div>
                </div>
                <span className="font-semibold text-green-600">{product.revenue.toLocaleString()} ريال</span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Payment Methods & Order Status */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold mb-4">طرق الدفع</h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span>تحويل بنكي</span>
              <span className="font-semibold">{reportsData.bankTransferOrders} طلب</span>
            </div>
            <div className="flex justify-between items-center">
              <span>دفع عند الاستلام</span>
              <span className="font-semibold">{reportsData.cashOnDeliveryOrders} طلب</span>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold mb-4">حالة الطلبات</h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span>طلبات معلقة</span>
              <span className="font-semibold text-yellow-600">{reportsData.pendingOrders}</span>
            </div>
            <div className="flex justify-between items-center">
              <span>طلبات مكتملة</span>
              <span className="font-semibold text-green-600">{reportsData.completedOrders}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Reports;
