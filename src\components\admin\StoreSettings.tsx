'use client';

import React, { useState } from 'react';
import { useAdmin } from '@/contexts/AdminContext';
import { AdminSettings, BankAccount } from '@/types';
import WorkingHoursSettings from './WorkingHoursSettings';
import PagesSettings from './PagesSettings';
import TopBarSettings from './TopBarSettings';

import {
  Store,
  Phone,
  Mail,
  MapPin,
  CreditCard,
  Truck,
  Globe,
  Save,
  Facebook,
  Instagram,
  MessageCircle,
  Plus,
  Edit,
  Trash2,
  Palette,
  Clock,
  Monitor
} from 'lucide-react';

const StoreSettings: React.FC = () => {
  const { settings, updateSettings, addBank, updateBank, deleteBank } = useAdmin();
  const [formData, setFormData] = useState<AdminSettings>(settings);
  const [activeSection, setActiveSection] = useState('general');
  const [isSaving, setIsSaving] = useState(false);
  const [saveMessage, setSaveMessage] = useState('');
  const [showAddBank, setShowAddBank] = useState(false);
  const [editingBank, setEditingBank] = useState<string | null>(null);
  const [newBank, setNewBank] = useState<Omit<BankAccount, 'id'>>({
    bankName: '',
    accountNumber: '',
    accountName: '',
    enabled: true,
    description: ''
  });

  // Update form data when settings change
  React.useEffect(() => {
    setFormData(settings);
  }, [settings]);

  const handleInputChange = (section: string, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [section]: {
        ...prev[section as keyof AdminSettings],
        [field]: value
      }
    }));
  };

  const handleNestedInputChange = (section: string, subsection: string, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [section]: {
        ...prev[section as keyof AdminSettings],
        [subsection]: {
          ...(prev[section as keyof AdminSettings] as any)[subsection],
          [field]: value
        }
      }
    }));
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      // Validate required fields
      if (!formData.siteName.trim()) {
        setSaveMessage('يرجى إدخال اسم المتجر');
        setIsSaving(false);
        return;
      }

      if (!formData.contactInfo.phone.trim() || !formData.contactInfo.email.trim()) {
        setSaveMessage('يرجى إدخال معلومات الاتصال الأساسية');
        setIsSaving(false);
        return;
      }

      updateSettings(formData);
      setSaveMessage('تم حفظ الإعدادات بنجاح! ستظهر التغييرات في الموقع فوراً.');
      setTimeout(() => setSaveMessage(''), 5000);
    } catch (error) {
      setSaveMessage('حدث خطأ أثناء حفظ الإعدادات');
      setTimeout(() => setSaveMessage(''), 5000);
    } finally {
      setIsSaving(false);
    }
  };

  const handleAddBank = () => {
    if (newBank.bankName && newBank.accountNumber && newBank.accountName) {
      addBank(newBank);
      setNewBank({
        bankName: '',
        accountNumber: '',
        accountName: '',
        enabled: true,
        description: ''
      });
      setShowAddBank(false);
      setSaveMessage('تم إضافة البنك بنجاح!');
      setTimeout(() => setSaveMessage(''), 3000);
    }
  };

  const handleUpdateBank = (bankId: string, bankData: Partial<BankAccount>) => {
    updateBank(bankId, bankData);
    setEditingBank(null);
    setSaveMessage('تم تحديث البنك بنجاح!');
    setTimeout(() => setSaveMessage(''), 3000);
  };

  const handleDeleteBank = (bankId: string) => {
    if (confirm('هل أنت متأكد من حذف هذا البنك؟')) {
      deleteBank(bankId);
      setSaveMessage('تم حذف البنك بنجاح!');
      setTimeout(() => setSaveMessage(''), 3000);
    }
  };

  const sections = [
    { id: 'general', name: 'معلومات عامة', icon: Store },
    { id: 'contact', name: 'معلومات الاتصال', icon: Phone },
    { id: 'payment', name: 'طرق الدفع', icon: CreditCard },
    { id: 'shipping', name: 'إعدادات الشحن', icon: Truck },
    { id: 'theme', name: 'ألوان الموقع', icon: Palette },
    { id: 'top-bar', name: 'الشريط العلوي', icon: Monitor },
    { id: 'working-hours', name: 'أوقات العمل', icon: Clock }
  ];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-semibold">إعدادات المتجر</h2>
        <button
          onClick={handleSave}
          disabled={isSaving}
          className="bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-colors disabled:opacity-50 flex items-center"
        >
          <Save className="w-5 h-5 ml-2" />
          {isSaving ? 'جاري الحفظ...' : 'حفظ التغييرات'}
        </button>
      </div>

      {saveMessage && (
        <div className={`p-4 rounded-lg ${
          saveMessage.includes('نجاح') ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
        }`}>
          {saveMessage}
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Navigation */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow-md p-4">
            <nav className="space-y-2">
              {sections.map((section) => (
                <button
                  key={section.id}
                  onClick={() => setActiveSection(section.id)}
                  className={`w-full flex items-center px-3 py-2 rounded-lg text-right transition-colors ${
                    activeSection === section.id
                      ? 'bg-primary-100 text-primary-700'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  <section.icon className="w-5 h-5 ml-3" />
                  {section.name}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Content */}
        <div className="lg:col-span-3">
          <div className="bg-white rounded-lg shadow-md p-6">
            
            {/* General Settings */}
            {activeSection === 'general' && (
              <div className="space-y-6">
                <div className="flex items-center mb-6">
                  <Store className="w-6 h-6 text-primary-600 ml-3" />
                  <h3 className="text-xl font-semibold">المعلومات العامة</h3>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      اسم المتجر *
                    </label>
                    <input
                      type="text"
                      value={formData.siteName}
                      onChange={(e) => setFormData(prev => ({ ...prev, siteName: e.target.value }))}
                      className="input-field"
                      placeholder="اسم متجرك"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      رابط الشعار (اختياري)
                    </label>
                    <input
                      type="url"
                      value={formData.logo || ''}
                      onChange={(e) => setFormData(prev => ({ ...prev, logo: e.target.value }))}
                      className="input-field"
                      placeholder="https://example.com/logo.png"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    وصف المتجر *
                  </label>
                  <textarea
                    value={formData.siteDescription}
                    onChange={(e) => setFormData(prev => ({ ...prev, siteDescription: e.target.value }))}
                    className="input-field"
                    rows={3}
                    placeholder="وصف مختصر عن متجرك"
                  />
                </div>
              </div>
            )}

            {/* Contact Settings */}
            {activeSection === 'contact' && (
              <div className="space-y-6">
                <div className="flex items-center mb-6">
                  <Phone className="w-6 h-6 text-primary-600 ml-3" />
                  <h3 className="text-xl font-semibold">معلومات الاتصال</h3>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      رقم الهاتف *
                    </label>
                    <input
                      type="tel"
                      value={formData.contactInfo.phone}
                      onChange={(e) => handleInputChange('contactInfo', 'phone', e.target.value)}
                      className="input-field"
                      placeholder="+967-1-234567"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      البريد الإلكتروني *
                    </label>
                    <input
                      type="email"
                      value={formData.contactInfo.email}
                      onChange={(e) => handleInputChange('contactInfo', 'email', e.target.value)}
                      className="input-field"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    العنوان *
                  </label>
                  <input
                    type="text"
                    value={formData.contactInfo.address}
                    onChange={(e) => handleInputChange('contactInfo', 'address', e.target.value)}
                    className="input-field"
                    placeholder="العنوان الكامل"
                  />
                </div>

                <div className="border-t pt-6">
                  <h4 className="text-lg font-semibold mb-4 flex items-center">
                    <Globe className="w-5 h-5 ml-2" />
                    وسائل التواصل الاجتماعي
                  </h4>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                        <Facebook className="w-4 h-4 ml-2" />
                        فيسبوك
                      </label>
                      <input
                        type="url"
                        value={formData.contactInfo.socialMedia.facebook || ''}
                        onChange={(e) => handleNestedInputChange('contactInfo', 'socialMedia', 'facebook', e.target.value)}
                        className="input-field"
                        placeholder="https://facebook.com/yourpage"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                        <Instagram className="w-4 h-4 ml-2" />
                        إنستغرام
                      </label>
                      <input
                        type="url"
                        value={formData.contactInfo.socialMedia.instagram || ''}
                        onChange={(e) => handleNestedInputChange('contactInfo', 'socialMedia', 'instagram', e.target.value)}
                        className="input-field"
                        placeholder="https://instagram.com/yourpage"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                        <MessageCircle className="w-4 h-4 ml-2" />
                        واتساب
                      </label>
                      <input
                        type="tel"
                        value={formData.contactInfo.socialMedia.whatsapp || ''}
                        onChange={(e) => handleNestedInputChange('contactInfo', 'socialMedia', 'whatsapp', e.target.value)}
                        className="input-field"
                        placeholder="+967712345678"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        تويتر
                      </label>
                      <input
                        type="url"
                        value={formData.contactInfo.socialMedia.twitter || ''}
                        onChange={(e) => handleNestedInputChange('contactInfo', 'socialMedia', 'twitter', e.target.value)}
                        className="input-field"
                        placeholder="https://twitter.com/yourpage"
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Payment Settings */}
            {activeSection === 'payment' && (
              <div className="space-y-6">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center">
                    <CreditCard className="w-6 h-6 text-primary-600 ml-3" />
                    <h3 className="text-xl font-semibold">طرق الدفع</h3>
                  </div>
                </div>

                {/* Bank Transfer */}
                <div className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="text-lg font-semibold">التحويل البنكي</h4>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={formData.paymentMethods.bankTransfer.enabled}
                        onChange={(e) => handleNestedInputChange('paymentMethods', 'bankTransfer', 'enabled', e.target.checked)}
                        className="ml-2"
                      />
                      <span className="text-sm">تفعيل</span>
                    </label>
                  </div>

                  {formData.paymentMethods.bankTransfer.enabled && (
                    <div className="space-y-4">
                      {/* Banks List */}
                      <div className="space-y-3">
                        {formData.paymentMethods.bankTransfer.banks.map((bank) => (
                          <div key={bank.id} className="border rounded-lg p-4 bg-gray-50">
                            <div className="flex items-center justify-between mb-2">
                              <div className="flex items-center">
                                <h5 className="font-semibold">{bank.bankName}</h5>
                                <span className={`ml-2 px-2 py-1 text-xs rounded-full ${
                                  bank.enabled ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                }`}>
                                  {bank.enabled ? 'مفعل' : 'معطل'}
                                </span>
                              </div>
                              <div className="flex space-x-2 space-x-reverse">
                                <button
                                  onClick={() => setEditingBank(bank.id)}
                                  className="text-blue-600 hover:text-blue-800"
                                >
                                  <Edit className="w-4 h-4" />
                                </button>
                                <button
                                  onClick={() => handleDeleteBank(bank.id)}
                                  className="text-red-600 hover:text-red-800"
                                >
                                  <Trash2 className="w-4 h-4" />
                                </button>
                              </div>
                            </div>
                            <div className="text-sm text-gray-600">
                              <p>رقم الحساب: {bank.accountNumber}</p>
                              <p>اسم صاحب الحساب: {bank.accountName}</p>
                              {bank.description && <p>الوصف: {bank.description}</p>}
                            </div>
                          </div>
                        ))}
                      </div>

                      {/* Add Bank Button */}
                      <button
                        onClick={() => setShowAddBank(true)}
                        className="w-full border-2 border-dashed border-gray-300 rounded-lg p-4 text-gray-600 hover:border-primary-500 hover:text-primary-600 transition-colors flex items-center justify-center"
                      >
                        <Plus className="w-5 h-5 ml-2" />
                        إضافة بنك جديد
                      </button>

                      {/* Add Bank Form */}
                      {showAddBank && (
                        <div className="border rounded-lg p-4 bg-blue-50">
                          <h5 className="font-semibold mb-4">إضافة بنك جديد</h5>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                اسم البنك *
                              </label>
                              <input
                                type="text"
                                value={newBank.bankName}
                                onChange={(e) => setNewBank(prev => ({ ...prev, bankName: e.target.value }))}
                                className="input-field"
                                placeholder="البنك الأهلي اليمني"
                              />
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                رقم الحساب *
                              </label>
                              <input
                                type="text"
                                value={newBank.accountNumber}
                                onChange={(e) => setNewBank(prev => ({ ...prev, accountNumber: e.target.value }))}
                                className="input-field"
                                placeholder="*********"
                              />
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                اسم صاحب الحساب *
                              </label>
                              <input
                                type="text"
                                value={newBank.accountName}
                                onChange={(e) => setNewBank(prev => ({ ...prev, accountName: e.target.value }))}
                                className="input-field"
                                placeholder="اسم صاحب الحساب"
                              />
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                الوصف (اختياري)
                              </label>
                              <input
                                type="text"
                                value={newBank.description || ''}
                                onChange={(e) => setNewBank(prev => ({ ...prev, description: e.target.value }))}
                                className="input-field"
                                placeholder="وصف البنك"
                              />
                            </div>
                          </div>
                          <div className="flex justify-end space-x-2 space-x-reverse mt-4">
                            <button
                              onClick={() => setShowAddBank(false)}
                              className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
                            >
                              إلغاء
                            </button>
                            <button
                              onClick={handleAddBank}
                              className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700"
                            >
                              إضافة البنك
                            </button>
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>

                {/* Cash on Delivery */}
                <div className="border rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <h4 className="text-lg font-semibold">الدفع عند الاستلام</h4>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={formData.paymentMethods.cashOnDelivery.enabled}
                        onChange={(e) => handleNestedInputChange('paymentMethods', 'cashOnDelivery', 'enabled', e.target.checked)}
                        className="ml-2"
                      />
                      <span className="text-sm">تفعيل</span>
                    </label>
                  </div>
                </div>
              </div>
            )}

            {/* Shipping Settings */}
            {activeSection === 'shipping' && (
              <div className="space-y-6">
                <div className="flex items-center mb-6">
                  <Truck className="w-6 h-6 text-primary-600 ml-3" />
                  <h3 className="text-xl font-semibold">إعدادات الشحن</h3>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      تكلفة الشحن (ريال) *
                    </label>
                    <input
                      type="number"
                      value={formData.shippingSettings.shippingCost}
                      onChange={(e) => handleInputChange('shippingSettings', 'shippingCost', Number(e.target.value))}
                      className="input-field"
                      placeholder="500"
                      min="0"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      الحد الأدنى للشحن المجاني (ريال) *
                    </label>
                    <input
                      type="number"
                      value={formData.shippingSettings.freeShippingThreshold}
                      onChange={(e) => handleInputChange('shippingSettings', 'freeShippingThreshold', Number(e.target.value))}
                      className="input-field"
                      placeholder="10000"
                      min="0"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    مناطق التوصيل *
                  </label>
                  <div className="space-y-2">
                    {formData.shippingSettings.deliveryAreas.map((area, index) => (
                      <div key={index} className="flex items-center space-x-2 space-x-reverse">
                        <input
                          type="text"
                          value={area}
                          onChange={(e) => {
                            const newAreas = [...formData.shippingSettings.deliveryAreas];
                            newAreas[index] = e.target.value;
                            handleInputChange('shippingSettings', 'deliveryAreas', newAreas);
                          }}
                          className="input-field flex-1"
                          placeholder="اسم المنطقة"
                        />
                        <button
                          type="button"
                          onClick={() => {
                            const newAreas = formData.shippingSettings.deliveryAreas.filter((_, i) => i !== index);
                            handleInputChange('shippingSettings', 'deliveryAreas', newAreas);
                          }}
                          className="text-red-600 hover:text-red-800 px-2"
                        >
                          حذف
                        </button>
                      </div>
                    ))}
                    <button
                      type="button"
                      onClick={() => {
                        const newAreas = [...formData.shippingSettings.deliveryAreas, ''];
                        handleInputChange('shippingSettings', 'deliveryAreas', newAreas);
                      }}
                      className="text-primary-600 hover:text-primary-800 text-sm"
                    >
                      + إضافة منطقة جديدة
                    </button>
                  </div>
                </div>

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="font-semibold text-blue-800 mb-2">معاينة إعدادات الشحن:</h4>
                  <ul className="text-sm text-blue-700 space-y-1">
                    <li>• تكلفة الشحن: {formData.shippingSettings.shippingCost.toLocaleString()} ريال</li>
                    <li>• شحن مجاني للطلبات أكثر من: {formData.shippingSettings.freeShippingThreshold.toLocaleString()} ريال</li>
                    <li>• عدد مناطق التوصيل: {formData.shippingSettings.deliveryAreas.length} منطقة</li>
                  </ul>
                </div>
              </div>
            )}

            {/* Theme Settings */}
            {activeSection === 'theme' && (
              <div className="space-y-6">
                <div className="flex items-center mb-6">
                  <Palette className="w-6 h-6 text-primary-600 ml-3" />
                  <h3 className="text-xl font-semibold">ألوان الموقع</h3>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      اللون الأساسي
                    </label>
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <input
                        type="color"
                        value={formData.theme.primaryColor}
                        onChange={(e) => handleInputChange('theme', 'primaryColor', e.target.value)}
                        className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                      />
                      <input
                        type="text"
                        value={formData.theme.primaryColor}
                        onChange={(e) => handleInputChange('theme', 'primaryColor', e.target.value)}
                        className="input-field flex-1"
                        placeholder="#2563eb"
                      />
                    </div>
                    <p className="text-xs text-gray-500 mt-1">يستخدم للأزرار والروابط الرئيسية</p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      اللون الثانوي
                    </label>
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <input
                        type="color"
                        value={formData.theme.secondaryColor}
                        onChange={(e) => handleInputChange('theme', 'secondaryColor', e.target.value)}
                        className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                      />
                      <input
                        type="text"
                        value={formData.theme.secondaryColor}
                        onChange={(e) => handleInputChange('theme', 'secondaryColor', e.target.value)}
                        className="input-field flex-1"
                        placeholder="#64748b"
                      />
                    </div>
                    <p className="text-xs text-gray-500 mt-1">يستخدم للنصوص والحدود</p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      لون التمييز
                    </label>
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <input
                        type="color"
                        value={formData.theme.accentColor}
                        onChange={(e) => handleInputChange('theme', 'accentColor', e.target.value)}
                        className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                      />
                      <input
                        type="text"
                        value={formData.theme.accentColor}
                        onChange={(e) => handleInputChange('theme', 'accentColor', e.target.value)}
                        className="input-field flex-1"
                        placeholder="#d946ef"
                      />
                    </div>
                    <p className="text-xs text-gray-500 mt-1">يستخدم للعناصر المميزة</p>
                  </div>
                </div>

                <div className="bg-gray-50 border rounded-lg p-6">
                  <h4 className="font-semibold mb-4">معاينة الألوان</h4>
                  <div className="space-y-4">
                    <div className="flex items-center space-x-4 space-x-reverse">
                      <button
                        style={{ backgroundColor: formData.theme.primaryColor }}
                        className="px-4 py-2 text-white rounded-lg"
                      >
                        زر أساسي
                      </button>
                      <button
                        style={{
                          backgroundColor: formData.theme.secondaryColor,
                          color: 'white'
                        }}
                        className="px-4 py-2 rounded-lg"
                      >
                        زر ثانوي
                      </button>
                      <button
                        style={{ backgroundColor: formData.theme.accentColor }}
                        className="px-4 py-2 text-white rounded-lg"
                      >
                        زر مميز
                      </button>
                    </div>
                    <div className="text-sm text-gray-600">
                      <p style={{ color: formData.theme.primaryColor }}>نص باللون الأساسي</p>
                      <p style={{ color: formData.theme.secondaryColor }}>نص باللون الثانوي</p>
                      <p style={{ color: formData.theme.accentColor }}>نص باللون المميز</p>
                    </div>
                  </div>
                </div>

                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <h4 className="font-semibold text-yellow-800 mb-2">ملاحظة مهمة:</h4>
                  <p className="text-sm text-yellow-700">
                    بعد تغيير الألوان، قد تحتاج إلى إعادة تحميل الصفحة لرؤية التغييرات في جميع أنحاء الموقع.
                  </p>
                </div>

                {/* إعدادات الصفحات والسياسات */}
                <div className="mt-8 pt-8 border-t border-gray-200">
                  <PagesSettings />
                </div>
              </div>
            )}

            {/* Top Bar Section */}
            {activeSection === 'top-bar' && (
              <div className="space-y-6">
                <TopBarSettings />
              </div>
            )}

            {/* Working Hours Section */}
            {activeSection === 'working-hours' && (
              <div className="space-y-6">
                <WorkingHoursSettings />
              </div>
            )}


          </div>
        </div>
      </div>
    </div>
  );
};

export default StoreSettings;
