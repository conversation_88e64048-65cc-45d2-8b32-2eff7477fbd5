// ملف لإعادة تعيين الإعدادات ومسح البيانات المحفوظة

// دالة لمسح جميع البيانات المحفوظة في localStorage
function clearAllStoredData() {
  console.log('🧹 بدء مسح البيانات المحفوظة...');
  
  // قائمة بجميع المفاتيح المحتملة
  const keysToRemove = [
    'admin-settings',
    'admin-products', 
    'admin-orders',
    'admin-categories',
    'admin-design-settings',
    'admin-slider-settings',
    'admin-colors',
    'admin-fonts',
    'admin-typography',
    'yemenecommerce-settings',
    'ecommerce-settings',
    'site-settings',
    'slider-settings',
    'design-settings'
  ];
  
  // مسح المفاتيح المحددة
  keysToRemove.forEach(key => {
    if (localStorage.getItem(key)) {
      localStorage.removeItem(key);
      console.log(`✅ تم مسح: ${key}`);
    }
  });
  
  // مسح أي مفاتيح أخرى تحتوي على كلمات مفتاحية
  const allKeys = Object.keys(localStorage);
  allKeys.forEach(key => {
    if (key.includes('admin') || key.includes('settings') || key.includes('yemen') || key.includes('ecommerce')) {
      localStorage.removeItem(key);
      console.log(`✅ تم مسح: ${key}`);
    }
  });
  
  console.log('🎉 تم مسح جميع البيانات المحفوظة!');
}

// دالة لإعادة تعيين الإعدادات إلى القيم الجديدة
function resetToNewDefaults() {
  console.log('🔄 إعادة تعيين الإعدادات إلى القيم الجديدة...');
  
  const newDefaultSettings = {
    siteName: 'مكتبة أنوار دارس',
    siteDescription: 'مكتبتك الشاملة للكتب والقرطاسية',
    contactInfo: {
      phone: '+967-1-234567',
      email: '<EMAIL>',
      address: 'صنعاء، اليمن',
    },
    paymentMethods: {
      bankTransfer: {
        enabled: true,
        banks: [
          {
            id: '1',
            bankName: 'البنك الأهلي اليمني',
            accountNumber: '*********',
            accountName: 'مكتبة أنوار دارس',
            enabled: true,
            description: 'الحساب الرئيسي للمكتبة'
          }
        ]
      }
    },
    designSettings: {
      slider: {
        enabled: true,
        height: '400px',
        autoplay: true,
        autoplaySpeed: 5000,
        showDots: true,
        showArrows: true,
        pauseOnHover: true,
        transition: 'slide',
        images: [
          {
            id: '1',
            url: '/images/products/slider/55555.jpg',
            title: 'مرحباً بكم في مكتبة أنوار دارس',
            description: 'اكتشف أفضل الكتب والقرطاسية',
            link: '/products'
          },
          {
            id: '2',
            url: '/images/products/slider/6666.jpg',
            title: 'عروض خاصة',
            description: 'خصومات تصل إلى 50% على الكتب المختارة',
            link: '/offers'
          },
          {
            id: '3',
            url: '/images/products/slider/777.jpg',
            title: 'شحن مجاني',
            description: 'شحن مجاني للطلبات أكثر من 200 ريال',
            link: '/shipping'
          },
          {
            id: '4',
            url: '/images/products/slider/8888.jpg',
            title: 'خدمة عملاء ممتازة',
            description: 'فريق دعم متاح 24/7 لخدمتكم',
            link: '/contact'
          }
        ]
      }
    }
  };
  
  // حفظ الإعدادات الجديدة
  localStorage.setItem('admin-settings', JSON.stringify(newDefaultSettings));
  console.log('✅ تم حفظ الإعدادات الجديدة');
  
  return newDefaultSettings;
}

// دالة شاملة لإعادة التعيين
function fullReset() {
  console.log('🚀 بدء إعادة التعيين الشاملة...');
  
  // 1. مسح البيانات القديمة
  clearAllStoredData();
  
  // 2. تعيين الإعدادات الجديدة
  const newSettings = resetToNewDefaults();
  
  // 3. إعادة تحميل الصفحة
  console.log('🔄 إعادة تحميل الصفحة...');
  setTimeout(() => {
    window.location.reload();
  }, 1000);
  
  return newSettings;
}

// تصدير الدوال للاستخدام
if (typeof window !== 'undefined') {
  window.clearAllStoredData = clearAllStoredData;
  window.resetToNewDefaults = resetToNewDefaults;
  window.fullReset = fullReset;
  
  console.log('🛠️ دوال إعادة التعيين متاحة:');
  console.log('- clearAllStoredData() - لمسح البيانات المحفوظة');
  console.log('- resetToNewDefaults() - لتعيين الإعدادات الجديدة');
  console.log('- fullReset() - لإعادة التعيين الشاملة');
}

// تشغيل تلقائي عند تحميل الملف
console.log('📋 ملف إعادة التعيين جاهز للاستخدام');
console.log('💡 لإعادة التعيين الشاملة، اكتب في Console: fullReset()');
