'use client';

import React, { useEffect, useState } from 'react';

interface CustomFont {
  family: string;
  name: string;
  path: string;
  type: string;
}

const CustomFontLoader: React.FC = () => {
  const [fontsLoaded, setFontsLoaded] = useState(false);

  useEffect(() => {
    const loadCustomFonts = async () => {
      try {
        // جلب قائمة الخطوط المخصصة
        const response = await fetch('/api/fonts');
        const data = await response.json();
        
        if (data.fonts && data.fonts.length > 0) {
          // إنشاء CSS ديناميكي للخطوط المخصصة
          const fontCSS = data.fonts.map((font: CustomFont) => {
            const fontType = font.type === 'woff2' ? 'woff2' : 
                           font.type === 'woff' ? 'woff' : 
                           font.type === 'ttf' ? 'truetype' : 
                           font.type === 'otf' ? 'opentype' : 'truetype';
            
            return `
              @font-face {
                font-family: '${font.family}';
                src: url('${font.path}') format('${fontType}');
                font-weight: normal;
                font-style: normal;
                font-display: swap;
              }
            `;
          }).join('\n');

          // إضافة CSS للصفحة
          const styleId = 'custom-fonts-loader';
          let styleElement = document.getElementById(styleId) as HTMLStyleElement;
          
          if (!styleElement) {
            styleElement = document.createElement('style');
            styleElement.id = styleId;
            document.head.appendChild(styleElement);
          }
          
          styleElement.textContent = fontCSS;
          
          console.log('تم تحميل الخطوط المخصصة:', data.fonts.map((f: CustomFont) => f.family));
          setFontsLoaded(true);
        }
      } catch (error) {
        console.error('خطأ في تحميل الخطوط المخصصة:', error);
      }
    };

    loadCustomFonts();
  }, []);

  // إضافة CSS لضمان تطبيق الخطوط على العناصر
  useEffect(() => {
    if (fontsLoaded) {
      const globalStyleId = 'custom-fonts-global';
      let globalStyleElement = document.getElementById(globalStyleId) as HTMLStyleElement;
      
      if (!globalStyleElement) {
        globalStyleElement = document.createElement('style');
        globalStyleElement.id = globalStyleId;
        document.head.appendChild(globalStyleElement);
      }
      
      globalStyleElement.textContent = `
        /* تطبيق الخطوط المخصصة على العناصر */
        .font-custom {
          font-family: var(--custom-font-family, inherit) !important;
        }
        
        /* تحسين عرض الخطوط */
        * {
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
          text-rendering: optimizeLegibility;
        }
      `;
    }
  }, [fontsLoaded]);

  return null; // هذا المكون لا يعرض أي شيء، فقط يحمل الخطوط
};

export default CustomFontLoader;
