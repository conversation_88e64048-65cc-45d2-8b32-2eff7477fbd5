'use client';

import React from 'react';

// مكون مثال لعرض الخطوط المخصصة
// هذا المكون يوضح كيفية استخدام الخطوط المختلفة في الموقع

const FontExample: React.FC = () => {
  return (
    <div className="container mx-auto px-4 py-8 space-y-8">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold text-gray-800 mb-4">
          معرض الخطوط المخصصة
        </h1>
        <p className="text-gray-600">
          أمثلة على استخدام الخطوط المختلفة في الموقع
        </p>
      </div>

      {/* الخطوط العربية */}
      <section className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-2xl font-bold text-gray-800 mb-6">الخطوط العربية</h2>
        
        <div className="space-y-4">
          <div className="border-b pb-4">
            <h3 className="font-amiri text-xl font-bold text-gray-700 mb-2">خط أميري (Amiri)</h3>
            <p className="font-amiri text-lg">
              هذا نص تجريبي بخط أميري الجميل والأنيق للنصوص العربية التقليدية
            </p>
            <code className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded">
              className="font-amiri"
            </code>
          </div>

          <div className="border-b pb-4">
            <h3 className="font-cairo text-xl font-bold text-gray-700 mb-2">خط القاهرة (Cairo)</h3>
            <p className="font-cairo text-lg">
              هذا نص تجريبي بخط القاهرة الحديث والواضح للنصوص العربية العصرية
            </p>
            <code className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded">
              className="font-cairo"
            </code>
          </div>

          <div className="border-b pb-4">
            <h3 className="font-tajawal text-xl font-bold text-gray-700 mb-2">خط تجوال (Tajawal)</h3>
            <p className="font-tajawal text-lg">
              هذا نص تجريبي بخط تجوال العصري والأنيق للعناوين والنصوص المميزة
            </p>
            <code className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded">
              className="font-tajawal"
            </code>
          </div>

          <div className="border-b pb-4">
            <h3 className="font-almarai text-xl font-bold text-gray-700 mb-2">خط المرعي (Almarai)</h3>
            <p className="font-almarai text-lg">
              هذا نص تجريبي بخط المرعي البسيط والواضح للنصوص الصغيرة والتفاصيل
            </p>
            <code className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded">
              className="font-almarai"
            </code>
          </div>

          <div className="pb-4">
            <h3 className="font-custom-arabic text-xl font-bold text-gray-700 mb-2">خط عربي مخصص</h3>
            <p className="font-custom-arabic text-lg">
              هذا نص تجريبي بالخط العربي المخصص (استبدل بخطك المفضل)
            </p>
            <code className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded">
              className="font-custom-arabic"
            </code>
          </div>
        </div>
      </section>

      {/* الخطوط الإنجليزية */}
      <section className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-2xl font-bold text-gray-800 mb-6">الخطوط الإنجليزية</h2>
        
        <div className="space-y-4">
          <div className="border-b pb-4">
            <h3 className="font-inter text-xl font-bold text-gray-700 mb-2">Inter Font</h3>
            <p className="font-inter text-lg">
              This is a sample text using Inter font, modern and clean for English content
            </p>
            <code className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded">
              className="font-inter"
            </code>
          </div>

          <div className="border-b pb-4">
            <h3 className="font-roboto text-xl font-bold text-gray-700 mb-2">Roboto Font</h3>
            <p className="font-roboto text-lg">
              This is a sample text using Roboto font, classic and readable for all content
            </p>
            <code className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded">
              className="font-roboto"
            </code>
          </div>

          <div className="border-b pb-4">
            <h3 className="font-poppins text-xl font-bold text-gray-700 mb-2">Poppins Font</h3>
            <p className="font-poppins text-lg">
              This is a sample text using Poppins font, elegant and modern for headings
            </p>
            <code className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded">
              className="font-poppins"
            </code>
          </div>

          <div className="pb-4">
            <h3 className="font-custom-english text-xl font-bold text-gray-700 mb-2">Custom English Font</h3>
            <p className="font-custom-english text-lg">
              This is a sample text using custom English font (replace with your preferred font)
            </p>
            <code className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded">
              className="font-custom-english"
            </code>
          </div>
        </div>
      </section>

      {/* خطوط العناوين */}
      <section className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-2xl font-bold text-gray-800 mb-6">خطوط العناوين</h2>
        
        <div className="space-y-4">
          <div className="border-b pb-4">
            <h1 className="font-main-heading text-3xl font-bold text-gray-800 mb-2">
              عنوان رئيسي بالخط المخصص
            </h1>
            <code className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded">
              className="font-main-heading"
            </code>
          </div>

          <div className="border-b pb-4">
            <h2 className="font-sub-heading text-2xl font-semibold text-gray-700 mb-2">
              عنوان فرعي بالخط المخصص
            </h2>
            <code className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded">
              className="font-sub-heading"
            </code>
          </div>

          <div className="pb-4">
            <h3 className="font-custom-heading text-xl font-medium text-gray-600 mb-2">
              عنوان مخصص بخط خاص
            </h3>
            <code className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded">
              className="font-custom-heading"
            </code>
          </div>
        </div>
      </section>

      {/* خطوط النصوص */}
      <section className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-2xl font-bold text-gray-800 mb-6">خطوط النصوص</h2>
        
        <div className="space-y-4">
          <div className="border-b pb-4">
            <p className="font-main-text text-lg text-gray-800 mb-2">
              هذا نص رئيسي بالخط المخصص للنصوص العادية في الموقع
            </p>
            <code className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded">
              className="font-main-text"
            </code>
          </div>

          <div className="border-b pb-4">
            <p className="font-small-text text-sm text-gray-600 mb-2">
              هذا نص صغير بالخط المخصص للتفاصيل والملاحظات الصغيرة
            </p>
            <code className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded">
              className="font-small-text"
            </code>
          </div>

          <div className="pb-4">
            <p className="font-custom-body text-base text-gray-700 mb-2">
              هذا نص بالخط المخصص للمحتوى (استبدل بخطك المفضل)
            </p>
            <code className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded">
              className="font-custom-body"
            </code>
          </div>
        </div>
      </section>

      {/* خطوط خاصة */}
      <section className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-2xl font-bold text-gray-800 mb-6">خطوط خاصة</h2>
        
        <div className="space-y-4">
          <div className="border-b pb-4">
            <div className="font-logo text-2xl font-bold text-primary-600 mb-2">
              شعار الموقع
            </div>
            <code className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded">
              className="font-logo"
            </code>
          </div>

          <div className="border-b pb-4">
            <div className="font-numbers text-xl text-gray-800 mb-2">
              123,456.78 ريال
            </div>
            <code className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded">
              className="font-numbers"
            </code>
          </div>

          <div className="pb-4">
            <code className="font-code text-sm bg-gray-900 text-green-400 p-2 rounded block">
              console.log('Hello World!');
            </code>
            <code className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded mt-2 inline-block">
              className="font-code"
            </code>
          </div>
        </div>
      </section>

      {/* تعليمات الاستخدام */}
      <section className="bg-blue-50 rounded-lg p-6">
        <h2 className="text-2xl font-bold text-blue-800 mb-4">كيفية الاستخدام</h2>
        <div className="space-y-3 text-blue-700">
          <p>1. ضع ملفات الخطوط في مجلد <code className="bg-blue-100 px-2 py-1 rounded">public/fonts/</code></p>
          <p>2. أضف تعريف الخط في ملف <code className="bg-blue-100 px-2 py-1 rounded">public/fonts/fonts.css</code></p>
          <p>3. استخدم الخط في المكونات باستخدام <code className="bg-blue-100 px-2 py-1 rounded">className="font-اسم-الخط"</code></p>
          <p>4. يمكنك تخصيص الخطوط في ملف <code className="bg-blue-100 px-2 py-1 rounded">tailwind.config.js</code></p>
        </div>
      </section>
    </div>
  );
};

export default FontExample;
