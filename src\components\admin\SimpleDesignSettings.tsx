'use client';

import React, { useState } from 'react';
import { useAdmin } from '@/contexts/AdminContext';
import { useToast } from '@/components/ToastContainer';
import { Save, Palette, Type, Layout } from 'lucide-react';

const SimpleDesignSettings: React.FC = () => {
  const { settings, updateSettings } = useAdmin();
  const { showSuccess, showError } = useToast();

  // إعدادات افتراضية بسيطة
  const [fontFamily, setFontFamily] = useState('Cairo, sans-serif');
  const [fontSize, setFontSize] = useState('16');
  const [primaryColor, setPrimaryColor] = useState('#2563eb');
  const [secondaryColor, setSecondaryColor] = useState('#64748b');
  const [backgroundColor, setBackgroundColor] = useState('#f8fafc');
  const [textColor, setTextColor] = useState('#1f2937');
  const [borderRadius, setBorderRadius] = useState('8');

  const handleSave = () => {
    try {
      const newDesignSettings = {
        typography: {
          fontFamily: fontFamily,
          fontSize: {
            small: `${parseInt(fontSize) - 2}px`,
            medium: `${fontSize}px`,
            large: `${parseInt(fontSize) + 4}px`,
            xlarge: `${parseInt(fontSize) + 8}px`
          },
          fontWeight: {
            light: '300',
            normal: '400',
            medium: '500',
            bold: '700'
          },
          lineHeight: '1.6',
          letterSpacing: '0.025em'
        },
        layout: {
          borderRadius: `${borderRadius}px`,
          spacing: '1rem',
          containerMaxWidth: '1200px',
          cardShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
          borderWidth: '1px'
        },
        colors: {
          primary: primaryColor,
          secondary: secondaryColor,
          accent: '#d946ef',
          success: '#10b981',
          warning: '#f59e0b',
          error: '#ef4444',
          info: '#3b82f6',
          background: backgroundColor,
          surface: '#ffffff',
          text: {
            primary: textColor,
            secondary: '#6b7280',
            muted: '#9ca3af',
            inverse: '#ffffff'
          },
          border: '#e5e7eb'
        },
        animations: {
          duration: '300ms',
          easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
          hoverScale: '1.05'
        }
      };

      updateSettings({ designSettings: newDesignSettings });
      showSuccess('تم الحفظ!', 'تم حفظ إعدادات التصميم بنجاح');
    } catch (error) {
      showError('خطأ', 'حدث خطأ أثناء حفظ الإعدادات');
    }
  };

  const fontOptions = [
    { value: 'Cairo, sans-serif', label: 'Cairo - خط عربي حديث' },
    { value: 'Tajawal, sans-serif', label: 'Tajawal - خط أنيق' },
    { value: 'Amiri, serif', label: 'Amiri - خط كلاسيكي' },
    { value: 'Almarai, sans-serif', label: 'Almarai - خط بسيط' },
    { value: 'system-ui, sans-serif', label: 'خط النظام الافتراضي' }
  ];

  const colorPresets = [
    { name: 'الأزرق الكلاسيكي', primary: '#2563eb', secondary: '#64748b' },
    { name: 'الأخضر الطبيعي', primary: '#059669', secondary: '#6b7280' },
    { name: 'البرتقالي الدافئ', primary: '#ea580c', secondary: '#64748b' },
    { name: 'الأحمر الجريء', primary: '#dc2626', secondary: '#6b7280' },
    { name: 'البنفسجي الأنيق', primary: '#7c3aed', secondary: '#64748b' }
  ];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-semibold">إعدادات التصميم والمظهر</h2>
        <button
          onClick={handleSave}
          className="px-6 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors flex items-center"
        >
          <Save className="w-4 h-4 ml-2" />
          حفظ التغييرات
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* الخطوط والنصوص */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center">
            <Type className="w-5 h-5 ml-2" />
            الخطوط والنصوص
          </h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                نوع الخط
              </label>
              <select
                value={fontFamily}
                onChange={(e) => setFontFamily(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                {fontOptions.map((font) => (
                  <option key={font.value} value={font.value}>
                    {font.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                حجم الخط الأساسي (بكسل)
              </label>
              <input
                type="number"
                value={fontSize}
                onChange={(e) => setFontSize(e.target.value)}
                min="12"
                max="24"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>

            {/* معاينة الخط */}
            <div className="border rounded-lg p-4 bg-gray-50">
              <h4 className="text-sm font-medium text-gray-700 mb-2">معاينة النص</h4>
              <div style={{ fontFamily: fontFamily, fontSize: `${fontSize}px` }}>
                <p className="font-bold text-lg mb-1">عنوان رئيسي</p>
                <p className="font-medium mb-1">عنوان فرعي</p>
                <p className="mb-1">نص عادي للمحتوى والفقرات</p>
                <p className="text-sm text-gray-600">نص صغير للملاحظات</p>
              </div>
            </div>
          </div>
        </div>

        {/* الألوان */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center">
            <Palette className="w-5 h-5 ml-2" />
            الألوان
          </h3>
          
          <div className="space-y-4">
            {/* مجموعات ألوان جاهزة */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                مجموعات ألوان جاهزة
              </label>
              <div className="grid grid-cols-1 gap-2">
                {colorPresets.map((preset) => (
                  <button
                    key={preset.name}
                    onClick={() => {
                      setPrimaryColor(preset.primary);
                      setSecondaryColor(preset.secondary);
                    }}
                    className="flex items-center justify-between p-3 border rounded-lg hover:border-gray-400 transition-colors text-right"
                  >
                    <span className="text-sm font-medium">{preset.name}</span>
                    <div className="flex space-x-2 space-x-reverse">
                      <div 
                        className="w-4 h-4 rounded-full border" 
                        style={{ backgroundColor: preset.primary }}
                      />
                      <div 
                        className="w-4 h-4 rounded-full border" 
                        style={{ backgroundColor: preset.secondary }}
                      />
                    </div>
                  </button>
                ))}
              </div>
            </div>

            {/* ألوان مخصصة */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  اللون الأساسي
                </label>
                <div className="flex items-center space-x-2 space-x-reverse">
                  <input
                    type="color"
                    value={primaryColor}
                    onChange={(e) => setPrimaryColor(e.target.value)}
                    className="w-10 h-10 rounded border border-gray-300"
                  />
                  <input
                    type="text"
                    value={primaryColor}
                    onChange={(e) => setPrimaryColor(e.target.value)}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  اللون الثانوي
                </label>
                <div className="flex items-center space-x-2 space-x-reverse">
                  <input
                    type="color"
                    value={secondaryColor}
                    onChange={(e) => setSecondaryColor(e.target.value)}
                    className="w-10 h-10 rounded border border-gray-300"
                  />
                  <input
                    type="text"
                    value={secondaryColor}
                    onChange={(e) => setSecondaryColor(e.target.value)}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  لون الخلفية
                </label>
                <div className="flex items-center space-x-2 space-x-reverse">
                  <input
                    type="color"
                    value={backgroundColor}
                    onChange={(e) => setBackgroundColor(e.target.value)}
                    className="w-10 h-10 rounded border border-gray-300"
                  />
                  <input
                    type="text"
                    value={backgroundColor}
                    onChange={(e) => setBackgroundColor(e.target.value)}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  لون النص
                </label>
                <div className="flex items-center space-x-2 space-x-reverse">
                  <input
                    type="color"
                    value={textColor}
                    onChange={(e) => setTextColor(e.target.value)}
                    className="w-10 h-10 rounded border border-gray-300"
                  />
                  <input
                    type="text"
                    value={textColor}
                    onChange={(e) => setTextColor(e.target.value)}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* التخطيط */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center">
            <Layout className="w-5 h-5 ml-2" />
            التخطيط والأشكال
          </h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                انحناء الزوايا (بكسل)
              </label>
              <input
                type="number"
                value={borderRadius}
                onChange={(e) => setBorderRadius(e.target.value)}
                min="0"
                max="20"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>

            {/* معاينة التخطيط */}
            <div className="border rounded-lg p-4 bg-gray-50">
              <h4 className="text-sm font-medium text-gray-700 mb-3">معاينة التخطيط</h4>
              <div className="space-y-3">
                <div 
                  className="bg-white p-4 border shadow-sm"
                  style={{
                    borderRadius: `${borderRadius}px`,
                    backgroundColor: backgroundColor,
                    color: textColor
                  }}
                >
                  <h5 className="font-medium mb-2">بطاقة تجريبية</h5>
                  <p className="text-sm">هذا مثال على شكل البطاقات مع الإعدادات الحالية</p>
                </div>
                <div className="flex space-x-2 space-x-reverse">
                  <button 
                    className="px-4 py-2 text-white text-sm"
                    style={{ 
                      backgroundColor: primaryColor,
                      borderRadius: `${borderRadius}px`
                    }}
                  >
                    زر أساسي
                  </button>
                  <button 
                    className="px-4 py-2 border text-sm"
                    style={{ 
                      borderRadius: `${borderRadius}px`,
                      borderColor: secondaryColor,
                      color: secondaryColor
                    }}
                  >
                    زر ثانوي
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* معاينة شاملة */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold mb-4">معاينة شاملة</h3>
          
          <div 
            className="border rounded-lg p-4"
            style={{
              fontFamily: fontFamily,
              fontSize: `${fontSize}px`,
              backgroundColor: backgroundColor,
              color: textColor,
              borderRadius: `${borderRadius}px`
            }}
          >
            <h4 className="font-bold text-lg mb-2" style={{ color: primaryColor }}>
              عنوان الموقع
            </h4>
            <p className="mb-3">
              مرحباً بكم في متجرنا الإلكتروني. نحن نقدم أفضل المنتجات بأسعار تنافسية.
            </p>
            <div className="flex space-x-2 space-x-reverse">
              <button 
                className="px-4 py-2 text-white text-sm"
                style={{ 
                  backgroundColor: primaryColor,
                  borderRadius: `${borderRadius}px`
                }}
              >
                تسوق الآن
              </button>
              <button 
                className="px-4 py-2 border text-sm"
                style={{ 
                  borderRadius: `${borderRadius}px`,
                  borderColor: secondaryColor,
                  color: secondaryColor
                }}
              >
                تعرف أكثر
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimpleDesignSettings;
