'use client';

import React, { useState, useEffect } from 'react';
import { useAdmin } from '@/contexts/AdminContext';
import { Type, Upload, Eye, Download, Trash2, Plus, Settings, Palette, FileText, Heading } from 'lucide-react';
import {
  systemFonts,
  formatFileSize,
  extractFontFamily,
  detectFontCategory,
  generateFontFaceCSS,
  validateFontFile,
  defaultFontSettings
} from '@/utils/fontUtils';

interface FontFile {
  name: string;
  path: string;
  size: number;
  type: string;
  family?: string;
}

interface FontSettings {
  headings: {
    primary: string;
    secondary: string;
    tertiary: string;
  };
  body: {
    main: string;
    small: string;
    caption: string;
  };
  special: {
    logo: string;
    numbers: string;
    buttons: string;
  };
}

const FontManagement: React.FC = () => {
  const { settings, updateSettings } = useAdmin();
  const [availableFonts, setAvailableFonts] = useState<FontFile[]>([]);
  const [activeTab, setActiveTab] = useState<'browse' | 'settings' | 'upload'>('browse');
  const [loading, setLoading] = useState(false);
  const [previewText, setPreviewText] = useState('مرحباً بكم في متجرنا الإلكتروني');

  const [fontSettings, setFontSettings] = useState<FontSettings>(
    settings?.fontSettings || defaultFontSettings
  );

  // تحميل الخطوط من المجلد
  useEffect(() => {
    const loadFonts = async () => {
      setLoading(true);
      try {
        const response = await fetch('/api/fonts');
        const data = await response.json();
        setAvailableFonts(data.fonts || []);
      } catch (error) {
        console.error('Error loading fonts:', error);
        setAvailableFonts([]);
      } finally {
        setLoading(false);
      }
    };

    loadFonts();
  }, []);

  const handleSaveFontSettings = () => {
    try {
      updateSettings({ fontSettings });
      alert('تم حفظ إعدادات الخطوط بنجاح!');
    } catch (error) {
      alert('حدث خطأ أثناء حفظ الإعدادات');
    }
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    setLoading(true);
    try {
      const formData = new FormData();
      Array.from(files).forEach(file => {
        formData.append('fonts', file);
      });

      const response = await fetch('/api/fonts', {
        method: 'POST',
        body: formData
      });

      const result = await response.json();

      if (result.success) {
        alert(`تم رفع ${result.uploaded.length} خط بنجاح!`);
        if (result.errors.length > 0) {
          alert('أخطاء:\n' + result.errors.join('\n'));
        }
        // إعادة تحميل قائمة الخطوط
        const fontsResponse = await fetch('/api/fonts');
        const fontsData = await fontsResponse.json();
        setAvailableFonts(fontsData.fonts || []);
      } else {
        alert('فشل في رفع الخطوط: ' + result.error);
      }
    } catch (error) {
      alert('حدث خطأ أثناء رفع الخطوط');
      console.error('Upload error:', error);
    } finally {
      setLoading(false);
      // إعادة تعيين input
      event.target.value = '';
    }
  };

  const handleDeleteFont = async (fontName: string) => {
    if (!confirm(`هل أنت متأكد من حذف الخط "${fontName}"؟`)) {
      return;
    }

    try {
      const response = await fetch(`/api/fonts?name=${encodeURIComponent(fontName)}`, {
        method: 'DELETE'
      });

      const result = await response.json();

      if (result.success) {
        alert(result.message);
        // إعادة تحميل قائمة الخطوط
        const fontsResponse = await fetch('/api/fonts');
        const fontsData = await fontsResponse.json();
        setAvailableFonts(fontsData.fonts || []);
      } else {
        alert('فشل في حذف الخط: ' + result.error);
      }
    } catch (error) {
      alert('حدث خطأ أثناء حذف الخط');
      console.error('Delete error:', error);
    }
  };



  const FontPreview: React.FC<{ fontFamily: string; text: string; size?: string }> = ({ 
    fontFamily, 
    text, 
    size = '1.2rem' 
  }) => (
    <div 
      style={{ 
        fontFamily: fontFamily,
        fontSize: size,
        padding: '12px',
        border: '1px solid #e5e7eb',
        borderRadius: '8px',
        backgroundColor: '#f9fafb',
        marginTop: '8px'
      }}
    >
      {text}
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3 space-x-reverse">
          <Type className="w-8 h-8 text-primary-600" />
          <div>
            <h2 className="text-2xl font-bold text-gray-800">إدارة الخطوط</h2>
            <p className="text-gray-600">تحكم في خطوط الموقع واستعرض الخطوط المتاحة</p>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 space-x-reverse">
          {[
            { id: 'browse', label: 'استعراض الخطوط', icon: Eye },
            { id: 'settings', label: 'إعدادات الخطوط', icon: Settings },
            { id: 'upload', label: 'رفع خطوط جديدة', icon: Upload },
          ].map(({ id, label, icon: Icon }) => (
            <button
              key={id}
              onClick={() => setActiveTab(id as any)}
              className={`flex items-center space-x-2 space-x-reverse py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === id
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <Icon className="w-5 h-5" />
              <span>{label}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="mt-6">
        {activeTab === 'browse' && (
          <div className="space-y-6">
            {/* Preview Text Input */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">نص المعاينة</h3>
              <input
                type="text"
                value={previewText}
                onChange={(e) => setPreviewText(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                placeholder="أدخل النص للمعاينة"
              />
            </div>

            {/* System Fonts */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <Palette className="w-5 h-5 ml-2" />
                الخطوط النظام
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {systemFonts.map((font) => (
                  <div key={font.name} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-semibold text-gray-800">{font.name}</h4>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        font.category === 'arabic' 
                          ? 'bg-blue-100 text-blue-800' 
                          : 'bg-green-100 text-green-800'
                      }`}>
                        {font.category === 'arabic' ? 'عربي' : 'إنجليزي'}
                      </span>
                    </div>
                    <FontPreview fontFamily={font.family} text={previewText} />
                    <p className="text-sm text-gray-600 mt-2">{font.preview}</p>
                  </div>
                ))}
              </div>
            </div>

            {/* Custom Fonts */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <Upload className="w-5 h-5 ml-2" />
                الخطوط المخصصة
              </h3>
              {loading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
                  <p className="text-gray-600 mt-2">جاري تحميل الخطوط...</p>
                </div>
              ) : availableFonts.length > 0 ? (
                <div className="space-y-4">
                  {availableFonts.map((font) => (
                    <div key={font.name} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                      <div className="flex items-center space-x-3 space-x-reverse">
                        <FileText className="w-5 h-5 text-gray-400" />
                        <div>
                          <h4 className="font-medium text-gray-800">{font.family || font.name}</h4>
                          <p className="text-sm text-gray-600">
                            {font.type.toUpperCase()} • {formatFileSize(font.size)}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <button className="p-2 text-gray-400 hover:text-primary-600 transition-colors">
                          <Eye className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteFont(font.name)}
                          className="p-2 text-gray-400 hover:text-red-600 transition-colors"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">لا توجد خطوط مخصصة</p>
                  <p className="text-sm text-gray-500">ارفع خطوطك المخصصة من تبويب "رفع خطوط جديدة"</p>
                </div>
              )}
            </div>
          </div>
        )}

        {activeTab === 'settings' && (
          <div className="space-y-6">
            {/* Font Settings */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-6 flex items-center">
                <Heading className="w-5 h-5 ml-2" />
                إعدادات خطوط العناوين
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {Object.entries(fontSettings.headings).map(([key, value]) => (
                  <div key={key}>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {key === 'primary' ? 'العناوين الرئيسية' : 
                       key === 'secondary' ? 'العناوين الفرعية' : 'العناوين الثالثة'}
                    </label>
                    <select
                      value={value}
                      onChange={(e) => setFontSettings(prev => ({
                        ...prev,
                        headings: { ...prev.headings, [key]: e.target.value }
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    >
                      {systemFonts.map(font => (
                        <option key={font.name} value={font.family}>{font.name}</option>
                      ))}
                    </select>
                    <FontPreview 
                      fontFamily={value} 
                      text="عنوان تجريبي" 
                      size={key === 'primary' ? '1.8rem' : key === 'secondary' ? '1.4rem' : '1.2rem'} 
                    />
                  </div>
                ))}
              </div>
            </div>

            {/* Body Text Settings */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-6 flex items-center">
                <FileText className="w-5 h-5 ml-2" />
                إعدادات خطوط النصوص
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {Object.entries(fontSettings.body).map(([key, value]) => (
                  <div key={key}>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {key === 'main' ? 'النص الرئيسي' : 
                       key === 'small' ? 'النص الصغير' : 'النص التوضيحي'}
                    </label>
                    <select
                      value={value}
                      onChange={(e) => setFontSettings(prev => ({
                        ...prev,
                        body: { ...prev.body, [key]: e.target.value }
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    >
                      {systemFonts.map(font => (
                        <option key={font.name} value={font.family}>{font.name}</option>
                      ))}
                    </select>
                    <FontPreview 
                      fontFamily={value} 
                      text="نص تجريبي للمعاينة" 
                      size={key === 'main' ? '1rem' : key === 'small' ? '0.875rem' : '0.75rem'} 
                    />
                  </div>
                ))}
              </div>
            </div>

            {/* Special Fonts Settings */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-6 flex items-center">
                <Settings className="w-5 h-5 ml-2" />
                الخطوط الخاصة
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {Object.entries(fontSettings.special).map(([key, value]) => (
                  <div key={key}>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {key === 'logo' ? 'خط الشعار' : 
                       key === 'numbers' ? 'خط الأرقام' : 'خط الأزرار'}
                    </label>
                    <select
                      value={value}
                      onChange={(e) => setFontSettings(prev => ({
                        ...prev,
                        special: { ...prev.special, [key]: e.target.value }
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    >
                      {systemFonts.map(font => (
                        <option key={font.name} value={font.family}>{font.name}</option>
                      ))}
                    </select>
                    <FontPreview 
                      fontFamily={value} 
                      text={key === 'logo' ? 'شعار الموقع' : key === 'numbers' ? '123,456 ريال' : 'زر تجريبي'} 
                    />
                  </div>
                ))}
              </div>
            </div>

            {/* Save Button */}
            <div className="flex justify-end">
              <button
                onClick={handleSaveFontSettings}
                className="bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors flex items-center space-x-2 space-x-reverse"
              >
                <Settings className="w-5 h-5" />
                <span>حفظ الإعدادات</span>
              </button>
            </div>
          </div>
        )}

        {activeTab === 'upload' && (
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-6 flex items-center">
              <Upload className="w-5 h-5 ml-2" />
              رفع خطوط جديدة
            </h3>
            
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
              <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h4 className="text-lg font-medium text-gray-800 mb-2">ارفع ملفات الخطوط</h4>
              <p className="text-gray-600 mb-4">
                اسحب وأفلت ملفات الخطوط هنا أو اضغط للاختيار
              </p>
              <p className="text-sm text-gray-500 mb-4">
                التنسيقات المدعومة: .woff2, .woff, .ttf, .otf
              </p>
              <input
                type="file"
                multiple
                accept=".woff2,.woff,.ttf,.otf"
                className="hidden"
                id="font-upload"
                onChange={handleFileUpload}
              />
              <label
                htmlFor="font-upload"
                className="bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors cursor-pointer inline-flex items-center space-x-2 space-x-reverse"
              >
                <Plus className="w-5 h-5" />
                <span>اختيار الملفات</span>
              </label>
            </div>

            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <h4 className="font-medium text-blue-800 mb-2">تعليمات الرفع:</h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• ضع ملفات الخطوط في مجلد <code className="bg-blue-100 px-1 rounded">public/fonts/</code></li>
                <li>• أضف تعريف الخط في ملف <code className="bg-blue-100 px-1 rounded">public/fonts/fonts.css</code></li>
                <li>• استخدم تنسيق WOFF2 للحصول على أفضل أداء</li>
                <li>• تأكد من أن الخط يدعم اللغة العربية إذا كنت تريد استخدامه للنصوص العربية</li>
              </ul>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default FontManagement;
