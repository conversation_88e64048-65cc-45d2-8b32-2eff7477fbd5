'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useCart } from '@/contexts/CartContext';
import { useAdmin } from '@/contexts/AdminContext';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { CustomerInfo, Order } from '@/types';
import { CreditCard, Truck, MapPin, Phone, User, Mail } from 'lucide-react';

export default function CheckoutPage() {
  const router = useRouter();
  const { items, total, clearCart } = useCart();
  const { settings } = useAdmin();
  
  const [customerInfo, setCustomerInfo] = useState<CustomerInfo>({
    name: '',
    phone: '',
    email: '',
    address: '',
    city: '',
    notes: ''
  });
  
  const [paymentMethod, setPaymentMethod] = useState<'bank_transfer' | 'cash_on_delivery'>('cash_on_delivery');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const shippingCost = total >= settings.shippingSettings.freeShippingThreshold ? 0 : settings.shippingSettings.shippingCost;
  const finalTotal = total + shippingCost;

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!customerInfo.name.trim()) {
      newErrors.name = 'الاسم مطلوب';
    }

    if (!customerInfo.phone.trim()) {
      newErrors.phone = 'رقم الهاتف مطلوب';
    } else if (!/^[0-9+\-\s()]+$/.test(customerInfo.phone)) {
      newErrors.phone = 'رقم الهاتف غير صحيح';
    }

    if (!customerInfo.address.trim()) {
      newErrors.address = 'العنوان مطلوب';
    }

    if (!customerInfo.city.trim()) {
      newErrors.city = 'المدينة مطلوبة';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Create order object
      const order: Omit<Order, 'id' | 'createdAt' | 'updatedAt'> = {
        items,
        customerInfo,
        totalAmount: finalTotal,
        status: 'pending',
        paymentMethod,
        paymentStatus: 'pending',
        shippingAddress: `${customerInfo.address}, ${customerInfo.city}`,
        notes: customerInfo.notes
      };

      // In a real app, you would send this to your backend
      console.log('Order submitted:', order);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Clear cart and redirect to success page
      clearCart();
      router.push('/order-success');
      
    } catch (error) {
      console.error('Error submitting order:', error);
      alert('حدث خطأ أثناء إرسال الطلب. يرجى المحاولة مرة أخرى.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: keyof CustomerInfo, value: string) => {
    setCustomerInfo(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  if (items.length === 0) {
    router.push('/cart');
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">إتمام الطلب</h1>
          <p className="text-gray-600">أكمل بياناتك لإتمام عملية الشراء</p>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Customer Information */}
            <div className="lg:col-span-2 space-y-6">
              {/* Personal Information */}
              <div className="bg-white rounded-lg shadow-md p-6">
                <div className="flex items-center mb-6">
                  <User className="w-6 h-6 text-primary-600 ml-3" />
                  <h2 className="text-xl font-semibold">المعلومات الشخصية</h2>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      الاسم الكامل *
                    </label>
                    <input
                      type="text"
                      value={customerInfo.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      className={`input-field ${errors.name ? 'border-red-500' : ''}`}
                      placeholder="أدخل اسمك الكامل"
                    />
                    {errors.name && (
                      <p className="text-red-500 text-sm mt-1">{errors.name}</p>
                    )}
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      رقم الهاتف *
                    </label>
                    <input
                      type="tel"
                      value={customerInfo.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      className={`input-field ${errors.phone ? 'border-red-500' : ''}`}
                      placeholder="مثال: +967712345678"
                    />
                    {errors.phone && (
                      <p className="text-red-500 text-sm mt-1">{errors.phone}</p>
                    )}
                  </div>
                  
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      البريد الإلكتروني (اختياري)
                    </label>
                    <input
                      type="email"
                      value={customerInfo.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      className="input-field"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>
              </div>

              {/* Shipping Address */}
              <div className="bg-white rounded-lg shadow-md p-6">
                <div className="flex items-center mb-6">
                  <MapPin className="w-6 h-6 text-primary-600 ml-3" />
                  <h2 className="text-xl font-semibold">عنوان التوصيل</h2>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      المدينة *
                    </label>
                    <select
                      value={customerInfo.city}
                      onChange={(e) => handleInputChange('city', e.target.value)}
                      className={`input-field ${errors.city ? 'border-red-500' : ''}`}
                    >
                      <option value="">اختر المدينة</option>
                      {settings.shippingSettings.deliveryAreas.map((city) => (
                        <option key={city} value={city}>{city}</option>
                      ))}
                    </select>
                    {errors.city && (
                      <p className="text-red-500 text-sm mt-1">{errors.city}</p>
                    )}
                  </div>
                  
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      العنوان التفصيلي *
                    </label>
                    <textarea
                      value={customerInfo.address}
                      onChange={(e) => handleInputChange('address', e.target.value)}
                      className={`input-field ${errors.address ? 'border-red-500' : ''}`}
                      rows={3}
                      placeholder="أدخل العنوان التفصيلي (الحي، الشارع، رقم المنزل...)"
                    />
                    {errors.address && (
                      <p className="text-red-500 text-sm mt-1">{errors.address}</p>
                    )}
                  </div>
                  
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      ملاحظات إضافية (اختياري)
                    </label>
                    <textarea
                      value={customerInfo.notes}
                      onChange={(e) => handleInputChange('notes', e.target.value)}
                      className="input-field"
                      rows={2}
                      placeholder="أي ملاحظات أو تعليمات خاصة للتوصيل..."
                    />
                  </div>
                </div>
              </div>

              {/* Payment Method */}
              <div className="bg-white rounded-lg shadow-md p-6">
                <div className="flex items-center mb-6">
                  <CreditCard className="w-6 h-6 text-primary-600 ml-3" />
                  <h2 className="text-xl font-semibold">طريقة الدفع</h2>
                </div>
                
                <div className="space-y-4">
                  {settings.paymentMethods.cashOnDelivery.enabled && (
                    <label className="flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50">
                      <input
                        type="radio"
                        name="paymentMethod"
                        value="cash_on_delivery"
                        checked={paymentMethod === 'cash_on_delivery'}
                        onChange={(e) => setPaymentMethod(e.target.value as 'cash_on_delivery')}
                        className="ml-3"
                      />
                      <div className="flex items-center">
                        <Truck className="w-5 h-5 text-green-600 ml-3" />
                        <div>
                          <h3 className="font-semibold">نقداً عند الاستلام</h3>
                          <p className="text-sm text-gray-600">ادفع عند استلام الطلب</p>
                        </div>
                      </div>
                    </label>
                  )}
                  
                  {settings.paymentMethods.bankTransfer.enabled &&
                   settings.paymentMethods.bankTransfer.banks.some(bank => bank.enabled) && (
                    <label className="flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50">
                      <input
                        type="radio"
                        name="paymentMethod"
                        value="bank_transfer"
                        checked={paymentMethod === 'bank_transfer'}
                        onChange={(e) => setPaymentMethod(e.target.value as 'bank_transfer')}
                        className="ml-3"
                      />
                      <div className="flex items-center">
                        <CreditCard className="w-5 h-5 text-blue-600 ml-3" />
                        <div>
                          <h3 className="font-semibold">تحويل بنكي</h3>
                          <p className="text-sm text-gray-600">
                            {settings.paymentMethods.bankTransfer.banks.filter(bank => bank.enabled).length} بنك متاح
                          </p>
                        </div>
                      </div>
                    </label>
                  )}
                </div>

                {/* Bank Transfer Details */}
                {paymentMethod === 'bank_transfer' && settings.paymentMethods.bankTransfer.enabled && (
                  <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <h3 className="font-semibold text-blue-800 mb-3">تفاصيل التحويل البنكي</h3>
                    <div className="space-y-4">
                      {settings.paymentMethods.bankTransfer.banks
                        .filter(bank => bank.enabled)
                        .map((bank, index) => (
                          <div key={bank.id} className="bg-white p-4 rounded-lg border">
                            <h4 className="font-semibold text-gray-800 mb-2">{bank.bankName}</h4>
                            <div className="space-y-2 text-sm">
                              <div className="flex justify-between">
                                <span className="text-gray-600">رقم الحساب:</span>
                                <span className="font-medium font-mono">{bank.accountNumber}</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-gray-600">اسم صاحب الحساب:</span>
                                <span className="font-medium">{bank.accountName}</span>
                              </div>
                              {bank.description && (
                                <div className="flex justify-between">
                                  <span className="text-gray-600">الوصف:</span>
                                  <span className="font-medium">{bank.description}</span>
                                </div>
                              )}
                            </div>
                          </div>
                        ))}

                      <div className="bg-white p-4 rounded-lg border border-green-200">
                        <div className="flex justify-between text-lg font-bold">
                          <span className="text-gray-600">المبلغ المطلوب:</span>
                          <span className="text-green-600">{finalTotal.toLocaleString()} ريال</span>
                        </div>
                      </div>
                    </div>

                    <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded text-sm text-yellow-800">
                      <strong>ملاحظة:</strong> يرجى إرسال صورة من إيصال التحويل عبر الواتساب على الرقم {settings.contactInfo.socialMedia.whatsapp} مع رقم الطلب.
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Order Summary */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-md p-6 sticky top-8">
                <h2 className="text-xl font-semibold mb-6">ملخص الطلب</h2>
                
                {/* Order Items */}
                <div className="space-y-3 mb-6">
                  {items.map((item) => (
                    <div key={item.id} className="flex justify-between items-center">
                      <div className="flex-1">
                        <h4 className="font-medium text-sm">{item.product.name}</h4>
                        <p className="text-xs text-gray-500">الكمية: {item.quantity}</p>
                      </div>
                      <span className="font-semibold text-sm">
                        {(item.product.price * item.quantity).toLocaleString()} ريال
                      </span>
                    </div>
                  ))}
                </div>
                
                {/* Totals */}
                <div className="space-y-3 border-t pt-4">
                  <div className="flex justify-between">
                    <span className="text-gray-600">المجموع الفرعي</span>
                    <span className="font-semibold">{total.toLocaleString()} ريال</span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-gray-600">الشحن</span>
                    <span className="font-semibold">
                      {shippingCost === 0 ? 'مجاني' : `${shippingCost.toLocaleString()} ريال`}
                    </span>
                  </div>
                  
                  <div className="flex justify-between text-lg font-bold border-t pt-3">
                    <span>المجموع الكلي</span>
                    <span className="text-primary-600">{finalTotal.toLocaleString()} ريال</span>
                  </div>
                </div>

                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full bg-primary-600 text-white py-3 rounded-lg font-semibold hover:bg-primary-700 transition-colors mt-6 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? 'جاري إرسال الطلب...' : 'تأكيد الطلب'}
                </button>
              </div>
            </div>
          </div>
        </form>
      </main>

      <Footer />
    </div>
  );
}
