'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useCart } from '@/contexts/CartContext';
import { useAdmin } from '@/contexts/AdminContext';
import { 
  ShoppingCart, 
  Search, 
  Menu, 
  X, 
  User, 
  Phone, 
  Mail,
  MapPin,
  Settings
} from 'lucide-react';

const Header: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const { itemCount } = useCart();
  const { categories, settings, isAdmin } = useAdmin();

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);
  const toggleSearch = () => setIsSearchOpen(!isSearchOpen);

  return (
    <header className="glass sticky top-0 z-50 border-b border-white/20 animate-fade-in-up">
      {/* Top Bar */}
      {settings.topBarSettings?.enabled !== false && (
        <div
          className="text-white py-2"
          style={{
            backgroundColor: settings.topBarSettings?.backgroundColor || '#2563eb',
            color: settings.topBarSettings?.textColor || '#ffffff',
            fontSize: settings.topBarSettings?.fontSize || '14px',
            fontWeight: settings.topBarSettings?.fontWeight || '400',
            padding: settings.topBarSettings?.padding || '8px 0'
          }}
        >
          <div className="container mx-auto px-4">
            <div className="flex justify-between items-center text-sm">
              <div className="flex items-center space-x-4 space-x-reverse">
                {settings.topBarSettings?.showPhone !== false && (
                  <div className="flex items-center">
                    <Phone className="w-4 h-4 ml-2" />
                    <span>{settings.contactInfo.phone}</span>
                  </div>
                )}
                {settings.topBarSettings?.showEmail !== false && (
                  <div className="flex items-center">
                    <Mail className="w-4 h-4 ml-2" />
                    <span>{settings.contactInfo.email}</span>
                  </div>
                )}
              </div>
              {settings.topBarSettings?.showShipping !== false && (
                <div className="flex items-center">
                  <MapPin className="w-4 h-4 ml-2" />
                  <span>
                    {settings.topBarSettings?.customShippingText ||
                     `التوصيل المجاني للطلبات أكثر من ${settings.shippingSettings.freeShippingThreshold} ريال`}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Main Header */}
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2 space-x-reverse nav-item animate-fade-in-left">
            <div className="gradient-bg text-white p-2 rounded-lg animate-float">
              <span className="text-xl font-bold">🛒</span>
            </div>
            <div>
              <h1 className="text-xl font-bold gradient-text">{settings.siteName}</h1>
              <p className="text-sm text-gray-600">{settings.siteDescription}</p>
            </div>
          </Link>

          {/* Search Bar - Desktop */}
          <div className="hidden md:flex flex-1 max-w-lg mx-8">
            <div className="relative w-full">
              <input
                type="text"
                placeholder="ابحث عن المنتجات..."
                className="input-field pr-10"
              />
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center space-x-4 space-x-reverse">
            {/* Search - Mobile */}
            <button
              onClick={toggleSearch}
              className="md:hidden p-2 text-gray-600 hover:text-primary-600"
            >
              <Search className="w-6 h-6" />
            </button>

            {/* Admin Panel */}
            {isAdmin && (
              <Link
                href="/admin"
                className="hidden md:flex items-center px-3 py-2 text-primary-600 hover:bg-primary-50 rounded-lg transition-all duration-300 nav-item animate-fade-in-right"
              >
                <Settings className="w-5 h-5 ml-2 animate-pulse" />
                <span>لوحة التحكم</span>
              </Link>
            )}

            {/* User Account */}
            <Link
              href="/account"
              className="hidden md:flex items-center px-3 py-2 text-gray-600 hover:text-primary-600 transition-colors"
            >
              <User className="w-5 h-5 ml-2" />
              <span>حسابي</span>
            </Link>

            {/* Cart */}
            <Link
              href="/cart"
              className="relative p-2 text-gray-600 hover:text-primary-600 transition-all duration-300 nav-item animate-fade-in-right"
            >
              <ShoppingCart className="w-6 h-6 hover:scale-110 transition-transform" />
              {itemCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center animate-bounce-gentle">
                  {itemCount}
                </span>
              )}
            </Link>

            {/* Mobile Menu Toggle */}
            <button
              onClick={toggleMenu}
              className="md:hidden p-2 text-gray-600 hover:text-primary-600"
            >
              {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Search */}
        {isSearchOpen && (
          <div className="md:hidden mt-4">
            <div className="relative">
              <input
                type="text"
                placeholder="ابحث عن المنتجات..."
                className="w-full px-4 py-2 pr-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            </div>
          </div>
        )}
      </div>

      {/* Navigation */}
      <nav className="bg-gray-50 border-t">
        <div className="container mx-auto px-4">
          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8 space-x-reverse py-3">
            <Link
              href="/"
              className="text-gray-700 hover:text-primary-600 font-medium transition-colors"
            >
              الرئيسية
            </Link>
            {categories.map((category) => (
              <Link
                key={category.id}
                href={`/category/${category.id}`}
                className="text-gray-700 hover:text-primary-600 font-medium transition-colors flex items-center"
              >
                <span className="ml-2">{category.icon}</span>
                {category.name}
              </Link>
            ))}
            <Link
              href="/contact"
              className="text-gray-700 hover:text-primary-600 font-medium transition-colors"
            >
              اتصل بنا
            </Link>
          </div>

          {/* Mobile Navigation */}
          {isMenuOpen && (
            <div className="md:hidden py-4 border-t">
              <div className="flex flex-col space-y-3">
                <Link
                  href="/"
                  className="text-gray-700 hover:text-primary-600 font-medium py-2"
                  onClick={() => setIsMenuOpen(false)}
                >
                  الرئيسية
                </Link>
                {categories.map((category) => (
                  <Link
                    key={category.id}
                    href={`/category/${category.id}`}
                    className="text-gray-700 hover:text-primary-600 font-medium py-2 flex items-center"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <span className="ml-2">{category.icon}</span>
                    {category.name}
                  </Link>
                ))}
                <Link
                  href="/contact"
                  className="text-gray-700 hover:text-primary-600 font-medium py-2"
                  onClick={() => setIsMenuOpen(false)}
                >
                  اتصل بنا
                </Link>
                {isAdmin && (
                  <Link
                    href="/admin"
                    className="text-primary-600 font-medium py-2 flex items-center"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <Settings className="w-5 h-5 ml-2" />
                    لوحة التحكم
                  </Link>
                )}
                <Link
                  href="/account"
                  className="text-gray-700 hover:text-primary-600 font-medium py-2 flex items-center"
                  onClick={() => setIsMenuOpen(false)}
                >
                  <User className="w-5 h-5 ml-2" />
                  حسابي
                </Link>
              </div>
            </div>
          )}
        </div>
      </nav>
    </header>
  );
};

export default Header;
