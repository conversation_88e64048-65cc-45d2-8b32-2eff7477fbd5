# حل المشاكل الشائعة

## 🔐 مشاكل تسجيل الدخول للإدارة

### المشكلة: "اسم المستخدم أو كلمة المرور غير صحيحة"

**الحل:**
1. تأكد من البيانات الصحيحة:
   - اسم المستخدم: `admin`
   - كلمة المرور: `admin77111`

2. إذا لم تعمل، اضغط على زر "إعادة تعيين بيانات الإدارة" في صفحة تسجيل الدخول

3. أو امسح localStorage يدوياً:
   - افتح Developer Tools (F12)
   - اذهب إلى Application > Local Storage
   - احذف جميع البيانات المتعلقة بـ admin_users

---

## 🖼️ مشاكل الصور

### المشكلة: الصور لا تظهر أو تظهر خطأ 500

**السبب:** مشكلة في الاتصال بـ via.placeholder.com

**الحل:** تم إصلاح هذه المشكلة باستخدام صور محلية بدلاً من الخدمة الخارجية.

---

## ⚠️ تحذيرات Next.js

### المشكلة: "Unsupported metadata viewport"

**الحل:** تم إصلاح هذه المشكلة بفصل viewport عن metadata في layout.tsx

---

## 🌐 مشاكل الشبكة

### المشكلة: TypeError: fetch failed

**الأسباب المحتملة:**
1. عدم توفر اتصال بالإنترنت
2. مشكلة في DNS
3. حجب الموقع من قبل الشبكة

**الحل:**
- تم استبدال جميع الصور الخارجية بصور محلية
- لا حاجة للاتصال بالإنترنت لتشغيل الموقع

---

## 🔧 مشاكل التشغيل

### المشكلة: "scripts is disabled on this system"

**الحل:**
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

أو استخدم:
```cmd
powershell -ExecutionPolicy Bypass -Command "npm run dev"
```

---

## 📱 مشاكل التصميم

### المشكلة: التصميم لا يظهر بشكل صحيح

**الحل:**
1. تأكد من تشغيل Tailwind CSS بشكل صحيح
2. امسح cache المتصفح (Ctrl+F5)
3. تأكد من وجود ملف globals.css

---

## 🛠️ أوامر مفيدة للإصلاح

### إعادة تثبيت المكتبات:
```bash
npm install
```

### مسح cache:
```bash
npm run build
```

### إعادة تشغيل الخادم:
```bash
npm run dev
```

### فحص الأخطاء:
```bash
npm run lint
```

---

## 📞 للمساعدة الإضافية

إذا استمرت المشاكل:
1. تحقق من console المتصفح (F12)
2. تحقق من terminal الذي يشغل الخادم
3. تأكد من إصدار Node.js (يُفضل 18 أو أحدث)

---

## 🔄 إعادة تعيين كاملة

إذا فشل كل شيء:
```bash
# احذف node_modules
rm -rf node_modules
rm package-lock.json

# أعد التثبيت
npm install

# شغل المشروع
npm run dev
```
