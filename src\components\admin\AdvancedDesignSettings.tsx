'use client';

import React, { useState, useEffect } from 'react';
import { useAdmin } from '@/contexts/AdminContext';
import { useToast } from '@/components/ToastContainer';
import { 
  Save, 
  Type, 
  Palette, 
  Image as ImageIcon,
  Upload,
  Trash2,
  Plus,
  Download
} from 'lucide-react';

const AdvancedDesignSettings: React.FC = () => {
  const { settings, updateSettings } = useAdmin();
  const { showSuccess, showError } = useToast();

  const [activeTab, setActiveTab] = useState<'typography' | 'colors' | 'slider'>('typography');

  // إعدادات افتراضية
  const defaultSettings = {
    typography: {
      headings: {
        fontFamily: 'Cairo, sans-serif',
        fontSize: '2rem',
        fontWeight: '700',
        lineHeight: '1.2',
        letterSpacing: '0.025em'
      },
      subheadings: {
        fontFamily: 'Cairo, sans-serif',
        fontSize: '1.5rem',
        fontWeight: '600',
        lineHeight: '1.3',
        letterSpacing: '0.025em'
      },
      paragraphs: {
        fontFamily: 'Cairo, sans-serif',
        fontSize: '1rem',
        fontWeight: '400',
        lineHeight: '1.6',
        letterSpacing: '0.025em'
      },
      notes: {
        fontFamily: 'Cairo, sans-serif',
        fontSize: '0.875rem',
        fontWeight: '400',
        lineHeight: '1.5',
        letterSpacing: '0.025em'
      }
    },
    slider: {
      enabled: true,
      height: '400px',
      autoplay: true,
      autoplaySpeed: 5000,
      showDots: true,
      showArrows: true,
      images: []
    },
    colors: {
      primary: '#2563eb',
      secondary: '#64748b',
      accent: '#d946ef',
      success: '#10b981',
      warning: '#f59e0b',
      error: '#ef4444',
      info: '#3b82f6',
      background: '#f8fafc',
      surface: '#ffffff',
      text: {
        primary: '#1f2937',
        secondary: '#6b7280',
        muted: '#9ca3af',
        inverse: '#ffffff'
      },
      border: '#e5e7eb'
    }
  };

  const [designSettings, setDesignSettings] = useState(
    settings?.designSettings || defaultSettings
  );

  const [customFonts, setCustomFonts] = useState<any[]>([]);
  const [loadingFonts, setLoadingFonts] = useState(false);

  // تحميل الخطوط المخصصة من المجلد
  useEffect(() => {
    const loadCustomFonts = async () => {
      setLoadingFonts(true);
      try {
        const response = await fetch('/api/fonts');
        const data = await response.json();
        if (data.fonts) {
          const customFontOptions = data.fonts.map((font: any) => ({
            value: `'${font.family}', sans-serif`,
            label: `${font.family} - خط مخصص`,
            category: 'مخصص',
            file: font.name,
            size: font.size,
            type: font.type
          }));
          setCustomFonts(customFontOptions);
          console.log('تم تحميل الخطوط المخصصة:', customFontOptions);
        } else {
          console.log('لا توجد خطوط في المجلد');
        }
      } catch (error) {
        console.error('Error loading custom fonts:', error);
      } finally {
        setLoadingFonts(false);
      }
    };

    loadCustomFonts();
  }, []);

  // الخطوط الأساسية (ثابتة)
  const baseFontOptions = [
    // خطوط عربية
    { value: 'Cairo, sans-serif', label: 'Cairo - خط عربي حديث', category: 'عربي' },
    { value: 'Tajawal, sans-serif', label: 'Tajawal - خط أنيق', category: 'عربي' },
    { value: 'Amiri, serif', label: 'Amiri - خط كلاسيكي', category: 'عربي' },
    { value: 'Almarai, sans-serif', label: 'Almarai - خط بسيط', category: 'عربي' },
    { value: 'IBM Plex Sans Arabic, sans-serif', label: 'IBM Plex Arabic - احترافي', category: 'عربي' },
    { value: 'Noto Sans Arabic, sans-serif', label: 'Noto Sans Arabic - Google', category: 'عربي' },
    { value: 'Markazi Text, serif', label: 'Markazi Text - للعناوين', category: 'عربي' },
    { value: 'Reem Kufi, sans-serif', label: 'Reem Kufi - عصري', category: 'عربي' },
    { value: 'Scheherazade New, serif', label: 'Scheherazade - تقليدي', category: 'عربي' },
    { value: 'Lateef, serif', label: 'Lateef - خط نسخ', category: 'عربي' },

    // خطوط إنجليزية
    { value: 'Inter, sans-serif', label: 'Inter - حديث ونظيف', category: 'إنجليزي' },
    { value: 'Roboto, sans-serif', label: 'Roboto - Google الشهير', category: 'إنجليزي' },
    { value: 'Open Sans, sans-serif', label: 'Open Sans - سهل القراءة', category: 'إنجليزي' },
    { value: 'Lato, sans-serif', label: 'Lato - أنيق ومرن', category: 'إنجليزي' },
    { value: 'Montserrat, sans-serif', label: 'Montserrat - للعناوين', category: 'إنجليزي' },
    { value: 'Poppins, sans-serif', label: 'Poppins - عصري', category: 'إنجليزي' },
    { value: 'Source Sans Pro, sans-serif', label: 'Source Sans Pro - Adobe', category: 'إنجليزي' },
    { value: 'Nunito, sans-serif', label: 'Nunito - ودود', category: 'إنجليزي' },

    // خطوط النظام
    { value: 'system-ui, sans-serif', label: 'خط النظام الافتراضي', category: 'النظام' },
    { value: '-apple-system, BlinkMacSystemFont, sans-serif', label: 'خط Apple النظام', category: 'النظام' },
    { value: 'Segoe UI, Tahoma, sans-serif', label: 'خط Windows النظام', category: 'النظام' }
  ];

  // دمج الخطوط الأساسية مع المخصصة (ديناميكي)
  const allFontOptions = [...baseFontOptions, ...customFonts];

  const handleSave = () => {
    try {
      updateSettings({ designSettings });
      showSuccess('تم الحفظ!', 'تم حفظ إعدادات التصميم بنجاح');
    } catch (error) {
      showError('خطأ', 'حدث خطأ أثناء حفظ الإعدادات');
    }
  };

  const updateTypography = (type: string, field: string, value: string) => {
    setDesignSettings(prev => ({
      ...prev,
      typography: {
        ...prev.typography,
        [type]: {
          ...prev.typography[type as keyof typeof prev.typography],
          [field]: value
        }
      }
    }));
  };

  const updateSlider = (field: string, value: any) => {
    console.log(`🔧 تحديث إعداد السلايدر: ${field} = ${value}`);

    setDesignSettings(prev => {
      const defaultSlider = {
        enabled: false,
        height: '400px',
        autoplay: true,
        autoplaySpeed: 3000,
        showDots: true,
        showArrows: true,
        pauseOnHover: true,
        transition: 'slide',
        images: []
      };

      const newSliderSettings = {
        ...defaultSlider,
        ...prev.slider,
        [field]: value
      };

      console.log('📊 إعدادات السلايدر الجديدة:', newSliderSettings);

      return {
        ...prev,
        slider: newSliderSettings
      };
    });

    // حفظ تلقائي للتغييرات
    setTimeout(() => {
      try {
        updateSettings({ designSettings: { ...designSettings, slider: { ...designSettings.slider, [field]: value } } });
        console.log('✅ تم حفظ إعداد السلايدر تلقائياً');
      } catch (error) {
        console.error('❌ خطأ في الحفظ التلقائي:', error);
      }
    }, 100);
  };

  const reloadCustomFonts = async () => {
    setLoadingFonts(true);
    try {
      const response = await fetch('/api/fonts');
      const data = await response.json();
      if (data.fonts) {
        const customFontOptions = data.fonts.map((font: any) => ({
          value: `'${font.family}', sans-serif`,
          label: `${font.family} - خط مخصص`,
          category: 'مخصص',
          file: font.name,
          size: font.size,
          type: font.type
        }));
        setCustomFonts(customFontOptions);
        console.log('إعادة تحميل الخطوط المخصصة:', customFontOptions);
      } else {
        console.log('لا توجد خطوط في المجلد عند إعادة التحميل');
      }
    } catch (error) {
      console.error('Error reloading custom fonts:', error);
    } finally {
      setLoadingFonts(false);
    }
  };

  const updateColors = (field: string, value: string) => {
    setDesignSettings(prev => {
      const newColors = { ...prev.colors };

      // التعامل مع الحقول المتداخلة للنص
      if (field.startsWith('text.')) {
        const textField = field.split('.')[1];
        newColors.text = {
          ...newColors.text,
          [textField]: value
        };
      } else {
        newColors[field] = value;
      }

      return {
        ...prev,
        colors: newColors
      };
    });
  };



  const addSliderImage = () => {
    const newImage = {
      id: Date.now().toString(),
      url: '/images/products/slider/welcome-banner.svg',
      title: 'عنوان جديد',
      description: 'وصف الصورة',
      link: ''
    };
    
    setDesignSettings(prev => {
      const defaultSlider = {
        enabled: true,
        height: '400px',
        autoplay: true,
        autoplaySpeed: 3000,
        showDots: true,
        showArrows: true,
        pauseOnHover: true,
        transition: 'slide',
        images: []
      };

      return {
        ...prev,
        slider: {
          ...defaultSlider,
          ...prev.slider,
          images: [...(prev.slider?.images || []), newImage]
        }
      };
    });
  };

  const updateSliderImage = (id: string, field: string, value: string) => {
    setDesignSettings(prev => {
      const defaultSlider = {
        enabled: false,
        height: '400px',
        autoplay: true,
        autoplaySpeed: 3000,
        showDots: true,
        showArrows: true,
        pauseOnHover: true,
        transition: 'slide',
        images: []
      };

      return {
        ...prev,
        slider: {
          ...defaultSlider,
          ...prev.slider,
          images: (prev.slider?.images || []).map(img =>
            img.id === id ? { ...img, [field]: value } : img
          )
        }
      };
    });
  };

  const removeSliderImage = (id: string) => {
    setDesignSettings(prev => {
      const defaultSlider = {
        enabled: true,
        height: '400px',
        autoplay: true,
        autoplaySpeed: 3000,
        showDots: true,
        showArrows: true,
        pauseOnHover: true,
        transition: 'slide',
        images: []
      };

      return {
        ...prev,
        slider: {
          ...defaultSlider,
          ...prev.slider,
          images: (prev.slider?.images || []).filter(img => img.id !== id)
        }
      };
    });
  };

  const downloadFont = (fontFamily: string) => {
    // إنشاء رابط Google Fonts
    const fontName = fontFamily.split(',')[0].replace(/\s+/g, '+');
    const googleFontsUrl = `https://fonts.googleapis.com/css2?family=${fontName}:wght@300;400;500;600;700&display=swap`;
    
    // إضافة الخط إلى الصفحة
    const existingLink = document.querySelector(`link[href*="${fontName}"]`);
    if (!existingLink) {
      const link = document.createElement('link');
      link.href = googleFontsUrl;
      link.rel = 'stylesheet';
      document.head.appendChild(link);
      showSuccess('تم تحميل الخط!', `تم تحميل خط ${fontFamily.split(',')[0]} بنجاح`);
    } else {
      showSuccess('الخط محمل مسبقاً', `خط ${fontFamily.split(',')[0]} متاح بالفعل`);
    }
  };

  const typographyTypes = [
    { key: 'headings', label: 'العناوين الرئيسية', description: 'للعناوين الكبيرة والمهمة' },
    { key: 'subheadings', label: 'العناوين الفرعية', description: 'للعناوين الثانوية' },
    { key: 'paragraphs', label: 'نص الفقرات', description: 'للنصوص العادية والمحتوى' },
    { key: 'notes', label: 'نص الملاحظات', description: 'للنصوص الصغيرة والتفاصيل' }
  ];

  return (
    <div className="space-y-4 md:space-y-6 w-full min-h-0">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <h2 className="text-xl md:text-2xl font-semibold break-words">إعدادات التصميم المتقدمة</h2>
        <button
          onClick={handleSave}
          className="px-4 py-2 md:px-6 md:py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors flex items-center text-sm md:text-base whitespace-nowrap"
        >
          <Save className="w-4 h-4 ml-1 md:ml-2" />
          حفظ التغييرات
        </button>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-lg shadow-md">
        <div className="border-b">
          <div className="flex flex-wrap space-x-0 space-x-reverse">
            {[
              { id: 'typography', name: 'الخطوط والنصوص', icon: Type },
              { id: 'colors', name: 'الألوان', icon: Palette },
              { id: 'slider', name: 'السلايدر', icon: ImageIcon }
            ].map(({ id, name, icon: Icon }) => (
              <button
                key={id}
                onClick={() => setActiveTab(id as any)}
                className={`flex items-center px-4 py-3 border-b-2 transition-colors text-sm md:text-base md:px-6 md:py-4 ${
                  activeTab === id
                    ? 'border-primary-500 text-primary-600 bg-primary-50'
                    : 'border-transparent text-gray-600 hover:text-gray-800 hover:bg-gray-50'
                }`}
              >
                <Icon className="w-4 h-4 ml-1 md:w-5 md:h-5 md:ml-2" />
                <span className="whitespace-nowrap">{name}</span>
              </button>
            ))}
          </div>
        </div>

        <div className="p-4 md:p-6 min-h-0 overflow-auto">
          {/* Typography Tab */}
          {activeTab === 'typography' && (
            <div className="space-y-8">
              <div className="text-center">
                <h3 className="text-lg font-semibold mb-2">التحكم المفصل في الخطوط</h3>
                <p className="text-gray-600">خصص كل نوع من النصوص بشكل منفصل</p>
              </div>

              {typographyTypes.map(({ key, label, description }) => (
                <div key={key} className="bg-gray-50 rounded-lg p-4 md:p-6">
                  <div className="mb-4">
                    <h4 className="text-base md:text-lg font-semibold text-gray-800 break-words">{label}</h4>
                    <p className="text-xs md:text-sm text-gray-600 break-words">{description}</p>
                  </div>

                  <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4">
                    {/* Font Family */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        نوع الخط
                      </label>
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <select
                          value={designSettings.typography[key as keyof typeof designSettings.typography]?.fontFamily || 'Cairo, sans-serif'}
                          onChange={(e) => updateTypography(key, 'fontFamily', e.target.value)}
                          className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm"
                        >
                          {/* تجميع الخطوط حسب الفئة */}
                          <optgroup label="الخطوط العربية">
                            {allFontOptions.filter(font => font.category === 'عربي').map((font) => (
                              <option key={font.value} value={font.value}>
                                {font.label}
                              </option>
                            ))}
                          </optgroup>

                          <optgroup label="الخطوط الإنجليزية">
                            {allFontOptions.filter(font => font.category === 'إنجليزي').map((font) => (
                              <option key={font.value} value={font.value}>
                                {font.label}
                              </option>
                            ))}
                          </optgroup>

                          <optgroup label="خطوط النظام">
                            {allFontOptions.filter(font => font.category === 'النظام').map((font) => (
                              <option key={font.value} value={font.value}>
                                {font.label}
                              </option>
                            ))}
                          </optgroup>

                          {customFonts.length > 0 && (
                            <optgroup label="الخطوط المخصصة">
                              {customFonts.map((font) => (
                                <option key={font.value} value={font.value}>
                                  {font.label}
                                </option>
                              ))}
                            </optgroup>
                          )}
                        </select>
                        <button
                          onClick={() => downloadFont(designSettings.typography[key as keyof typeof designSettings.typography]?.fontFamily || 'Cairo, sans-serif')}
                          className="p-2 text-primary-600 hover:text-primary-700 border border-primary-200 rounded-lg hover:bg-primary-50"
                          title="تحميل الخط"
                        >
                          <Download className="w-4 h-4" />
                        </button>
                      </div>
                    </div>

                    {/* Font Size */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        حجم الخط
                      </label>
                      <input
                        type="text"
                        value={designSettings.typography[key as keyof typeof designSettings.typography]?.fontSize || '1rem'}
                        onChange={(e) => updateTypography(key, 'fontSize', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm"
                        placeholder="1rem"
                      />
                    </div>

                    {/* Font Weight */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        وزن الخط
                      </label>
                      <select
                        value={designSettings.typography[key as keyof typeof designSettings.typography]?.fontWeight || '400'}
                        onChange={(e) => updateTypography(key, 'fontWeight', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm"
                      >
                        <option value="300">خفيف (300)</option>
                        <option value="400">عادي (400)</option>
                        <option value="500">متوسط (500)</option>
                        <option value="600">نصف عريض (600)</option>
                        <option value="700">عريض (700)</option>
                        <option value="800">عريض جداً (800)</option>
                      </select>
                    </div>

                    {/* Line Height */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        ارتفاع السطر
                      </label>
                      <input
                        type="text"
                        value={designSettings.typography[key as keyof typeof designSettings.typography]?.lineHeight || '1.6'}
                        onChange={(e) => updateTypography(key, 'lineHeight', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm"
                        placeholder="1.6"
                      />
                    </div>

                    {/* Letter Spacing */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        تباعد الأحرف
                      </label>
                      <input
                        type="text"
                        value={designSettings.typography[key as keyof typeof designSettings.typography]?.letterSpacing || '0.025em'}
                        onChange={(e) => updateTypography(key, 'letterSpacing', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm"
                        placeholder="0.025em"
                      />
                    </div>
                  </div>

                  {/* Preview */}
                  <div className="mt-4 p-4 bg-white rounded-lg border">
                    <h5 className="text-sm font-medium text-gray-700 mb-2">معاينة {label}</h5>
                    <div
                      style={{
                        fontFamily: designSettings.typography[key as keyof typeof designSettings.typography]?.fontFamily || 'Cairo, sans-serif',
                        fontSize: designSettings.typography[key as keyof typeof designSettings.typography]?.fontSize || '1rem',
                        fontWeight: designSettings.typography[key as keyof typeof designSettings.typography]?.fontWeight || '400',
                        lineHeight: designSettings.typography[key as keyof typeof designSettings.typography]?.lineHeight || '1.6',
                        letterSpacing: designSettings.typography[key as keyof typeof designSettings.typography]?.letterSpacing || '0.025em'
                      }}
                    >
                      {key === 'headings' && 'هذا مثال على العنوان الرئيسي'}
                      {key === 'subheadings' && 'هذا مثال على العنوان الفرعي'}
                      {key === 'paragraphs' && 'هذا مثال على نص الفقرة العادية التي تحتوي على محتوى مفصل ومعلومات مهمة للقارئ'}
                      {key === 'notes' && 'هذا مثال على نص الملاحظات والتفاصيل الصغيرة'}
                    </div>
                  </div>
                </div>
              ))}

              {/* Custom Fonts Section */}
              <div className="bg-blue-50 rounded-lg p-6 border border-blue-200">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h4 className="text-lg font-semibold text-blue-800 flex items-center">
                      <Upload className="w-5 h-5 ml-2" />
                      الخطوط المخصصة من المجلد
                    </h4>
                    <p className="text-blue-600 text-sm">
                      الخطوط المتاحة في مجلد public/fonts/ ({customFonts.length} خط)
                    </p>
                  </div>
                  <div className="flex items-center space-x-2 space-x-reverse">
                    {loadingFonts && (
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                    )}
                    <button
                      onClick={reloadCustomFonts}
                      disabled={loadingFonts}
                      className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors disabled:opacity-50"
                    >
                      إعادة تحميل
                    </button>
                  </div>
                </div>

                {customFonts.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {customFonts.map((font, index) => (
                      <div key={index} className="bg-white rounded-lg p-4 border border-blue-200">
                        <div className="flex items-center justify-between mb-2">
                          <h5 className="font-semibold text-gray-800">{font.label.split(' - ')[0]}</h5>
                          <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                            {font.category}
                          </span>
                        </div>

                        {font.file && (
                          <div className="text-xs text-gray-600 mb-2">
                            <div>ملف: {font.file}</div>
                            {font.size && <div>حجم: {Math.round(font.size / 1024)} KB</div>}
                            {font.type && <div>نوع: {font.type.toUpperCase()}</div>}
                          </div>
                        )}

                        <div
                          className="text-sm p-2 bg-gray-50 rounded border"
                          style={{ fontFamily: font.value }}
                        >
                          مرحباً بكم في متجرنا الإلكتروني
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Upload className="w-12 h-12 text-blue-400 mx-auto mb-4" />
                    <p className="text-blue-600">لا توجد خطوط مخصصة</p>
                    <p className="text-blue-500 text-sm">
                      ضع ملفات الخطوط في مجلد <code className="bg-blue-100 px-1 rounded">public/fonts/</code>
                    </p>
                  </div>
                )}

                <div className="mt-4 p-3 bg-blue-100 rounded-lg">
                  <h5 className="font-medium text-blue-800 mb-2">كيفية إضافة خط جديد:</h5>
                  <ol className="text-sm text-blue-700 space-y-1">
                    <li>1. ضع ملف الخط في مجلد <code className="bg-blue-200 px-1 rounded">public/fonts/</code></li>
                    <li>2. أضف تعريف الخط في <code className="bg-blue-200 px-1 rounded">public/fonts/fonts.css</code></li>
                    <li>3. أعد تحميل هذه الصفحة لرؤية الخط الجديد</li>
                    <li>4. اختر الخط من قائمة "نوع الخط" أعلاه</li>
                  </ol>
                </div>
              </div>
            </div>
          )}

          {/* Colors Tab */}
          {activeTab === 'colors' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold text-gray-800">إعدادات الألوان</h3>
                  <p className="text-gray-600">تخصيص ألوان الموقع والعناصر المختلفة</p>
                </div>
              </div>

              {/* Primary Colors */}
              <div className="bg-white rounded-lg shadow-md p-6">
                <h4 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                  <Palette className="w-5 h-5 ml-2" />
                  الألوان الأساسية
                </h4>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {/* Primary Color */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      اللون الأساسي
                    </label>
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <input
                        type="color"
                        value={designSettings.colors?.primary || '#2563eb'}
                        onChange={(e) => updateColors('primary', e.target.value)}
                        className="w-12 h-10 border border-gray-300 rounded-lg cursor-pointer"
                      />
                      <input
                        type="text"
                        value={designSettings.colors?.primary || '#2563eb'}
                        onChange={(e) => updateColors('primary', e.target.value)}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm font-mono"
                        placeholder="#2563eb"
                      />
                    </div>
                    <p className="text-xs text-gray-500 mt-1">للأزرار والروابط والعناصر المهمة</p>
                  </div>

                  {/* Secondary Color */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      اللون الثانوي
                    </label>
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <input
                        type="color"
                        value={designSettings.colors?.secondary || '#64748b'}
                        onChange={(e) => updateColors('secondary', e.target.value)}
                        className="w-12 h-10 border border-gray-300 rounded-lg cursor-pointer"
                      />
                      <input
                        type="text"
                        value={designSettings.colors?.secondary || '#64748b'}
                        onChange={(e) => updateColors('secondary', e.target.value)}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm font-mono"
                        placeholder="#64748b"
                      />
                    </div>
                    <p className="text-xs text-gray-500 mt-1">للعناصر الثانوية والحدود</p>
                  </div>

                  {/* Background Color */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      لون الخلفية
                    </label>
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <input
                        type="color"
                        value={designSettings.colors?.background || '#f8fafc'}
                        onChange={(e) => updateColors('background', e.target.value)}
                        className="w-12 h-10 border border-gray-300 rounded-lg cursor-pointer"
                      />
                      <input
                        type="text"
                        value={designSettings.colors?.background || '#f8fafc'}
                        onChange={(e) => updateColors('background', e.target.value)}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm font-mono"
                        placeholder="#f8fafc"
                      />
                    </div>
                    <p className="text-xs text-gray-500 mt-1">لخلفية الصفحات والأقسام</p>
                  </div>

                  {/* Text Color */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      لون النص الأساسي
                    </label>
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <input
                        type="color"
                        value={designSettings.colors?.text?.primary || '#1f2937'}
                        onChange={(e) => updateColors('text.primary', e.target.value)}
                        className="w-12 h-10 border border-gray-300 rounded-lg cursor-pointer"
                      />
                      <input
                        type="text"
                        value={designSettings.colors?.text?.primary || '#1f2937'}
                        onChange={(e) => updateColors('text.primary', e.target.value)}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm font-mono"
                        placeholder="#1f2937"
                      />
                    </div>
                    <p className="text-xs text-gray-500 mt-1">للنصوص والعناوين الرئيسية</p>
                  </div>
                </div>
              </div>

              {/* Additional Colors */}
              <div className="bg-white rounded-lg shadow-md p-6">
                <h4 className="text-lg font-semibold text-gray-800 mb-4">ألوان إضافية</h4>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {/* Success Color */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      لون النجاح
                    </label>
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <input
                        type="color"
                        value={designSettings.colors?.success || '#10b981'}
                        onChange={(e) => updateColors('success', e.target.value)}
                        className="w-12 h-10 border border-gray-300 rounded-lg cursor-pointer"
                      />
                      <input
                        type="text"
                        value={designSettings.colors?.success || '#10b981'}
                        onChange={(e) => updateColors('success', e.target.value)}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm font-mono"
                        placeholder="#10b981"
                      />
                    </div>
                    <p className="text-xs text-gray-500 mt-1">للرسائل الإيجابية والتأكيدات</p>
                  </div>

                  {/* Warning Color */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      لون التحذير
                    </label>
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <input
                        type="color"
                        value={designSettings.colors?.warning || '#f59e0b'}
                        onChange={(e) => updateColors('warning', e.target.value)}
                        className="w-12 h-10 border border-gray-300 rounded-lg cursor-pointer"
                      />
                      <input
                        type="text"
                        value={designSettings.colors?.warning || '#f59e0b'}
                        onChange={(e) => updateColors('warning', e.target.value)}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm font-mono"
                        placeholder="#f59e0b"
                      />
                    </div>
                    <p className="text-xs text-gray-500 mt-1">للتحذيرات والتنبيهات</p>
                  </div>

                  {/* Error Color */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      لون الخطأ
                    </label>
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <input
                        type="color"
                        value={designSettings.colors?.error || '#ef4444'}
                        onChange={(e) => updateColors('error', e.target.value)}
                        className="w-12 h-10 border border-gray-300 rounded-lg cursor-pointer"
                      />
                      <input
                        type="text"
                        value={designSettings.colors?.error || '#ef4444'}
                        onChange={(e) => updateColors('error', e.target.value)}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm font-mono"
                        placeholder="#ef4444"
                      />
                    </div>
                    <p className="text-xs text-gray-500 mt-1">لرسائل الخطأ والمشاكل</p>
                  </div>

                  {/* Text Secondary Color */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      لون النص الثانوي
                    </label>
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <input
                        type="color"
                        value={designSettings.colors?.text?.secondary || '#6b7280'}
                        onChange={(e) => updateColors('text.secondary', e.target.value)}
                        className="w-12 h-10 border border-gray-300 rounded-lg cursor-pointer"
                      />
                      <input
                        type="text"
                        value={designSettings.colors?.text?.secondary || '#6b7280'}
                        onChange={(e) => updateColors('text.secondary', e.target.value)}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm font-mono"
                        placeholder="#6b7280"
                      />
                    </div>
                    <p className="text-xs text-gray-500 mt-1">للنصوص الفرعية والوصف</p>
                  </div>

                  {/* Border Color */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      لون الحدود
                    </label>
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <input
                        type="color"
                        value={designSettings.colors?.border || '#e5e7eb'}
                        onChange={(e) => updateColors('border', e.target.value)}
                        className="w-12 h-10 border border-gray-300 rounded-lg cursor-pointer"
                      />
                      <input
                        type="text"
                        value={designSettings.colors?.border || '#e5e7eb'}
                        onChange={(e) => updateColors('border', e.target.value)}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm font-mono"
                        placeholder="#e5e7eb"
                      />
                    </div>
                    <p className="text-xs text-gray-500 mt-1">للحدود والفواصل</p>
                  </div>

                  {/* Surface Color */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      لون السطح
                    </label>
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <input
                        type="color"
                        value={designSettings.colors?.surface || '#ffffff'}
                        onChange={(e) => updateColors('surface', e.target.value)}
                        className="w-12 h-10 border border-gray-300 rounded-lg cursor-pointer"
                      />
                      <input
                        type="text"
                        value={designSettings.colors?.surface || '#ffffff'}
                        onChange={(e) => updateColors('surface', e.target.value)}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm font-mono"
                        placeholder="#ffffff"
                      />
                    </div>
                    <p className="text-xs text-gray-500 mt-1">للبطاقات والعناصر المرتفعة</p>
                  </div>
                </div>
              </div>

              {/* Color Preview */}
              <div className="bg-white rounded-lg shadow-md p-6">
                <h4 className="text-lg font-semibold text-gray-800 mb-4">معاينة الألوان</h4>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Buttons Preview */}
                  <div>
                    <h5 className="font-medium text-gray-700 mb-3">الأزرار</h5>
                    <div className="space-y-3">
                      <button
                        style={{ backgroundColor: designSettings.colors?.primary || '#2563eb' }}
                        className="px-4 py-2 text-white rounded-lg font-medium"
                      >
                        زر أساسي
                      </button>
                      <button
                        style={{
                          backgroundColor: designSettings.colors?.secondary || '#64748b',
                          color: 'white'
                        }}
                        className="px-4 py-2 rounded-lg font-medium ml-2"
                      >
                        زر ثانوي
                      </button>
                      <button
                        style={{ backgroundColor: designSettings.colors?.success || '#10b981' }}
                        className="px-4 py-2 text-white rounded-lg font-medium ml-2"
                      >
                        زر نجاح
                      </button>
                    </div>
                  </div>

                  {/* Text Preview */}
                  <div>
                    <h5 className="font-medium text-gray-700 mb-3">النصوص</h5>
                    <div className="space-y-2">
                      <h3
                        style={{ color: designSettings.colors?.text?.primary || '#1f2937' }}
                        className="text-xl font-bold"
                      >
                        عنوان رئيسي
                      </h3>
                      <p
                        style={{ color: designSettings.colors?.text?.primary || '#1f2937' }}
                        className="text-base"
                      >
                        هذا نص تجريبي لمعاينة لون النص المختار
                      </p>
                      <p
                        style={{ color: designSettings.colors?.text?.secondary || '#6b7280' }}
                        className="text-sm"
                      >
                        نص ثانوي بلون مختلف
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Color Presets */}
              <div className="bg-white rounded-lg shadow-md p-6">
                <h4 className="text-lg font-semibold text-gray-800 mb-4">قوالب الألوان الجاهزة</h4>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {[
                    {
                      name: 'الأزرق الكلاسيكي',
                      colors: {
                        primary: '#2563eb',
                        secondary: '#64748b',
                        background: '#f8fafc',
                        'text.primary': '#1f2937',
                        success: '#10b981',
                        warning: '#f59e0b',
                        error: '#ef4444'
                      }
                    },
                    {
                      name: 'الأخضر الطبيعي',
                      colors: {
                        primary: '#059669',
                        secondary: '#6b7280',
                        background: '#f0fdf4',
                        'text.primary': '#1f2937',
                        success: '#10b981',
                        warning: '#f59e0b',
                        error: '#ef4444'
                      }
                    },
                    {
                      name: 'البرتقالي الدافئ',
                      colors: {
                        primary: '#ea580c',
                        secondary: '#6b7280',
                        background: '#fff7ed',
                        'text.primary': '#1f2937',
                        success: '#10b981',
                        warning: '#f59e0b',
                        error: '#ef4444'
                      }
                    },
                    {
                      name: 'البنفسجي الأنيق',
                      colors: {
                        primary: '#7c3aed',
                        secondary: '#6b7280',
                        background: '#faf5ff',
                        'text.primary': '#1f2937',
                        success: '#10b981',
                        warning: '#f59e0b',
                        error: '#ef4444'
                      }
                    },
                    {
                      name: 'الأحمر القوي',
                      colors: {
                        primary: '#dc2626',
                        secondary: '#6b7280',
                        background: '#fef2f2',
                        'text.primary': '#1f2937',
                        success: '#10b981',
                        warning: '#f59e0b',
                        error: '#ef4444'
                      }
                    },
                    {
                      name: 'الرمادي المحايد',
                      colors: {
                        primary: '#374151',
                        secondary: '#9ca3af',
                        background: '#f9fafb',
                        'text.primary': '#111827',
                        success: '#10b981',
                        warning: '#f59e0b',
                        error: '#ef4444'
                      }
                    }
                  ].map((preset, index) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                      <h5 className="font-medium text-gray-800 mb-3">{preset.name}</h5>
                      <div className="flex space-x-2 space-x-reverse mb-3">
                        {Object.entries(preset.colors).map(([key, color]) => (
                          <div
                            key={key}
                            className="w-8 h-8 rounded-full border border-gray-300"
                            style={{ backgroundColor: color }}
                            title={`${key}: ${color}`}
                          ></div>
                        ))}
                      </div>
                      <button
                        onClick={() => {
                          Object.entries(preset.colors).forEach(([key, color]) => {
                            updateColors(key, color);
                          });
                        }}
                        className="w-full px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors text-sm"
                      >
                        تطبيق هذا القالب
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Slider Tab */}
          {activeTab === 'slider' && (
            <div className="space-y-6">
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div>
                  <h3 className="text-base md:text-lg font-semibold break-words">إعدادات السلايدر</h3>
                  <p className="text-sm md:text-base text-gray-600 break-words">تحكم في السلايدر المتحرك في الصفحة الرئيسية</p>
                </div>
                <div className="flex flex-wrap gap-2">
                  <button
                    onClick={addSliderImage}
                    className="px-3 py-2 md:px-4 md:py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center text-sm md:text-base whitespace-nowrap"
                  >
                    <Plus className="w-4 h-4 ml-1 md:ml-2" />
                    إضافة صورة
                  </button>
                  <button
                    onClick={() => {
                      if (confirm('هل تريد حذف جميع صور السلايدر؟')) {
                        setDesignSettings(prev => ({
                          ...prev,
                          slider: {
                            ...prev.slider,
                            images: []
                          }
                        }));
                      }
                    }}
                    className="px-3 py-2 md:px-4 md:py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors flex items-center text-sm md:text-base whitespace-nowrap"
                    disabled={(designSettings.slider?.images || []).length === 0}
                  >
                    <Trash2 className="w-4 h-4 ml-1 md:ml-2" />
                    حذف الكل
                  </button>
                </div>
              </div>

              {/* Slider Settings */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6 bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl border border-gray-200">
                <div>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={designSettings.slider?.enabled || false}
                      onChange={(e) => updateSlider('enabled', e.target.checked)}
                      className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                    />
                    <span className="mr-2 text-sm">تفعيل السلايدر</span>
                  </label>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    ارتفاع السلايدر
                  </label>
                  <input
                    type="text"
                    value={designSettings.slider?.height || '400px'}
                    onChange={(e) => updateSlider('height', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm"
                    placeholder="400px"
                  />
                </div>

                <div>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={designSettings.slider?.autoplay || false}
                      onChange={(e) => updateSlider('autoplay', e.target.checked)}
                      className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                    />
                    <span className="mr-2 text-sm">تشغيل تلقائي</span>
                  </label>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    سرعة التبديل (مللي ثانية)
                  </label>
                  <input
                    type="number"
                    value={designSettings.slider?.autoplaySpeed || 3000}
                    onChange={(e) => updateSlider('autoplaySpeed', Number(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm"
                    min="1000"
                    max="10000"
                    step="500"
                  />
                </div>
              </div>

              {/* Advanced Slider Controls */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                <div>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={designSettings.slider?.showDots !== undefined ? designSettings.slider.showDots : true}
                      onChange={(e) => updateSlider('showDots', e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="mr-2 text-sm">إظهار النقاط</span>
                  </label>
                </div>

                <div>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={designSettings.slider?.showArrows !== undefined ? designSettings.slider.showArrows : true}
                      onChange={(e) => updateSlider('showArrows', e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="mr-2 text-sm">إظهار الأسهم</span>
                  </label>
                </div>

                <div>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={designSettings.slider?.pauseOnHover !== undefined ? designSettings.slider.pauseOnHover : true}
                      onChange={(e) => updateSlider('pauseOnHover', e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="mr-2 text-sm">إيقاف عند التمرير</span>
                  </label>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    تأثير الانتقال
                  </label>
                  <select
                    value={designSettings.slider?.transition || 'slide'}
                    onChange={(e) => updateSlider('transition', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                  >
                    <option value="slide">انزلاق</option>
                    <option value="fade">تلاشي</option>
                    <option value="zoom">تكبير</option>
                  </select>
                </div>
              </div>

              {/* Slider Images */}
              <div className="space-y-6">
                <div className="flex justify-between items-center">
                  <h4 className="text-lg font-semibold text-gray-800">صور السلايدر</h4>
                  <span className="text-sm text-gray-500">
                    {(designSettings.slider?.images || []).length} صورة
                  </span>
                </div>
                {(designSettings.slider?.images || []).map((image, index) => (
                  <div key={image.id} className="p-4 md:p-6 border-2 border-gray-200 rounded-xl bg-gradient-to-r from-white to-gray-50 shadow-sm hover:shadow-md transition-all duration-300">
                    <div className="flex justify-between items-center mb-4">
                      <h5 className="text-sm md:text-base font-medium break-words">الصورة {index + 1}</h5>
                      <div className="flex gap-2">
                        <button
                          onClick={() => {
                            const newIndex = index > 0 ? index - 1 : (designSettings.slider?.images || []).length - 1;
                            const images = [...(designSettings.slider?.images || [])];
                            [images[index], images[newIndex]] = [images[newIndex], images[index]];
                            setDesignSettings(prev => ({
                              ...prev,
                              slider: { ...prev.slider, images }
                            }));
                          }}
                          className="text-blue-600 hover:text-blue-700 p-1 text-xs md:text-sm"
                          title="تحريك لأعلى"
                        >
                          ↑
                        </button>
                        <button
                          onClick={() => {
                            const newIndex = index < (designSettings.slider?.images || []).length - 1 ? index + 1 : 0;
                            const images = [...(designSettings.slider?.images || [])];
                            [images[index], images[newIndex]] = [images[newIndex], images[index]];
                            setDesignSettings(prev => ({
                              ...prev,
                              slider: { ...prev.slider, images }
                            }));
                          }}
                          className="text-blue-600 hover:text-blue-700 p-1 text-xs md:text-sm"
                          title="تحريك لأسفل"
                        >
                          ↓
                        </button>
                        <button
                          onClick={() => removeSliderImage(image.id)}
                          className="text-red-600 hover:text-red-700 p-1"
                          title="حذف الصورة"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          صورة السلايدر
                        </label>
                        <div className="space-y-3">
                          {/* Image Upload Area */}
                          <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-primary-400 transition-colors">
                            <input
                              type="file"
                              accept="image/*"
                              onChange={(e) => {
                                const file = e.target.files?.[0];
                                if (file) {
                                  const reader = new FileReader();
                                  reader.onload = (event) => {
                                    updateSliderImage(image.id, 'url', event.target?.result as string);
                                  };
                                  reader.readAsDataURL(file);
                                }
                              }}
                              className="hidden"
                              id={`slider-upload-${image.id}`}
                            />
                            <label
                              htmlFor={`slider-upload-${image.id}`}
                              className="cursor-pointer flex flex-col items-center"
                            >
                              <Upload className="w-8 h-8 text-gray-400 mb-2" />
                              <span className="text-sm text-gray-600">اضغط لرفع صورة أو اسحب وأفلت</span>
                              <span className="text-xs text-gray-500 mt-1">JPG, PNG, WebP (أقل من 2MB)</span>
                            </label>
                          </div>

                          {/* Manual URL Input */}
                          <div>
                            <label className="block text-xs text-gray-600 mb-1">أو أدخل رابط الصورة:</label>
                            <input
                              type="text"
                              value={image.url}
                              onChange={(e) => updateSliderImage(image.id, 'url', e.target.value)}
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm"
                              placeholder="/images/products/slider/slide1.jpg أو https://example.com/image.jpg"
                            />
                          </div>

                          {/* Image Preview */}
                          {image.url && (
                            <div className="mt-3">
                              <img
                                src={image.url}
                                alt={image.title || 'معاينة السلايدر'}
                                className="w-full h-32 object-cover rounded-lg border border-gray-200"
                                onError={(e) => {
                                  e.currentTarget.src = '/images/products/placeholder-default.svg';
                                }}
                              />
                            </div>
                          )}
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          العنوان
                        </label>
                        <input
                          type="text"
                          value={image.title}
                          onChange={(e) => updateSliderImage(image.id, 'title', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm"
                          placeholder="عنوان الصورة"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          الوصف
                        </label>
                        <input
                          type="text"
                          value={image.description}
                          onChange={(e) => updateSliderImage(image.id, 'description', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm"
                          placeholder="وصف الصورة"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          رابط الانتقال (اختياري)
                        </label>
                        <input
                          type="text"
                          value={image.link || ''}
                          onChange={(e) => updateSliderImage(image.id, 'link', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm"
                          placeholder="/products"
                        />
                      </div>
                    </div>
                  </div>
                ))}

                {(designSettings.slider?.images || []).length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    <ImageIcon className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p>لا توجد صور في السلايدر</p>
                    <p className="text-sm">اضغط "إضافة صورة" لإضافة أول صورة</p>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Save Button */}
        <div className="flex justify-end mt-8">
          <button
            onClick={handleSave}
            className="px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors flex items-center space-x-2 space-x-reverse"
          >
            <Download className="w-5 h-5" />
            <span>حفظ جميع الإعدادات</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default AdvancedDesignSettings;
