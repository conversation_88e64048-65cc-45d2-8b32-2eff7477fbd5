'use client';

import React, { useState, useRef } from 'react';
import { Upload, X, Check, AlertCircle } from 'lucide-react';

interface ImageUploaderProps {
  value: string;
  onChange: (url: string) => void;
  className?: string;
}

const ImageUploader: React.FC<ImageUploaderProps> = ({ value, onChange, className = '' }) => {
  const [isDragging, setIsDragging] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const validateFile = (file: File): { valid: boolean; error?: string } => {
    const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/svg+xml'];
    const maxSize = 2 * 1024 * 1024; // 2MB

    if (!validTypes.includes(file.type)) {
      return {
        valid: false,
        error: 'نوع الملف غير مدعوم. الأنواع المدعومة: JPG, PNG, WebP, SVG'
      };
    }

    if (file.size > maxSize) {
      return {
        valid: false,
        error: 'حجم الملف كبير جداً. الحد الأقصى 2 ميجابايت'
      };
    }

    return { valid: true };
  };

  const handleFileUpload = async (file: File) => {
    const validation = validateFile(file);
    if (!validation.valid) {
      setError(validation.error || 'خطأ في الملف');
      return;
    }

    setIsUploading(true);
    setError(null);

    try {
      // تحويل الملف إلى Base64 للمعاينة المؤقتة
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        onChange(result);
        setIsUploading(false);
      };
      reader.readAsDataURL(file);

    } catch (err) {
      console.error('خطأ في رفع الصورة:', err);
      setError(err instanceof Error ? err.message : 'فشل في رفع الصورة');
      setIsUploading(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileUpload(files[0]);
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileUpload(files[0]);
    }
  };

  const clearImage = () => {
    onChange('/images/products/placeholder-default.svg');
    setError(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Upload Area */}
      <div
        className={`relative border-2 border-dashed rounded-xl p-6 text-center transition-all duration-300 ${
          isDragging
            ? 'border-primary-500 bg-primary-50'
            : 'border-gray-300 hover:border-primary-400 hover:bg-gray-50'
        }`}
        onDrop={handleDrop}
        onDragOver={(e) => {
          e.preventDefault();
          setIsDragging(true);
        }}
        onDragLeave={() => setIsDragging(false)}
      >
        {isUploading ? (
          <div className="flex flex-col items-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mb-2"></div>
            <p className="text-sm text-gray-600">جاري رفع الصورة...</p>
          </div>
        ) : (
          <>
            <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h4 className="text-lg font-medium text-gray-800 mb-2">رفع صورة المنتج</h4>
            <p className="text-gray-600 mb-4">
              اسحب وأفلت الصورة هنا أو اضغط للاختيار
            </p>
            <div className="text-sm text-gray-500 space-y-1">
              <p><strong>الصيغ المدعومة:</strong> JPG, PNG, WebP, SVG</p>
              <p><strong>الحد الأقصى:</strong> 2 ميجابايت</p>
              <p><strong>المقاس المثالي:</strong> 800x800 بكسل</p>
            </div>
            <input
              ref={fileInputRef}
              type="file"
              accept="image/jpeg,image/jpg,image/png,image/webp,image/svg+xml"
              onChange={handleFileSelect}
              className="hidden"
            />
            <button
              type="button"
              onClick={() => fileInputRef.current?.click()}
              className="mt-4 bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-colors inline-flex items-center space-x-2 space-x-reverse"
            >
              <Upload className="w-4 h-4" />
              <span>اختيار صورة</span>
            </button>
          </>
        )}
      </div>

      {/* Error Message */}
      {error && (
        <div className="flex items-center p-3 bg-red-50 border border-red-200 rounded-lg">
          <AlertCircle className="w-5 h-5 text-red-500 ml-2" />
          <p className="text-sm text-red-700">{error}</p>
        </div>
      )}

      {/* Image Preview */}
      {value && value !== '/images/products/placeholder-default.svg' && (
        <div className="relative">
          <div className="flex items-center p-3 bg-green-50 border border-green-200 rounded-lg">
            <Check className="w-5 h-5 text-green-500 ml-2" />
            <p className="text-sm text-green-700">تم رفع الصورة بنجاح</p>
          </div>
          
          <div className="mt-3 relative inline-block">
            <img
              src={value}
              alt="معاينة المنتج"
              className="w-32 h-32 object-cover rounded-lg border border-gray-200 shadow-sm"
              onError={(e) => {
                e.currentTarget.src = '/images/products/placeholder-default.svg';
              }}
            />
            <button
              type="button"
              onClick={clearImage}
              className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors"
              title="حذف الصورة"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>
      )}

      {/* Manual URL Input */}
      <div className="border-t pt-4">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          أو أدخل رابط الصورة يدوياً:
        </label>
        <input
          type="url"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm"
          placeholder="https://example.com/image.jpg أو /images/products/my-product.jpg"
        />
        <div className="mt-2 text-xs text-gray-500 space-y-1">
          <p><strong>محلياً:</strong> /images/products/books/math-book.jpg</p>
          <p><strong>خارجياً:</strong> https://example.com/image.jpg</p>
        </div>
      </div>
    </div>
  );
};

export default ImageUploader;
