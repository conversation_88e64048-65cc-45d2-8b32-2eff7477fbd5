'use client';

import React, { useState } from 'react';
import { useAdmin } from '@/contexts/AdminContext';
import { useToast } from '@/components/ToastContainer';
import { 
  Monitor, 
  Palette, 
  Type, 
  Save, 
  Eye, 
  EyeOff,
  Phone,
  Mail,
  MapPin,
  Settings
} from 'lucide-react';

const TopBarSettings: React.FC = () => {
  const { settings, updateSettings } = useAdmin();
  const { showSuccess, showError } = useToast();
  const [showPreview, setShowPreview] = useState(true);

  const defaultTopBarSettings = {
    enabled: true,
    backgroundColor: '#2563eb',
    textColor: '#ffffff',
    fontSize: '14px',
    fontWeight: '400',
    padding: '8px 0',
    showPhone: true,
    showEmail: true,
    showShipping: true,
    customShippingText: ''
  };

  const topBarSettings = settings.topBarSettings || defaultTopBarSettings;

  const updateTopBarSetting = (key: string, value: any) => {
    const updatedSettings = {
      ...settings,
      topBarSettings: {
        ...topBarSettings,
        [key]: value
      }
    };
    updateSettings(updatedSettings);
  };

  const handleSave = () => {
    try {
      if (typeof window !== 'undefined') {
        localStorage.setItem('admin-settings', JSON.stringify(settings));
      }
      showSuccess('تم الحفظ!', 'تم حفظ إعدادات الشريط العلوي بنجاح');
    } catch (error) {
      showError('خطأ', 'حدث خطأ أثناء حفظ الإعدادات');
    }
  };

  const resetToDefault = () => {
    if (confirm('هل أنت متأكد من إعادة تعيين إعدادات الشريط العلوي للقيم الافتراضية؟')) {
      updateSettings({
        ...settings,
        topBarSettings: defaultTopBarSettings
      });
      showSuccess('تم الإعادة!', 'تم إعادة تعيين الإعدادات الافتراضية');
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Monitor className="w-6 h-6 text-blue-600" />
          <h3 className="text-xl font-bold text-gray-800">إعدادات الشريط العلوي</h3>
        </div>
        
        <div className="flex items-center gap-3">
          <button
            onClick={() => setShowPreview(!showPreview)}
            className="flex items-center gap-2 px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
          >
            {showPreview ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            <span className="text-sm">{showPreview ? 'إخفاء المعاينة' : 'عرض المعاينة'}</span>
          </button>
          
          <button
            onClick={handleSave}
            className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
          >
            <Save className="w-4 h-4" />
            <span className="text-sm">حفظ التغييرات</span>
          </button>
        </div>
      </div>

      {/* معاينة الشريط العلوي */}
      {showPreview && topBarSettings.enabled && (
        <div className="border rounded-lg overflow-hidden">
          <div className="bg-gray-100 px-4 py-2 text-sm font-medium text-gray-700">
            معاينة الشريط العلوي
          </div>
          <div 
            className="py-2"
            style={{
              backgroundColor: topBarSettings.backgroundColor,
              color: topBarSettings.textColor,
              fontSize: topBarSettings.fontSize,
              fontWeight: topBarSettings.fontWeight,
              padding: topBarSettings.padding
            }}
          >
            <div className="container mx-auto px-4">
              <div className="flex justify-between items-center text-sm">
                <div className="flex items-center space-x-4 space-x-reverse">
                  {topBarSettings.showPhone && (
                    <div className="flex items-center">
                      <Phone className="w-4 h-4 ml-2" />
                      <span>{settings.contactInfo.phone}</span>
                    </div>
                  )}
                  {topBarSettings.showEmail && (
                    <div className="flex items-center">
                      <Mail className="w-4 h-4 ml-2" />
                      <span>{settings.contactInfo.email}</span>
                    </div>
                  )}
                </div>
                {topBarSettings.showShipping && (
                  <div className="flex items-center">
                    <MapPin className="w-4 h-4 ml-2" />
                    <span>
                      {topBarSettings.customShippingText || 
                       `التوصيل المجاني للطلبات أكثر من ${settings.shippingSettings.freeShippingThreshold} ريال`}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* إعدادات الشريط العلوي */}
      <div className="bg-white p-6 rounded-lg border">
        <h4 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
          <Settings className="w-5 h-5" />
          إعدادات الشريط العلوي
        </h4>
        
        {/* تفعيل/إلغاء تفعيل الشريط */}
        <div className="mb-6 p-4 bg-blue-50 rounded-lg">
          <label className="flex items-center gap-3">
            <input
              type="checkbox"
              checked={topBarSettings.enabled}
              onChange={(e) => updateTopBarSetting('enabled', e.target.checked)}
              className="w-5 h-5 text-blue-600 rounded focus:ring-blue-500"
            />
            <span className="text-lg font-medium text-gray-700">
              عرض الشريط العلوي
            </span>
          </label>
        </div>

        {topBarSettings.enabled && (
          <div className="space-y-6">
            {/* إعدادات التصميم */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {/* لون الخلفية */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  لون الخلفية
                </label>
                <div className="flex gap-2">
                  <input
                    type="color"
                    value={topBarSettings.backgroundColor}
                    onChange={(e) => updateTopBarSetting('backgroundColor', e.target.value)}
                    className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                  />
                  <input
                    type="text"
                    value={topBarSettings.backgroundColor}
                    onChange={(e) => updateTopBarSetting('backgroundColor', e.target.value)}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="#2563eb"
                  />
                </div>
              </div>

              {/* لون النص */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  لون النص
                </label>
                <div className="flex gap-2">
                  <input
                    type="color"
                    value={topBarSettings.textColor}
                    onChange={(e) => updateTopBarSetting('textColor', e.target.value)}
                    className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                  />
                  <input
                    type="text"
                    value={topBarSettings.textColor}
                    onChange={(e) => updateTopBarSetting('textColor', e.target.value)}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="#ffffff"
                  />
                </div>
              </div>

              {/* حجم الخط */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  حجم الخط
                </label>
                <select
                  value={topBarSettings.fontSize}
                  onChange={(e) => updateTopBarSetting('fontSize', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="12px">صغير (12px)</option>
                  <option value="14px">متوسط (14px)</option>
                  <option value="16px">كبير (16px)</option>
                  <option value="18px">كبير جداً (18px)</option>
                </select>
              </div>

              {/* وزن الخط */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  وزن الخط
                </label>
                <select
                  value={topBarSettings.fontWeight}
                  onChange={(e) => updateTopBarSetting('fontWeight', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="300">خفيف (300)</option>
                  <option value="400">عادي (400)</option>
                  <option value="500">متوسط (500)</option>
                  <option value="600">سميك (600)</option>
                  <option value="700">سميك جداً (700)</option>
                </select>
              </div>

              {/* المسافة الداخلية */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  المسافة الداخلية
                </label>
                <select
                  value={topBarSettings.padding}
                  onChange={(e) => updateTopBarSetting('padding', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="4px 0">صغيرة (4px)</option>
                  <option value="8px 0">متوسطة (8px)</option>
                  <option value="12px 0">كبيرة (12px)</option>
                  <option value="16px 0">كبيرة جداً (16px)</option>
                </select>
              </div>
            </div>

            {/* إعدادات المحتوى */}
            <div className="border-t pt-6">
              <h5 className="text-md font-semibold text-gray-800 mb-4">إعدادات المحتوى</h5>
              
              <div className="space-y-4">
                {/* عرض الهاتف */}
                <label className="flex items-center gap-3">
                  <input
                    type="checkbox"
                    checked={topBarSettings.showPhone}
                    onChange={(e) => updateTopBarSetting('showPhone', e.target.checked)}
                    className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
                  />
                  <Phone className="w-4 h-4 text-gray-600" />
                  <span className="text-gray-700">عرض رقم الهاتف</span>
                </label>

                {/* عرض البريد الإلكتروني */}
                <label className="flex items-center gap-3">
                  <input
                    type="checkbox"
                    checked={topBarSettings.showEmail}
                    onChange={(e) => updateTopBarSetting('showEmail', e.target.checked)}
                    className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
                  />
                  <Mail className="w-4 h-4 text-gray-600" />
                  <span className="text-gray-700">عرض البريد الإلكتروني</span>
                </label>

                {/* عرض معلومات الشحن */}
                <label className="flex items-center gap-3">
                  <input
                    type="checkbox"
                    checked={topBarSettings.showShipping}
                    onChange={(e) => updateTopBarSetting('showShipping', e.target.checked)}
                    className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
                  />
                  <MapPin className="w-4 h-4 text-gray-600" />
                  <span className="text-gray-700">عرض معلومات الشحن</span>
                </label>

                {/* نص شحن مخصص */}
                {topBarSettings.showShipping && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      نص شحن مخصص (اختياري)
                    </label>
                    <input
                      type="text"
                      value={topBarSettings.customShippingText || ''}
                      onChange={(e) => updateTopBarSetting('customShippingText', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="اتركه فارغاً لاستخدام النص التلقائي"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      إذا تركته فارغاً، سيظهر: "التوصيل المجاني للطلبات أكثر من {settings.shippingSettings.freeShippingThreshold} ريال"
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* أزرار الإجراءات */}
      <div className="flex justify-between items-center pt-4 border-t">
        <button
          onClick={resetToDefault}
          className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
        >
          إعادة تعيين افتراضي
        </button>
        
        <button
          onClick={handleSave}
          className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          حفظ التغييرات
        </button>
      </div>
    </div>
  );
};

export default TopBarSettings;
