'use client';

import React from 'react';

interface MapWithPinProps {
  lat: string;
  lng: string;
  storeName?: string;
  height?: string;
}

const MapWithPin: React.FC<MapWithPinProps> = ({ 
  lat, 
  lng, 
  storeName = 'مكتبة أنوار دارس',
  height = '320px'
}) => {
  // إنشاء رابط خريطة محسن مع دبوس واضح
  const generateEnhancedMapSrc = () => {
    const coordinates = `${lat},${lng}`;
    const encodedStoreName = encodeURIComponent(storeName);
    
    // استخدام Google Maps مع معاملات محسنة للدبوس
    const params = new URLSearchParams({
      'pb': `!1m18!1m12!1m3!1d1920!2d${lng}!3d${lat}!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2z${encodedStoreName}!5e0!3m2!1sar!2sye!4v${Date.now()}!5m2!1sar!2sye`,
      'q': coordinates,
      'hl': 'ar',
      'gl': 'YE',
      'z': '16'
    });
    
    return `https://www.google.com/maps/embed?${params.toString()}`;
  };

  return (
    <div className="relative w-full rounded-lg overflow-hidden shadow-md" style={{ height }}>
      {/* الخريطة الأساسية */}
      <iframe
        src={generateEnhancedMapSrc()}
        width="100%"
        height="100%"
        style={{ border: 0 }}
        allowFullScreen
        loading="lazy"
        referrerPolicy="no-referrer-when-downgrade"
        title={`موقع ${storeName} على الخريطة`}
        className="w-full h-full"
      />
      
      {/* دبوس مخصص فوق الخريطة */}
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-full pointer-events-none z-10">
        <div className="relative">
          {/* الدبوس */}
          <div className="bg-red-600 text-white rounded-full w-12 h-12 flex items-center justify-center shadow-lg border-4 border-white">
            <span className="text-xl">🏪</span>
          </div>
          
          {/* المثلث السفلي */}
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-8 border-l-transparent border-r-transparent border-t-red-600"></div>
          
          {/* تسمية المتجر */}
          <div className="absolute -top-16 left-1/2 transform -translate-x-1/2 bg-white px-3 py-1 rounded-lg shadow-lg border border-gray-200 whitespace-nowrap">
            <span className="text-sm font-semibold text-gray-800">{storeName}</span>
            <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-2 border-r-2 border-t-4 border-l-transparent border-r-transparent border-t-white"></div>
          </div>
        </div>
      </div>
      
      {/* مؤشر الموقع في الزاوية */}
      <div className="absolute top-4 right-4 bg-white bg-opacity-90 px-3 py-2 rounded-lg shadow-md">
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-red-600 rounded-full animate-pulse"></div>
          <span className="text-xs font-medium text-gray-700">موقع المتجر</span>
        </div>
      </div>
      
      {/* إحداثيات في الزاوية السفلى (للمدير فقط) */}
      <div className="absolute bottom-4 left-4 bg-black bg-opacity-70 text-white px-2 py-1 rounded text-xs font-mono">
        {lat}, {lng}
      </div>
    </div>
  );
};

export default MapWithPin;
