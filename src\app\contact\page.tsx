'use client';

import React, { useState } from 'react';
import { useAdmin } from '@/contexts/AdminContext';

import Header from '@/components/Header';
import Footer from '@/components/Footer';
import WorkingHoursDisplay from '@/components/WorkingHoursDisplay';
import InteractiveMap from '@/components/InteractiveMap';
import { 
  Phone, 
  Mail, 
  MapPin, 
  MessageCircle, 
  Facebook, 
  Instagram,
  Clock,
  Send
} from 'lucide-react';

export default function ContactPage() {
  const { settings } = useAdmin();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    alert('تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.');
    setFormData({
      name: '',
      email: '',
      phone: '',
      subject: '',
      message: ''
    });
    setIsSubmitting(false);
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        {/* Page Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">تواصل معنا</h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            نحن هنا لمساعدتك! تواصل معنا في أي وقت وسنكون سعداء للرد على استفساراتك
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Contact Information */}
          <div className="lg:col-span-1 space-y-6">
            {/* Contact Cards */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center mb-4">
                <div className="bg-primary-100 p-3 rounded-full ml-4">
                  <Phone className="w-6 h-6 text-primary-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-800">اتصل بنا</h3>
                  <p className="text-gray-600">للاستفسارات العاجلة</p>
                </div>
              </div>
              <a 
                href={`tel:${settings.contactInfo.phone}`}
                className="text-primary-600 hover:text-primary-700 font-semibold"
              >
                {settings.contactInfo.phone}
              </a>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center mb-4">
                <div className="bg-green-100 p-3 rounded-full ml-4">
                  <MessageCircle className="w-6 h-6 text-green-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-800">واتساب</h3>
                  <p className="text-gray-600">تواصل سريع ومباشر</p>
                </div>
              </div>
              {settings.contactInfo.socialMedia.whatsapp && (
                <a 
                  href={`https://wa.me/${settings.contactInfo.socialMedia.whatsapp.replace(/[^0-9]/g, '')}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-green-600 hover:text-green-700 font-semibold"
                >
                  {settings.contactInfo.socialMedia.whatsapp}
                </a>
              )}
            </div>

            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center mb-4">
                <div className="bg-blue-100 p-3 rounded-full ml-4">
                  <Mail className="w-6 h-6 text-blue-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-800">البريد الإلكتروني</h3>
                  <p className="text-gray-600">للاستفسارات التفصيلية</p>
                </div>
              </div>
              <a 
                href={`mailto:${settings.contactInfo.email}`}
                className="text-blue-600 hover:text-blue-700 font-semibold"
              >
                {settings.contactInfo.email}
              </a>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center mb-4">
                <div className="bg-red-100 p-3 rounded-full ml-4">
                  <MapPin className="w-6 h-6 text-red-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-800">العنوان</h3>
                  <p className="text-gray-600">موقعنا الفعلي</p>
                </div>
              </div>
              <p className="text-gray-700">{settings.contactInfo.address}</p>
            </div>

            {/* Working Hours */}
            <WorkingHoursDisplay />

            {/* Social Media */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="font-semibold text-gray-800 mb-4">تابعنا على</h3>
              <div className="flex space-x-4 space-x-reverse">
                {settings.contactInfo.socialMedia.facebook && (
                  <a
                    href={settings.contactInfo.socialMedia.facebook}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="bg-blue-600 p-3 rounded-full text-white hover:bg-blue-700 transition-colors"
                  >
                    <Facebook className="w-5 h-5" />
                  </a>
                )}
                {settings.contactInfo.socialMedia.instagram && (
                  <a
                    href={settings.contactInfo.socialMedia.instagram}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="bg-pink-600 p-3 rounded-full text-white hover:bg-pink-700 transition-colors"
                  >
                    <Instagram className="w-5 h-5" />
                  </a>
                )}
                {settings.contactInfo.socialMedia.whatsapp && (
                  <a
                    href={`https://wa.me/${settings.contactInfo.socialMedia.whatsapp.replace(/[^0-9]/g, '')}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="bg-green-600 p-3 rounded-full text-white hover:bg-green-700 transition-colors"
                  >
                    <MessageCircle className="w-5 h-5" />
                  </a>
                )}
              </div>
            </div>
          </div>

          {/* Contact Form */}
          <div className="lg:col-span-2 space-y-8">
            {/* خريطة الموقع التفاعلية */}
            <InteractiveMap
              address={settings.contactInfo.address}
              whatsapp={settings.contactInfo.whatsapp}
            />

            {/* نموذج الاتصال */}
            <div className="bg-white rounded-lg shadow-md p-8">
              <h2 className="text-2xl font-semibold text-gray-800 mb-6">أرسل لنا رسالة</h2>
              
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      الاسم الكامل *
                    </label>
                    <input
                      type="text"
                      value={formData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      className="input-field"
                      placeholder="أدخل اسمك الكامل"
                      required
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      رقم الهاتف *
                    </label>
                    <input
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      className="input-field"
                      placeholder="مثال: +967712345678"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    البريد الإلكتروني
                  </label>
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    className="input-field"
                    placeholder="<EMAIL>"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    موضوع الرسالة *
                  </label>
                  <select
                    value={formData.subject}
                    onChange={(e) => handleInputChange('subject', e.target.value)}
                    className="input-field"
                    required
                  >
                    <option value="">اختر موضوع الرسالة</option>
                    <option value="general">استفسار عام</option>
                    <option value="order">استفسار عن طلب</option>
                    <option value="product">استفسار عن منتج</option>
                    <option value="complaint">شكوى</option>
                    <option value="suggestion">اقتراح</option>
                    <option value="other">أخرى</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    الرسالة *
                  </label>
                  <textarea
                    value={formData.message}
                    onChange={(e) => handleInputChange('message', e.target.value)}
                    className="input-field"
                    rows={6}
                    placeholder="اكتب رسالتك هنا..."
                    required
                  />
                </div>

                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full bg-primary-600 text-white py-3 rounded-lg font-semibold hover:bg-primary-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                >
                  {isSubmitting ? (
                    'جاري الإرسال...'
                  ) : (
                    <>
                      <Send className="w-5 h-5 ml-2" />
                      إرسال الرسالة
                    </>
                  )}
                </button>
              </form>
            </div>
          </div>
        </div>

        {/* FAQ Section */}
        <div className="mt-16">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-gray-800 mb-4">الأسئلة الشائعة</h2>
            <p className="text-gray-600">إجابات على أكثر الأسئلة شيوعاً</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="font-semibold text-gray-800 mb-3">كم يستغرق التوصيل؟</h3>
              <p className="text-gray-600">
                عادة ما يستغرق التوصيل من 1-3 أيام عمل داخل المدن الرئيسية، و3-5 أيام للمناطق الأخرى.
              </p>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="font-semibold text-gray-800 mb-3">ما هي طرق الدفع المتاحة؟</h3>
              <p className="text-gray-600">
                نقبل الدفع نقداً عند الاستلام والتحويل البنكي. نعمل على إضافة المزيد من طرق الدفع قريباً.
              </p>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="font-semibold text-gray-800 mb-3">هل يمكنني إرجاع المنتج؟</h3>
              <p className="text-gray-600">
                نعم، يمكنك إرجاع المنتج خلال 7 أيام من تاريخ الاستلام بشرط أن يكون في حالته الأصلية.
              </p>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="font-semibold text-gray-800 mb-3">هل التوصيل مجاني؟</h3>
              <p className="text-gray-600">
                التوصيل مجاني للطلبات أكثر من {settings.shippingSettings.freeShippingThreshold} ريال، 
                وإلا فتكلفة التوصيل {settings.shippingSettings.shippingCost} ريال.
              </p>
            </div>
          </div>
        </div>


      </main>

      <Footer />
    </div>
  );
}
