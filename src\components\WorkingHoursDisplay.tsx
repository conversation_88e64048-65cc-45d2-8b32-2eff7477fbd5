'use client';

import React from 'react';
import { useAdmin } from '@/contexts/AdminContext';
import { Clock, Calendar, CheckCircle, XCircle } from 'lucide-react';

const WorkingHoursDisplay: React.FC = () => {
  const { settings } = useAdmin();

  console.log('WorkingHoursDisplay: إعدادات أوقات العمل:', settings.workingHours);

  if (!settings.workingHours?.enabled) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
        <div className="flex items-center gap-3 mb-4">
          <Clock className="w-6 h-6 text-yellow-600" />
          <h3 className="text-xl font-bold text-yellow-800">أوقات العمل</h3>
        </div>
        <div className="text-center">
          <p className="text-yellow-700 mb-4">
            أوقات العمل غير مفعلة حالياً
          </p>
          <p className="text-sm text-yellow-600">
            لتفعيل عرض أوقات العمل، اذهب إلى لوحة التحكم → الإعدادات → أوقات العمل
          </p>
        </div>
      </div>
    );
  }

  const { workingHours } = settings;

  const days = [
    { key: 'saturday', name: 'السبت', nameEn: 'Saturday' },
    { key: 'sunday', name: 'الأحد', nameEn: 'Sunday' },
    { key: 'monday', name: 'الاثنين', nameEn: 'Monday' },
    { key: 'tuesday', name: 'الثلاثاء', nameEn: 'Tuesday' },
    { key: 'wednesday', name: 'الأربعاء', nameEn: 'Wednesday' },
    { key: 'thursday', name: 'الخميس', nameEn: 'Thursday' },
    { key: 'friday', name: 'الجمعة', nameEn: 'Friday' }
  ];

  const formatTime = (time: string) => {
    if (!time) return '';
    
    if (workingHours.displayFormat === '12') {
      const [hours, minutes] = time.split(':');
      const hour = parseInt(hours);
      const ampm = hour >= 12 ? 'م' : 'ص';
      const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
      return `${displayHour}:${minutes} ${ampm}`;
    }
    
    return time;
  };

  const getCurrentDayStatus = () => {
    const now = new Date();
    const currentDay = now.getDay(); // 0 = Sunday, 1 = Monday, etc.
    
    // تحويل رقم اليوم إلى مفتاح اليوم
    const dayKeys = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
    const todayKey = dayKeys[currentDay];
    
    const todaySchedule = workingHours.schedule?.[todayKey as keyof typeof workingHours.schedule];
    
    if (!todaySchedule?.enabled) {
      return {
        isOpen: false,
        message: workingHours.closedMessage || 'مغلق اليوم',
        dayName: days.find(d => d.key === todayKey)?.name || ''
      };
    }

    const currentTime = now.getHours() * 60 + now.getMinutes();
    const [openHour, openMin] = (todaySchedule.open || '08:00').split(':').map(Number);
    const [closeHour, closeMin] = (todaySchedule.close || '20:00').split(':').map(Number);
    
    const openTime = openHour * 60 + openMin;
    const closeTime = closeHour * 60 + closeMin;
    
    const isOpen = currentTime >= openTime && currentTime <= closeTime;
    
    return {
      isOpen,
      message: isOpen ? workingHours.openMessage || 'مفتوح الآن' : workingHours.closedMessage || 'مغلق الآن',
      dayName: days.find(d => d.key === todayKey)?.name || '',
      openTime: formatTime(todaySchedule.open || '08:00'),
      closeTime: formatTime(todaySchedule.close || '20:00')
    };
  };

  const currentStatus = getCurrentDayStatus();

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex items-center gap-3 mb-6">
        <Clock className="w-6 h-6 text-blue-600" />
        <h3 className="text-xl font-bold text-gray-800">أوقات العمل</h3>
      </div>

      {/* حالة اليوم الحالي */}
      <div className={`p-4 rounded-lg mb-6 ${currentStatus.isOpen ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
        <div className="flex items-center gap-3">
          {currentStatus.isOpen ? (
            <CheckCircle className="w-6 h-6 text-green-600" />
          ) : (
            <XCircle className="w-6 h-6 text-red-600" />
          )}
          <div>
            <p className={`font-bold text-lg ${currentStatus.isOpen ? 'text-green-800' : 'text-red-800'}`}>
              {currentStatus.message}
            </p>
            <p className="text-sm text-gray-600">
              {currentStatus.dayName}
              {currentStatus.openTime && currentStatus.closeTime && (
                <span> - من {currentStatus.openTime} إلى {currentStatus.closeTime}</span>
              )}
            </p>
          </div>
        </div>
      </div>

      {/* جدول أوقات العمل الأسبوعي */}
      <div className="space-y-3">
        <div className="flex items-center gap-2 mb-4">
          <Calendar className="w-5 h-5 text-gray-600" />
          <h4 className="font-semibold text-gray-800">الجدول الأسبوعي</h4>
        </div>
        
        {days.map((day) => {
          const daySchedule = workingHours.schedule?.[day.key as keyof typeof workingHours.schedule];
          const isToday = new Date().getDay() === days.indexOf(day);
          
          return (
            <div 
              key={day.key} 
              className={`flex items-center justify-between p-3 rounded-lg border ${
                isToday ? 'bg-blue-50 border-blue-200' : 'bg-gray-50 border-gray-200'
              }`}
            >
              <div className="flex items-center gap-3">
                <span className={`font-medium ${isToday ? 'text-blue-800' : 'text-gray-700'}`}>
                  {day.name}
                  {isToday && <span className="text-xs text-blue-600 mr-2">(اليوم)</span>}
                </span>
              </div>
              
              <div className="flex items-center gap-2">
                {daySchedule?.enabled ? (
                  <>
                    <CheckCircle className="w-4 h-4 text-green-600" />
                    <span className="text-sm text-gray-700">
                      {formatTime(daySchedule.open || '08:00')} - {formatTime(daySchedule.close || '20:00')}
                    </span>
                  </>
                ) : (
                  <>
                    <XCircle className="w-4 h-4 text-red-600" />
                    <span className="text-sm text-red-600 font-medium">مغلق</span>
                  </>
                )}
              </div>
            </div>
          );
        })}
      </div>

      {/* معلومات إضافية */}
      <div className="mt-6 p-4 bg-gray-50 rounded-lg">
        <p className="text-sm text-gray-600 text-center">
          <Clock className="w-4 h-4 inline-block ml-1" />
          المنطقة الزمنية: صنعاء
        </p>
        <p className="text-xs text-gray-500 text-center mt-2">
          الأوقات معروضة بتوقيت اليمن المحلي
        </p>
      </div>
    </div>
  );
};

export default WorkingHoursDisplay;
