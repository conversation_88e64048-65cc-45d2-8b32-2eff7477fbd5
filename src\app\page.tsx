'use client';

import React from 'react';
import Link from 'next/link';
import { useAdmin } from '@/contexts/AdminContext';

import Header from '@/components/Header';
import Footer from '@/components/Footer';
import ProductCard from '@/components/ProductCard';
import CategoryGrid from '@/components/CategoryGrid'
import ImageSlider from '@/components/ImageSlider';
import { ArrowLeft, Star, TrendingUp, Zap } from 'lucide-react';

export default function HomePage() {
  const { products, categories, settings } = useAdmin();
  
  const featuredProducts = products.filter(product => product.featured);
  const bestSellers = products.slice(0, 8);

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main>
        {/* Hero Slider */}
        <section className="container mx-auto px-4 py-8">
          <ImageSlider />
        </section>





        {/* Categories Section */}
        <CategoryGrid
          title="تسوق حسب الفئة"
          className="bg-gray-50"
          maxItems={8}
        />

        {/* Featured Products */}
        {featuredProducts.length > 0 && (
          <section className="py-16 bg-white">
            <div className="container mx-auto px-4">
              <div className="flex justify-between items-center mb-12">
                <div>
                  <h2 className="text-3xl font-bold text-gray-800 mb-2">المنتجات المميزة</h2>
                  <p className="text-gray-600">أفضل المنتجات المختارة خصيصاً لك</p>
                </div>
                <Link
                  href="/products?featured=true"
                  className="text-primary-600 hover:text-primary-700 font-medium flex items-center"
                >
                  عرض الكل
                  <ArrowLeft className="w-4 h-4 mr-2" />
                </Link>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                {featuredProducts.slice(0, 8).map((product) => (
                  <ProductCard key={product.id} product={product} />
                ))}
              </div>
            </div>
          </section>
        )}

        {/* Best Sellers */}
        <section className="py-16 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="flex justify-between items-center mb-12">
              <div>
                <h2 className="text-3xl font-bold text-gray-800 mb-2">الأكثر مبيعاً</h2>
                <p className="text-gray-600">المنتجات الأكثر طلباً من عملائنا</p>
              </div>
              <Link
                href="/products?sort=bestselling"
                className="text-primary-600 hover:text-primary-700 font-medium flex items-center"
              >
                عرض الكل
                <ArrowLeft className="w-4 h-4 mr-2" />
              </Link>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {bestSellers.map((product) => (
                <ProductCard key={product.id} product={product} />
              ))}
            </div>

            {/* Service Features */}
            <div className="mt-16 text-center">
              <div className="bg-gradient-to-r from-primary-50 to-primary-100 rounded-2xl p-8 border border-primary-200">
                <h3 className="text-2xl font-bold text-primary-800 mb-4">
                  🚀 توصيل سريع • 💎 جودة عالية • 💰 أسعار تنافسية
                </h3>
                <p className="text-primary-700 text-lg">
                  نضمن لك أفضل تجربة تسوق مع خدمة توصيل سريعة ومنتجات عالية الجودة بأسعار لا تُقاوم
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Newsletter Section */}
        <section className="py-16 bg-primary-600 text-white">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-3xl font-bold mb-4">اشترك في النشرة الإخبارية</h2>
            <p className="text-blue-100 mb-8 max-w-2xl mx-auto">
              احصل على آخر العروض والخصومات والمنتجات الجديدة مباشرة في بريدك الإلكتروني
            </p>
            <div className="max-w-md mx-auto flex gap-4">
              <input
                type="email"
                placeholder="أدخل بريدك الإلكتروني"
                className="flex-1 px-4 py-3 rounded-lg text-gray-800 focus:outline-none focus:ring-2 focus:ring-white"
              />
              <button className="bg-white text-primary-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                اشتراك
              </button>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
}
