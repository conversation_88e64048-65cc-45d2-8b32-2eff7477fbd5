'use client';

import React from 'react';
import { useAdmin } from '@/contexts/AdminContext';

import Header from '@/components/Header';
import Footer from '@/components/Footer';
import WorkingHoursDisplay from '@/components/WorkingHoursDisplay';
import { Clock, Calendar, Info } from 'lucide-react';

const WorkingHoursPage: React.FC = () => {
  const { settings } = useAdmin();

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        {/* العنوان الرئيسي */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center gap-3 mb-4">
            <Clock className="w-12 h-12 text-blue-600" />
            <h1 className="text-4xl font-bold text-gray-800">ساعات العمل</h1>
          </div>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            تعرف على أوقات عمل {settings.siteName || 'مكتبة أنوار دارس'} وخطط لزيارتك
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          {/* أوقات العمل الرئيسية */}
          <WorkingHoursDisplay />
          
          {/* معلومات إضافية */}
          <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* نصائح للزيارة */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center gap-3 mb-4">
                <Info className="w-6 h-6 text-blue-600" />
                <h3 className="text-xl font-bold text-gray-800">نصائح للزيارة</h3>
              </div>
              
              <div className="space-y-3 text-gray-600">
                <div className="flex items-start gap-3">
                  <span className="text-blue-600 font-bold">•</span>
                  <p>يُفضل الزيارة في الصباح الباكر لتجنب الازدحام</p>
                </div>
                <div className="flex items-start gap-3">
                  <span className="text-blue-600 font-bold">•</span>
                  <p>نوفر خدمة الحجز المسبق للكتب النادرة</p>
                </div>
                <div className="flex items-start gap-3">
                  <span className="text-blue-600 font-bold">•</span>
                  <p>خدمة التوصيل متاحة لجميع أنحاء المدينة</p>
                </div>
                <div className="flex items-start gap-3">
                  <span className="text-blue-600 font-bold">•</span>
                  <p>يمكنك التواصل معنا قبل الزيارة للتأكد من توفر المنتج</p>
                </div>
              </div>
            </div>
            
            {/* أيام العطل والمناسبات */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center gap-3 mb-4">
                <Calendar className="w-6 h-6 text-red-600" />
                <h3 className="text-xl font-bold text-gray-800">أيام العطل</h3>
              </div>
              
              <div className="space-y-3 text-gray-600">
                <div className="flex items-start gap-3">
                  <span className="text-red-600 font-bold">•</span>
                  <p>العطل الرسمية: مغلق</p>
                </div>
                <div className="flex items-start gap-3">
                  <span className="text-red-600 font-bold">•</span>
                  <p>عيد الفطر: مغلق 3 أيام</p>
                </div>
                <div className="flex items-start gap-3">
                  <span className="text-red-600 font-bold">•</span>
                  <p>عيد الأضحى: مغلق 4 أيام</p>
                </div>
                <div className="flex items-start gap-3">
                  <span className="text-orange-600 font-bold">•</span>
                  <p>شهر رمضان: ساعات عمل مخفضة</p>
                </div>
              </div>
              
              <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <p className="text-sm text-yellow-800">
                  <strong>ملاحظة:</strong> يتم الإعلان عن أي تغييرات في ساعات العمل مسبقاً على وسائل التواصل الاجتماعي
                </p>
              </div>
            </div>
          </div>
          
          {/* معلومات الاتصال السريع */}
          <div className="mt-8 bg-gradient-to-r from-blue-600 to-blue-800 rounded-lg p-6 text-white">
            <div className="text-center">
              <h3 className="text-2xl font-bold mb-4">هل تحتاج للمساعدة؟</h3>
              <p className="text-blue-100 mb-6">
                تواصل معنا في أي وقت للاستفسار عن المنتجات أو ساعات العمل
              </p>
              
              <div className="flex flex-wrap justify-center gap-4">
                <a
                  href={`tel:${settings.contactInfo.phone}`}
                  className="bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors"
                >
                  📞 اتصل بنا
                </a>
                
                <a
                  href={`https://wa.me/${settings.contactInfo.whatsapp?.replace(/[^0-9]/g, '')}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors"
                >
                  💬 واتساب
                </a>
                
                <a
                  href="/contact"
                  className="bg-gray-800 text-white px-6 py-3 rounded-lg font-semibold hover:bg-gray-900 transition-colors"
                >
                  📧 صفحة الاتصال
                </a>
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default WorkingHoursPage;
