# 📞 دليل صفحة "اتصل بنا" مع أوقات العمل والخريطة

## ✅ تم إضافة الميزات التالية:

### **1. 🕒 أوقات العمل في صفحة "اتصل بنا":**
- ✅ **عرض الحالة الحالية** (مفتوح/مغلق)
- ✅ **جدول أوقات العمل الأسبوعي**
- ✅ **تمييز اليوم الحالي**
- ✅ **رسالة توضيحية** إذا كانت غير مفعلة

### **2. 🗺️ خريطة جوجل للموقع:**
- ✅ **خريطة تفاعلية** مدمجة من جوجل
- ✅ **معلومات الموقع** التفصيلية
- ✅ **أزرار للتنقل** (جوجل مابس، خرائط آبل، واتساب)
- ✅ **تصميم متجاوب** لجميع الأجهزة

### **3. 🛠️ أداة إعداد أوقات العمل:**
- ✅ **إعداد تلقائي** لأوقات العمل
- ✅ **فحص الحالة الحالية**
- ✅ **تفعيل فوري**

---

## 🚀 كيفية الوصول والاستخدام:

### **الخطوة 1: إعداد أوقات العمل**

#### **استخدم أداة الإعداد:**
```
http://localhost:3000/setup-working-hours.html
```

#### **الخطوات:**
1. **افتح الأداة**
2. **اضغط "إعداد أوقات العمل"**
3. **ستتم إضافة الجدول التالي:**
   ```
   السبت:    08:00 ص - 08:00 م
   الأحد:    08:00 ص - 08:00 م
   الاثنين:  08:00 ص - 08:00 م
   الثلاثاء: 08:00 ص - 08:00 م
   الأربعاء: 08:00 ص - 08:00 م
   الخميس:  08:00 ص - 08:00 م
   الجمعة:   مغلق
   ```

### **الخطوة 2: مشاهدة صفحة "اتصل بنا"**

#### **افتح الصفحة:**
```
http://localhost:3000/contact
```

#### **ما ستجده:**
- 📞 **معلومات الاتصال** (هاتف، إيميل، عنوان، واتساب)
- 📱 **وسائل التواصل الاجتماعي** (فيسبوك، إنستغرام، تويتر، تلجرام)
- 📝 **نموذج إرسال رسالة**
- 🕒 **أوقات العمل** مع الحالة الحالية
- 🗺️ **خريطة الموقع** التفاعلية

---

## 🕒 قسم أوقات العمل:

### **الميزات المتاحة:**

#### **1. الحالة الحالية:**
- 🟢 **"مكتبة أنوار دارس مفتوحة الآن"** (إذا كان الوقت ضمن ساعات العمل)
- 🔴 **"نعتذر، المكتبة مغلقة حالياً"** (خارج ساعات العمل)
- 📅 **عرض اليوم الحالي** وأوقات العمل

#### **2. الجدول الأسبوعي:**
- 📋 **جميع أيام الأسبوع** مع أوقات العمل
- 🎯 **تمييز اليوم الحالي** بلون مختلف
- ✅ **أيقونات للأيام المفتوحة**
- ❌ **أيقونات للأيام المغلقة**

#### **3. معلومات إضافية:**
- 🌍 **المنطقة الزمنية:** Asia/Aden
- ⏰ **تنسيق الوقت:** 12 ساعة (AM/PM)
- 📝 **ملاحظة:** "الأوقات معروضة بتوقيت اليمن المحلي"

---

## 🗺️ قسم خريطة الموقع:

### **الميزات المتاحة:**

#### **1. معلومات الموقع:**
- 📍 **العنوان الكامل:** صنعاء، اليمن - شارع الزبيري
- ℹ️ **معلومات إضافية:**
  - بجوار المعالم الرئيسية في المنطقة
  - مواقف سيارات متاحة
  - سهولة الوصول بالمواصلات العامة
  - مناسب لذوي الاحتياجات الخاصة

#### **2. الخريطة التفاعلية:**
- 🗺️ **خريطة جوجل مدمجة** بحجم 400px
- 🔍 **إمكانية التكبير والتصغير**
- 📱 **متجاوبة** مع جميع أحجام الشاشات
- 🎯 **تحديد الموقع** بدقة

#### **3. أزرار التنقل:**
- 🔵 **فتح في جوجل مابس** - يفتح الموقع في تطبيق جوجل مابس
- ⚫ **فتح في خرائط آبل** - يفتح الموقع في خرائط آبل
- 🟢 **اسأل عن الموقع عبر واتساب** - يفتح محادثة واتساب مع رسالة جاهزة

#### **4. كيفية الوصول:**
- 📱 **تعليمات واضحة** لاستخدام تطبيقات الخرائط
- 🔍 **نصائح للبحث** عن "مكتبة أنوار دارس"
- 📍 **استخدام العنوان** للوصول بسهولة

---

## 🧪 اختبار الميزات:

### **اختبار أوقات العمل:**

#### **الخطوة 1: إعداد أوقات العمل**
```
1. افتح: http://localhost:3000/setup-working-hours.html
2. اضغط "إعداد أوقات العمل"
3. انتظر رسالة النجاح
```

#### **الخطوة 2: مشاهدة النتيجة**
```
1. افتح: http://localhost:3000/contact
2. انتقل لنهاية الصفحة
3. يجب أن ترى قسم "أوقات العمل"
4. تحقق من الحالة الحالية
```

#### **الخطوة 3: اختبار التحديث**
```
1. ادخل لوحة التحكم: http://localhost:3000/admin
2. اذهب لـ "الإعدادات" → "أوقات العمل"
3. غيّر أي إعدادات
4. احفظ وتحقق من التحديث في صفحة "اتصل بنا"
```

### **اختبار الخريطة:**

#### **الخطوة 1: مشاهدة الخريطة**
```
1. افتح: http://localhost:3000/contact
2. انتقل لقسم "موقعنا على الخريطة"
3. تحقق من ظهور الخريطة
```

#### **الخطوة 2: اختبار الأزرار**
```
1. اضغط "فتح في جوجل مابس"
2. يجب أن يفتح جوجل مابس في تبويب جديد
3. جرب "فتح في خرائط آبل"
4. جرب "اسأل عن الموقع عبر واتساب"
```

---

## 🔧 تخصيص الخريطة:

### **لتغيير موقع الخريطة:**

#### **الخطوة 1: الحصول على إحداثيات جديدة**
```
1. اذهب إلى: https://www.google.com/maps
2. ابحث عن الموقع المطلوب
3. انقر بالزر الأيمن على الموقع
4. اختر "ما هذا المكان؟"
5. انسخ الإحداثيات (مثال: 15.3694, 44.2066)
```

#### **الخطوة 2: تحديث الكود**
في ملف `src/app/contact/page.tsx`، ابحث عن:
```html
src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3840.1234567890123!2d44.2066!3d15.3694!..."
```

واستبدل الإحداثيات بالإحداثيات الجديدة.

### **لتخصيص العنوان:**
في نفس الملف، ابحث عن:
```html
<p className="text-gray-600">{settings.contactInfo.address}</p>
```

العنوان يأتي من إعدادات الاتصال في لوحة التحكم.

---

## 🎯 النتائج المتوقعة:

### **في صفحة "اتصل بنا" ستجد:**

#### **القسم الأول: معلومات الاتصال**
- 📞 الهاتف مع رابط للاتصال المباشر
- 📧 الإيميل مع رابط لإرسال رسالة
- 📍 العنوان
- 💬 واتساب مع رابط للمحادثة

#### **القسم الثاني: وسائل التواصل الاجتماعي**
- 🔵 فيسبوك
- 🟣 إنستغرام  
- ⚫ تويتر (X)
- 🔵 تلجرام
- 🟢 واتساب

#### **القسم الثالث: نموذج الاتصال**
- 📝 نموذج لإرسال رسالة مباشرة
- ✅ حقول مطلوبة وحقول اختيارية
- 📤 زر إرسال مع تأكيد

#### **القسم الرابع: أوقات العمل**
- 🕒 الحالة الحالية (مفتوح/مغلق)
- 📅 الجدول الأسبوعي
- 🎯 تمييز اليوم الحالي
- 🌍 المنطقة الزمنية

#### **القسم الخامس: خريطة الموقع**
- 🗺️ خريطة جوجل تفاعلية
- ℹ️ معلومات الموقع
- 🔗 أزرار التنقل
- 📱 تصميم متجاوب

---

## 🚨 إذا لم تظهر أوقات العمل:

### **الحل السريع:**
```
1. افتح: http://localhost:3000/setup-working-hours.html
2. اضغط "إعداد أوقات العمل"
3. افتح: http://localhost:3000/contact
4. أعد تحميل الصفحة (F5)
```

### **الحل البديل:**
```javascript
// في Console (F12)
const settings = JSON.parse(localStorage.getItem('admin-settings') || '{}');
settings.workingHours = {
  enabled: true,
  schedule: {
    saturday: { enabled: true, open: '08:00', close: '20:00' },
    sunday: { enabled: true, open: '08:00', close: '20:00' },
    monday: { enabled: true, open: '08:00', close: '20:00' },
    tuesday: { enabled: true, open: '08:00', close: '20:00' },
    wednesday: { enabled: true, open: '08:00', close: '20:00' },
    thursday: { enabled: true, open: '08:00', close: '20:00' },
    friday: { enabled: false, open: '14:00', close: '18:00' }
  },
  timezone: 'Asia/Aden',
  displayFormat: '12',
  closedMessage: 'نعتذر، المكتبة مغلقة حالياً',
  openMessage: 'المكتبة مفتوحة الآن'
};
localStorage.setItem('admin-settings', JSON.stringify(settings));
location.reload();
```

---

## 📞 الخطوات التالية:

### **1. إعداد أوقات العمل:**
```
http://localhost:3000/setup-working-hours.html
```

### **2. مشاهدة النتيجة:**
```
http://localhost:3000/contact
```

### **3. تخصيص الخريطة حسب موقعك الفعلي**

**الآن لديك صفحة "اتصل بنا" شاملة مع أوقات العمل والخريطة!** 🎉

**جرب الأدوات وأخبرني بالنتائج!** 🚀
