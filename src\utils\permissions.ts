import { Permission } from '@/types';

export const PERMISSIONS: Permission[] = [
  // Products permissions
  {
    id: 'products.view',
    name: 'عرض المنتجات',
    description: 'يمكن عرض قائمة المنتجات',
    category: 'products'
  },
  {
    id: 'products.create',
    name: 'إضافة منتجات',
    description: 'يمكن إضافة منتجات جديدة',
    category: 'products'
  },
  {
    id: 'products.edit',
    name: 'تعديل المنتجات',
    description: 'يمكن تعديل المنتجات الموجودة',
    category: 'products'
  },
  {
    id: 'products.delete',
    name: 'حذف المنتجات',
    description: 'يمكن حذف المنتجات',
    category: 'products'
  },

  // Orders permissions
  {
    id: 'orders.view',
    name: 'عرض الطلبات',
    description: 'يمكن عرض قائمة الطلبات',
    category: 'orders'
  },
  {
    id: 'orders.edit',
    name: 'تعديل الطلبات',
    description: 'يمكن تعديل حالة الطلبات',
    category: 'orders'
  },
  {
    id: 'orders.delete',
    name: 'حذف الطلبات',
    description: 'يمكن حذف الطلبات',
    category: 'orders'
  },

  // Users permissions
  {
    id: 'users.view',
    name: 'عرض المستخدمين',
    description: 'يمكن عرض قائمة المستخدمين الإداريين',
    category: 'users'
  },
  {
    id: 'users.create',
    name: 'إضافة مستخدمين',
    description: 'يمكن إضافة مستخدمين إداريين جدد',
    category: 'users'
  },
  {
    id: 'users.edit',
    name: 'تعديل المستخدمين',
    description: 'يمكن تعديل بيانات المستخدمين الإداريين',
    category: 'users'
  },
  {
    id: 'users.delete',
    name: 'حذف المستخدمين',
    description: 'يمكن حذف المستخدمين الإداريين',
    category: 'users'
  },

  // Settings permissions
  {
    id: 'settings.view',
    name: 'عرض الإعدادات',
    description: 'يمكن عرض إعدادات المتجر',
    category: 'settings'
  },
  {
    id: 'settings.edit',
    name: 'تعديل الإعدادات',
    description: 'يمكن تعديل إعدادات المتجر',
    category: 'settings'
  },

  // Reports permissions
  {
    id: 'reports.view',
    name: 'عرض التقارير',
    description: 'يمكن عرض التقارير والإحصائيات',
    category: 'reports'
  },
  {
    id: 'reports.export',
    name: 'تصدير التقارير',
    description: 'يمكن تصدير التقارير',
    category: 'reports'
  }
];

export const ROLE_PERMISSIONS = {
  super_admin: PERMISSIONS.map(p => p.id), // All permissions
  admin: [
    'products.view', 'products.create', 'products.edit', 'products.delete',
    'orders.view', 'orders.edit',
    'settings.view', 'settings.edit',
    'reports.view', 'reports.export'
  ],
  manager: [
    'products.view', 'products.create', 'products.edit',
    'orders.view', 'orders.edit',
    'reports.view'
  ],
  editor: [
    'products.view', 'products.create', 'products.edit',
    'orders.view'
  ]
};

export const hasPermission = (userPermissions: string[], requiredPermission: string): boolean => {
  return userPermissions.includes(requiredPermission);
};

export const hasAnyPermission = (userPermissions: string[], requiredPermissions: string[]): boolean => {
  return requiredPermissions.some(permission => userPermissions.includes(permission));
};

export const getPermissionsByCategory = (category: string): Permission[] => {
  return PERMISSIONS.filter(p => p.category === category);
};

export const getRoleDisplayName = (role: string): string => {
  const roleNames = {
    super_admin: 'مدير عام',
    admin: 'مدير',
    manager: 'مشرف',
    editor: 'محرر'
  };
  return roleNames[role as keyof typeof roleNames] || role;
};
