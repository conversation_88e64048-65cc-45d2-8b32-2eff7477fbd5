# مكتبة أنوار دارس 📚

موقع مكتبة إلكترونية متكامل وعصري مصمم خصيصاً للكتب والقرطاسية، يدعم جميع الأجهزة والشاشات مع تصميم متجاوب وجذاب.

## ✨ المميزات الرئيسية

### 🛍️ للمتسوقين
- **تسوق بدون تسجيل**: يمكن للعملاء الشراء دون إنشاء حساب
- **تصميم متجاوب**: يعمل بسلاسة على الجوالات والتابلت والكمبيوتر
- **واجهة عربية**: تصميم يدعم اللغة العربية بالكامل
- **طرق دفع متعددة**: تحويل بنكي أو نقداً عند الاستلام
- **سلة تسوق ذكية**: حفظ المنتجات حتى بعد إغلاق المتصفح

### 🏪 الأقسام الرئيسية
1. **الكتب والملازم** 📚
   - كتب دراسية
   - ملازم تعليمية
   - كتب خارج المنهج

2. **القرطاسية** ✏️
   - أدوات كتابية
   - أدوات مدرسية
   - أدوات فنية

3. **الإلكترونيات** 🔌
   - سماعات
   - شواحن
   - أجهزة ذكية

4. **الطباعة والتصوير** 🖨️
   - طباعة المستندات
   - تصوير الملازم
   - طباعة الصور

### 🎛️ لوحة التحكم الإدارية
- **إدارة المنتجات**: إضافة وتعديل وحذف المنتجات
- **إدارة الطلبات**: متابعة وتحديث حالة الطلبات
- **إدارة الفئات**: تنظيم المنتجات في فئات
- **الإحصائيات**: عرض إحصائيات المبيعات والطلبات
- **الإعدادات**: تخصيص إعدادات المتجر

## 🚀 التقنيات المستخدمة

- **Next.js 14**: إطار عمل React الحديث
- **TypeScript**: للكتابة الآمنة والمنظمة
- **Tailwind CSS**: للتصميم السريع والمتجاوب
- **Lucide React**: أيقونات عصرية وجميلة
- **Context API**: لإدارة الحالة العامة

## 📦 التثبيت والتشغيل

### المتطلبات
- Node.js (الإصدار 18 أو أحدث)
- npm أو yarn

### خطوات التثبيت

1. **تحميل Node.js**:
   - اذهب إلى [nodejs.org](https://nodejs.org)
   - حمل وثبت أحدث إصدار LTS

2. **تثبيت المكتبات**:
   ```bash
   npm install
   ```

3. **تشغيل المشروع**:
   ```bash
   npm run dev
   ```

4. **فتح الموقع**:
   افتح المتصفح واذهب إلى: `http://localhost:3000`

## 🔐 الوصول للوحة التحكم

- **الرابط**: `/admin`
- **اسم المستخدم**: `admin`
- **كلمة المرور الافتراضية**: `admin77111`

## 📱 الصفحات المتاحة

### صفحات العملاء
- `/` - الصفحة الرئيسية
- `/products` - جميع المنتجات
- `/category/[id]` - منتجات فئة معينة
- `/product/[id]` - تفاصيل المنتج
- `/cart` - سلة التسوق
- `/checkout` - إتمام الطلب
- `/order-success` - تأكيد الطلب
- `/contact` - اتصل بنا

### صفحات الإدارة
- `/admin` - لوحة التحكم الرئيسية
- `/admin/products` - إدارة المنتجات
- `/admin/orders` - إدارة الطلبات
- `/admin/settings` - إعدادات المتجر

## 🎨 التخصيص

### الألوان
يمكنك تغيير ألوان الموقع من ملف `tailwind.config.js`:
```javascript
colors: {
  primary: {
    // ألوان أساسية
  },
  secondary: {
    // ألوان ثانوية
  }
}
```

### المحتوى
- **المنتجات**: يتم إدارتها من لوحة التحكم
- **الفئات**: قابلة للتخصيص من الكود
- **معلومات الاتصال**: في ملف `AdminContext.tsx`

## 🛠️ الميزات المتقدمة

### إدارة السلة
- حفظ تلقائي في localStorage
- تحديث فوري للكميات
- حساب التكلفة الإجمالية

### نظام الطلبات
- معلومات العميل (الاسم والهاتف إجباري)
- عنوان التوصيل
- اختيار طريقة الدفع
- تتبع حالة الطلب

### التصميم المتجاوب
- يعمل على جميع أحجام الشاشات
- قوائم تنقل مخصصة للجوال
- تحسين تجربة اللمس

## 📞 معلومات الاتصال الافتراضية

- **الهاتف**: +967-1-234567
- **البريد الإلكتروني**: <EMAIL>
- **العنوان**: صنعاء، اليمن
- **واتساب**: +967712345678

## 🔄 التحديثات المستقبلية

- [ ] نظام المراجعات والتقييمات
- [ ] قائمة الأمنيات
- [ ] نظام الخصومات والكوبونات
- [ ] تطبيق جوال
- [ ] دفع إلكتروني
- [ ] نظام الإشعارات
- [ ] تقارير مفصلة
- [ ] دعم متعدد اللغات

## 📄 الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير.

## 🤝 المساهمة

نرحب بالمساهمات! يرجى إنشاء Pull Request أو فتح Issue للاقتراحات والتحسينات.

---

**تم تطوير هذا المشروع خصيصاً للسوق اليمني مع مراعاة الاحتياجات المحلية وطرق الدفع المتاحة.**
