'use client';

import { useEffect } from 'react';
import { useAdmin } from '@/contexts/AdminContext';

export const useTheme = () => {
  const { settings } = useAdmin();

  useEffect(() => {
    // Apply theme colors to CSS variables
    const root = document.documentElement;
    
    if (settings.theme) {
      // Convert hex to RGB for Tailwind CSS variables
      const hexToRgb = (hex: string) => {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
          r: parseInt(result[1], 16),
          g: parseInt(result[2], 16),
          b: parseInt(result[3], 16)
        } : null;
      };

      const primaryRgb = hexToRgb(settings.theme.primaryColor);
      const secondaryRgb = hexToRgb(settings.theme.secondaryColor);
      const accentRgb = hexToRgb(settings.theme.accentColor);

      if (primaryRgb) {
        root.style.setProperty('--color-primary-50', `rgb(${Math.min(255, primaryRgb.r + 50)}, ${Math.min(255, primaryRgb.g + 50)}, ${Math.min(255, primaryRgb.b + 50)})`);
        root.style.setProperty('--color-primary-100', `rgb(${Math.min(255, primaryRgb.r + 40)}, ${Math.min(255, primaryRgb.g + 40)}, ${Math.min(255, primaryRgb.b + 40)})`);
        root.style.setProperty('--color-primary-200', `rgb(${Math.min(255, primaryRgb.r + 30)}, ${Math.min(255, primaryRgb.g + 30)}, ${Math.min(255, primaryRgb.b + 30)})`);
        root.style.setProperty('--color-primary-300', `rgb(${Math.min(255, primaryRgb.r + 20)}, ${Math.min(255, primaryRgb.g + 20)}, ${Math.min(255, primaryRgb.b + 20)})`);
        root.style.setProperty('--color-primary-400', `rgb(${Math.min(255, primaryRgb.r + 10)}, ${Math.min(255, primaryRgb.g + 10)}, ${Math.min(255, primaryRgb.b + 10)})`);
        root.style.setProperty('--color-primary-500', `rgb(${primaryRgb.r}, ${primaryRgb.g}, ${primaryRgb.b})`);
        root.style.setProperty('--color-primary-600', `rgb(${Math.max(0, primaryRgb.r - 10)}, ${Math.max(0, primaryRgb.g - 10)}, ${Math.max(0, primaryRgb.b - 10)})`);
        root.style.setProperty('--color-primary-700', `rgb(${Math.max(0, primaryRgb.r - 20)}, ${Math.max(0, primaryRgb.g - 20)}, ${Math.max(0, primaryRgb.b - 20)})`);
        root.style.setProperty('--color-primary-800', `rgb(${Math.max(0, primaryRgb.r - 30)}, ${Math.max(0, primaryRgb.g - 30)}, ${Math.max(0, primaryRgb.b - 30)})`);
        root.style.setProperty('--color-primary-900', `rgb(${Math.max(0, primaryRgb.r - 40)}, ${Math.max(0, primaryRgb.g - 40)}, ${Math.max(0, primaryRgb.b - 40)})`);
      }

      if (secondaryRgb) {
        root.style.setProperty('--color-secondary-50', `rgb(${Math.min(255, secondaryRgb.r + 50)}, ${Math.min(255, secondaryRgb.g + 50)}, ${Math.min(255, secondaryRgb.b + 50)})`);
        root.style.setProperty('--color-secondary-100', `rgb(${Math.min(255, secondaryRgb.r + 40)}, ${Math.min(255, secondaryRgb.g + 40)}, ${Math.min(255, secondaryRgb.b + 40)})`);
        root.style.setProperty('--color-secondary-200', `rgb(${Math.min(255, secondaryRgb.r + 30)}, ${Math.min(255, secondaryRgb.g + 30)}, ${Math.min(255, secondaryRgb.b + 30)})`);
        root.style.setProperty('--color-secondary-300', `rgb(${Math.min(255, secondaryRgb.r + 20)}, ${Math.min(255, secondaryRgb.g + 20)}, ${Math.min(255, secondaryRgb.b + 20)})`);
        root.style.setProperty('--color-secondary-400', `rgb(${Math.min(255, secondaryRgb.r + 10)}, ${Math.min(255, secondaryRgb.g + 10)}, ${Math.min(255, secondaryRgb.b + 10)})`);
        root.style.setProperty('--color-secondary-500', `rgb(${secondaryRgb.r}, ${secondaryRgb.g}, ${secondaryRgb.b})`);
        root.style.setProperty('--color-secondary-600', `rgb(${Math.max(0, secondaryRgb.r - 10)}, ${Math.max(0, secondaryRgb.g - 10)}, ${Math.max(0, secondaryRgb.b - 10)})`);
        root.style.setProperty('--color-secondary-700', `rgb(${Math.max(0, secondaryRgb.r - 20)}, ${Math.max(0, secondaryRgb.g - 20)}, ${Math.max(0, secondaryRgb.b - 20)})`);
        root.style.setProperty('--color-secondary-800', `rgb(${Math.max(0, secondaryRgb.r - 30)}, ${Math.max(0, secondaryRgb.g - 30)}, ${Math.max(0, secondaryRgb.b - 30)})`);
        root.style.setProperty('--color-secondary-900', `rgb(${Math.max(0, secondaryRgb.r - 40)}, ${Math.max(0, secondaryRgb.g - 40)}, ${Math.max(0, secondaryRgb.b - 40)})`);
      }

      if (accentRgb) {
        root.style.setProperty('--color-accent-50', `rgb(${Math.min(255, accentRgb.r + 50)}, ${Math.min(255, accentRgb.g + 50)}, ${Math.min(255, accentRgb.b + 50)})`);
        root.style.setProperty('--color-accent-100', `rgb(${Math.min(255, accentRgb.r + 40)}, ${Math.min(255, accentRgb.g + 40)}, ${Math.min(255, accentRgb.b + 40)})`);
        root.style.setProperty('--color-accent-200', `rgb(${Math.min(255, accentRgb.r + 30)}, ${Math.min(255, accentRgb.g + 30)}, ${Math.min(255, accentRgb.b + 30)})`);
        root.style.setProperty('--color-accent-300', `rgb(${Math.min(255, accentRgb.r + 20)}, ${Math.min(255, accentRgb.g + 20)}, ${Math.min(255, accentRgb.b + 20)})`);
        root.style.setProperty('--color-accent-400', `rgb(${Math.min(255, accentRgb.r + 10)}, ${Math.min(255, accentRgb.g + 10)}, ${Math.min(255, accentRgb.b + 10)})`);
        root.style.setProperty('--color-accent-500', `rgb(${accentRgb.r}, ${accentRgb.g}, ${accentRgb.b})`);
        root.style.setProperty('--color-accent-600', `rgb(${Math.max(0, accentRgb.r - 10)}, ${Math.max(0, accentRgb.g - 10)}, ${Math.max(0, accentRgb.b - 10)})`);
        root.style.setProperty('--color-accent-700', `rgb(${Math.max(0, accentRgb.r - 20)}, ${Math.max(0, accentRgb.g - 20)}, ${Math.max(0, accentRgb.b - 20)})`);
        root.style.setProperty('--color-accent-800', `rgb(${Math.max(0, accentRgb.r - 30)}, ${Math.max(0, accentRgb.g - 30)}, ${Math.max(0, accentRgb.b - 30)})`);
        root.style.setProperty('--color-accent-900', `rgb(${Math.max(0, accentRgb.r - 40)}, ${Math.max(0, accentRgb.g - 40)}, ${Math.max(0, accentRgb.b - 40)})`);
      }
    }
  }, [settings.theme]);

  return {
    primaryColor: settings.theme?.primaryColor || '#2563eb',
    secondaryColor: settings.theme?.secondaryColor || '#64748b',
    accentColor: settings.theme?.accentColor || '#d946ef'
  };
};
